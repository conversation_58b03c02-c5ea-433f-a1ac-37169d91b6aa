import encrypt from '~/utils/encrypt'
import utilWhiteList from '~/utils/whiteList.js'
const STATION = process.env.STATION
const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
const loadConfig = require(`~/station/${STATION}/${NUXT_ENV}.js`).default

export default class Game {
  constructor(axios) {
    this.axios = axios
  }

  async gameSortList({
    headerData,
    gameCategoryId,
    sortType,
    lang,
    limit,
    stationName,
    offset,
    keyword,
    providerId
  }) {
    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }
    const body = {
      sort_type: sortType ? sortType : 1,
      lang: lang,
      limit: limit
    }

    if (gameCategoryId) {
      body.categoryType = gameCategoryId
    }
    if (stationName) {
      body.stationName = stationName
    }
    if (offset) {
      body.offset = offset
    }
    if (keyword) {
      body.keyword = keyword
    }
    if (providerId) {
      body.platform_id = providerId
    }

    let path = ''
    if (STATION === 'qc_domestic' || STATION === 'qc_overseas') {
      path = '/api/qc/game/sort_list'
    } else {
      path = '/api/client/game/sort_list'
    }

    return await this.axios.$get(path, {
      headers: head,
      params: body
    })
  }

  async gameList(headerData, gameList, lang, client_id) {
    //取得遊戲列表：GET /api/client/game/list => 若未帶遊戲 ID，則預設回傳全部遊戲資料

    //請求參數
    //'game_ids' => [ 'array' ],
    //'game_ids.*' => [ 'integer' ],
    //'lang' => [ 'string' ],
    //'is_station_game_id' => [ 'boolean' ], //預設為false，若為true，則會回傳營運平台遊戲ID，如 10297（華義站）
    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }
    const body = {
      client_id: client_id,
      game_ids: gameList,
      lang: lang
    }

    return this.axios.$get('/api/client/game/list', {
      headers: head,
      params: body,
      timeout: 2000
    })
  }

  gameCategoryList({ stationName } = {}) {
    let body = {}
    if (stationName) {
      body.stationName = stationName
    }
    let path = ''
    if (STATION === 'qc_domestic' || STATION === 'qc_overseas') {
      path = '/api/qc/platform/category/list'
    } else {
      path = '/api/client/platform/category/list'
    }
    return this.axios.$get(path, {
      params: body
    })
  }

  async gameLinkDemo({ headerData, gameId, mobile, lang, backUrl, stationName }) {
    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }
    const body = {
      game_id: gameId,
      mobile: mobile ? 1 : 0, // 因為是GET 所以要轉成1或0
      lang: lang,
      nocache: 1
    }
    if (stationName) {
      body.stationName = stationName
    }
    if (backUrl) {
      body.back_url = backUrl
    }

    return this.axios.$get('/api/client/game/link/demo', { headers: head, params: body })
  }

  async gameLink({
    headerData,
    xinkey,
    gameId,
    username,
    mobile,
    lang,
    ip,
    backUrl,
    userAgent,
    thumbUrl,
    userLevel,
    vipLevel,
    stationName
  }) {
    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      xinkey: xinkey,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }
    const body = {
      xinkey,
      game_id: gameId,
      username,
      mobile: mobile ? 1 : 0, // 因為是GET 所以要轉成1或0
      lang: lang,
      userAgent,
      thumb_url: thumbUrl
    }
    if (backUrl) {
      body.back_url = backUrl
    }
    if (userLevel) {
      body.user_level = userLevel
    }
    if (vipLevel) {
      body.vip_level = vipLevel
    }
    if (ip) {
      body.ip = ip
    }
    if (stationName) {
      body.stationName = stationName
    }

    return this.axios.$post('/api/client/game/link', body, { headers: head })
  }

  async gameClick({ headerData, gameId, isDemo, lang, username, xinkey, stationName = null }) {
    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      xinkey: xinkey,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }
    const body = { gameId, is_demo: isDemo ? 1 : 0, lang }
    if (username) {
      body.username = username
    }
    if (xinkey) {
      body.xinkey = xinkey
    }
    if (stationName) {
      body.stationName = stationName
    }
    return this.axios.$post('/api/client/game/click', body, { headers: head })
  }

  gameSearchAnalytics({ content, username, gameIds, at }) {
    const body = {
      content,
      gameIds,
      at
    }

    if (username) {
      body.username = username
    }

    return this.axios.$post('/api/client/game/search_record', body)
  }

  gameRTPList(gameIds, stationName = null) {
    const body = {
      gameIds
    }
    if (stationName) {
      body.stationName = stationName
    }
    return this.axios.$get('/api/client/game/rtp_list', {
      params: body,
      timeout: 2000
    })
  }

  async gamePlayed(username) {
    const body = {
      username
    }
    return this.axios.$post('/api/client/game/recent_play/list', body)
  }

  async gameUnCategorizedSortList({ headerData, sortType, lang, isOnlyId, limit, offset }) {
    const body = {
      sort_type: sortType ? sortType : 1,
      lang: lang,
      OnlyId: isOnlyId,
      limit: limit,
      offset: offset
    }

    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }

    const gameData = await this.axios.$get('/api/client/game/unclassified/sort_list', {
      headers: head,
      params: body
    })
    gameData.list = utilWhiteList.filterGames(gameData.list, window.location.origin)
    gameData.count = gameData.list.length
    return gameData
  }

  async getInCompleteGameList(username) {
    const body = {
      username
    }
    return this.axios.$get('/api/client/game/incomplete_bet/list', {
      params: body
    })
  }
}
