export default class Leaderboard {
  constructor(axios) {
    this.axios = axios
  }
  // 日榜: 查詢7天內的資料, 週榜: 查詢4週內的資料
  // type(0: 日榜, 1:周榜)
  async getWinRanking({ type, beginAt, endAt, limit, offset }) {
    const params = {
      type,
      beginAt,
      endAt,
      limit,
      offset
    }
    return await this.axios.get('/api/client/rank/win/list', { params })
  }

  async getOddsRanking({ type, beginAt, endAt, limit, offset }) {
    const params = {
      type,
      beginAt,
      endAt,
      limit,
      offset
    }
    return await this.axios.get('/api/client/rank/odds/list', { params })
  }

  //個人排名數據
  async getWinUserRanking({ type, username, beginAt, endAt }) {
    const params = {
      type,
      username,
      beginAt,
      endAt
    }
    return await this.axios.get('/api/client/rank/win/user', { params })
  }

  async getOddsUserRanking({ type, username, beginAt, endAt }) {
    const params = {
      type,
      username,
      beginAt,
      endAt
    }
    return await this.axios.get('/api/client/rank/odds/user', { params })
  }
}
