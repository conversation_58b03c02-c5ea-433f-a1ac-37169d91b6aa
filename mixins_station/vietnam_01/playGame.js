import playGameTaiwan from '~/mixins/game/playGameTaiwan'

export default {
  mixins: [playGameTaiwan],
  methods: {
    checkYears18Noty(fn) {
      const wbesiteMaintainOrError = this.status404 || this.maintainSystem[0].maintaining

      // 初始化 18 禁提示的 localStorage
      if (!this.$localStorage.get('years18Noty')) {
        this.$localStorage.set('years18Noty', { expired: '1950-01-01' })
      }

      // 獲取已顯示過的提示清單
      const shownNoty = this.$localStorage.get('shownNoty') || []
      const shownYears18Noty = shownNoty.includes('years18Noty')

      // 判斷是否顯示 18 禁提示
      let showYears18Noty = false
      const years18Noty = this.$localStorage.get('years18Noty')
      const targetDate = this.$moment(years18Noty.expired, 'YYYY-MM-DD')

      // 檢查是否今天已顯示過提示，或是第一次訪問
      const isToday = this.$moment().isSame(targetDate, 'day')
      const isFirst = years18Noty.expired === '1950-01-01'
      showYears18Noty = isFirst || !isToday

      if (!wbesiteMaintainOrError && !shownYears18Noty && showYears18Noty) {
        // 彈出 18 禁提示後，執行接續行為、儲存到"已顯示彈窗"陣列中
        this.$nuxt.$emit('root:showYears18NotyStatus', {
          show: true,
          onConfirmNotify: fn
        })
        shownNoty.push('years18Noty')
        this.$localStorage.set('shownNoty', shownNoty)
      } else if (fn) {
        fn()
      }
    }
  }
}
