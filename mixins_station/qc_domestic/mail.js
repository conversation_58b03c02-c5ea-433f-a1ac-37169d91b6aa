import mailBase from '~/mixins/mail/mailTaiwan'
import guildMgr from '~/mixins/guildMgr'
const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

export default {
  mixins: [mailBase, guildMgr],
  data() {
    return {
      itemInfo: [],
      displayOrder: [],
      defaultImgWebp: require('~/assets/image/inventory_card_default.webp'),
      defaultImgPng: require('~/assets/image/inventory_card_default.png'),
      receiveableItems: []
    }
  },
  watch: {
    getSingleMail: {
      async handler() {
        if (this.itemInfo.length === 0) await this.getItemInfo()
        this.getReceiveableItems()
      },
      immediate: true
    }
  },
  methods: {
    async doShowMailContent(mailId) {
      // show mail content page for mobile
      if (this.$vuetify.breakpoint.xsOnly) {
        this.mailListMobileScrollTop = document.getElementById('mail-list-mobile').scrollTop
        this.mobile.showMailContentstatus = true
      }
      const index = this.findIndexByMailId(mailId)
      if (index !== -1) {
        const element = this.showMailDataArrTmp[index]
        // 有附件且非星幣類型的信件，不做已讀
        if (
          (this.checkMailCanOpen(element) && (element.canReceive || element.itemType > 1)) ||
          // 通知類型信件但有附件，不自動做已讀
          (element.itemType === 0 && element.items.length !== 0)
        ) {
          const isExpire = this.$moment()
            .subtract(this.serverTimestamp, 'milliseconds')
            .isAfter(element.expire)
          if (!element.isRead && (element.itemType === 0 || isExpire)) {
            const res = await this.readMail(element.mailId)
            // 過期的信件與伺服器回傳的資訊一併做已讀
            if ((!element.isRead && isExpire) || (res.isRead && !isExpire)) {
              this.delReadBtnStatus = true
              this.$store.commit('mail/READ_SINGLE_MAIL', index)
            }
            // 經討論後，已逾時或無附件的信件不再顯示伺服器所傳的錯誤訊息 2023/03/22 by cash
          }
        }
        //後續動作會去更改到getSingleMail的內容，所以要先deep copy一份 以免使用非常規操作去改到vuex的內容
        this.getSingleMail = { ...element }
      }
      // 重新計算未讀信件數量
      this.updateNotyCount()
      // 重置信件滾動位置
      this.$nextTick(() => {
        if (this.$refs.mailContent) {
          this.$refs.mailContent.scrollTop = 0
        }
      })

      // 延遲檢查容器和滑塊的尺寸，如滑塊尺寸大於容器尺寸，則代表顯示滾動條，改變hasScrollbar
      setTimeout(() => {
        const swiperContainer = document.querySelector('.swiper-area')
        if (swiperContainer) {
          let slidesWidth = 0
          const slides = swiperContainer.querySelectorAll('.swiper-slide')
          slides.forEach((slide) => {
            slidesWidth += slide.offsetWidth + 8 // 8 是 spaceBetween
          })

          const containerWidth = swiperContainer.offsetWidth
          this.hasScrollbar = slidesWidth > containerWidth
        }
      }, 100) // 給足夠的時間讓內容渲染完成
    },
    async doReceive() {
      const index = this.findIndexByMailId(this.getSingleMail.mailId)
      if (index !== -1) {
        const element = this.showMailDataArrTmp[index]
        // 尚未已讀且內容為可領取附件類型的信件，才做領取附件(已讀)
        // 各站可領取附件請看各站點ui.config
        if (!element.isRead && element.canReceive) {
          const res = await this.readMail(element.mailId)
          // 全站統一為顯示領取成功/失敗
          if (res.isRead) {
            this.$notify.success(this.$t('mail_collect_success'))
            // 更新個人徽章數量
            await this.updatePersonalBadge()
          } else {
            this.$notify.error(this.$t('mail_collect_fail'))
          }
          //已讀信件
          this.$store.commit('mail/READ_SINGLE_MAIL', index)
          // 檢查按鈕狀態
          this.checkTopBtn(this.showMailDataArrTmp)
          // 改變按鈕的狀態
          this.delReadBtnStatus = true
          this.getSingleMail.isRead = true
          //重新計算未讀信件數量
          this.updateNotyCount()
          // 更新信件顯示的到期日
          this.getSingleMail = { ...element }
        }
      }
    },
    async doReceiveAll() {
      const readIds = []
      for (let index = 0; index < this.showMailDataArrTmp.length; index++) {
        const item = this.showMailDataArrTmp[index]

        if (!item.isRead && item.itemType > 0 && item.canReceive) {
          readIds.push(item.mailId)
        }
      }

      if (readIds.length > 0) {
        this.$wsClient.send(this.$wsPacketFactory.readAllMail(readIds))
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.READ)
        })
        //通知且更新未讀信件數量
        const hasReadMail = res.mailIds && res.mailIds.length > 0
        if (hasReadMail) {
          //依照伺服器回傳的已讀信件，後續伺服器會在回傳更新金幣數量，故不需再計算金幣
          res.mailIds.forEach((id) => {
            const index = this.findIndexByMailId(id)
            if (index !== -1) {
              this.$store.commit('mail/READ_SINGLE_MAIL', index)
            }
            //因為getSingleMail的內容是deep copy，所以需要額外更新
            if (id == this.getSingleMail.mailId) this.getSingleMail.isRead = true
          })
        }

        if (hasReadMail) {
          if (hasReadMail) this.$notify.success(this.$t('receive_all') + this.$t('success'))
          this.$store.commit(
            'mail/SET_NOTY_COUNT',
            this.showMailDataArrTmp.filter((val) => val.isRead === false).length
          )
          //按鈕狀態更新
          this.allReceiveBtnStatus = false
          this.delReadBtnStatus = true
        }
      }
      // 只在有選擇信件的情況下，才更新信件顯示的到期日
      if (this.selection) {
        const mailObj = this.showMailDataArrTmp.find((element) => element.mailId === this.selection)
        if (mailObj) {
          this.getSingleMail = { ...mailObj }
        }
      }
      // 更新個人徽章數量
      await this.updatePersonalBadge()
    },
    getReceiveableItems() {
      const items = []
      // 處理 mail 物件的函數
      const getMailObject = (info) => {
        const defaultMail = {
          name: '',
          img: '',
          imgSrc: this.defaultImgWebp,
          canReceive: false
        }
        return info?.mail ? { ...info.mail } : defaultMail
      }

      // 收集 itemType 內容
      if (
        this.getSingleMail.itemType > 0 &&
        this.getSingleMail.count &&
        this.getSingleMail.count > 0
      ) {
        const itemTypeInfo = this.itemInfo.find(
          (item) => item.id === this.getSingleMail.itemType.toString()
        )
        items.push({
          id: this.getSingleMail.itemType,
          count: this.getSingleMail.count,
          isItemType: true,
          uniqueKey: `${this.getSingleMail.itemType}_${this.getSingleMail.mailId}_itemType`, // 避免出現同樣的索引值進v-for迴圈
          ...itemTypeInfo, // 展開 itemTypeInfo 的屬性
          mail: getMailObject(itemTypeInfo) // 使用新的 mail 物件
        })
      }

      // 收集 items 陣列內的內容
      if (this.getSingleMail.items?.length > 0) {
        this.getSingleMail.items.forEach((item, index) => {
          if (item.count && item.count > 0) {
            const itemInfo = this.itemInfo.find((info) => info.id === item.id.toString())
            items.push({
              id: item.id,
              count: item.count,
              isItemType: false,
              uniqueKey: `${item.id}_${this.getSingleMail.mailId}_${index}`, // 避免出現同樣的索引值進v-for迴圈
              ...itemInfo, // 展開 itemTypeInfo 的屬性
              mail: getMailObject(itemInfo) // 使用新的 mail 物件
            })
          }
        })
      }

      items.sort((a, b) => {
        const aIndex = this.displayOrder.indexOf(a.id)
        const bIndex = this.displayOrder.indexOf(b.id)

        if (aIndex === bIndex) {
          // 相同 ID 時，itemType 的項目優先顯示
          if (a.isItemType && !b.isItemType) return -1
          if (!a.isItemType && b.isItemType) return 1
          // 如果都來自同一個來源，則按照原始順序
          return a.sortIndex - b.sortIndex
        }

        return aIndex - bIndex
      })

      this.receiveableItems = items
    },
    async getItemInfo() {
      try {
        const resInfo = await this.$axios.get(
          process.env.IMAGE_URL +
            `/inventory_items/item_provider/${loadConfig.client_id}/item_provider.json?` +
            Math.random()
        )
        this.itemInfo = resInfo.data
      } catch (error) {
        console.warn('Failed to get item info：', error)
        return
      }

      // 篩選出 canReceive 為 true 的項目，並新增 imgSrc
      this.itemInfo = this.itemInfo
        .filter((item) => item.mail.canReceive)
        .map((item) => ({
          ...item,
          mail: {
            ...item.mail,
            imgSrc: item.mail.img
              ? `${process.env.IMAGE_URL}/inventory_items/item_img/${loadConfig.client_id}/${
                  item.mail.img
                }?t=${Date.now()}`
              : this.defaultImgWebp
          }
        }))

      // 定義顯示順序：按照 JSON 排序控制，只取 id
      this.displayOrder = this.itemInfo.map((item) => item.id)
    },
    errorImgHandler(errorItem) {
      const originImgSrc = this.itemInfo.find((item) => item.id === errorItem.id).mail.imgSrc

      // 情況 1：載入預設圖（webp）失敗，改為預設圖（png）
      if (errorItem.mail.imgSrc === this.defaultImgWebp) {
        this.$set(errorItem.mail, 'imgSrc', this.defaultImgPng)
        return
      }

      // 情況 2：載入原始圖片（webp，非預設圖）失敗，改為 png
      if (errorItem.mail.imgSrc === originImgSrc) {
        // 嘗試將圖片格式從 .webp 替換為 .png
        this.$set(errorItem.mail, 'imgSrc', errorItem.mail.imgSrc.replace('.webp', '.png'))
        return
      }

      // 情況 3：非預設圖（webp 及 png），改為預設圖（webp）
      if (
        errorItem.mail.imgSrc !== this.defaultImgWebp &&
        errorItem.mail.imgSrc !== this.defaultImgPng
      ) {
        this.$set(errorItem.mail, 'imgSrc', this.defaultImgWebp)
        return
      }
    }
  }
}
