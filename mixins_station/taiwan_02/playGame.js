const STATION = process.env.STATION
const loginMixin = require(`@/mixins_station/${STATION}/login`).default
import playGameTaiwan from '~/mixins/game/playGameTaiwan'

export default {
  mixins: [loginMixin, playGameTaiwan],
  computed: {
    status404({ $store }) {
      return $store.getters['maintain/status404']
    }
  },
  methods: {
    async checkYears18Noty(fn) {
      const wbesiteMaintainOrError = this.status404 || this.maintainSystem[0].maintaining
      const loginAuthData = await this.getloginAuthData()

      // 初始化 18 禁提示的 localStorage
      if (!this.$localStorage.get('years18Noty')) {
        this.$localStorage.set('years18Noty', { expired: '1950-01-01' })
      }

      // 獲取已顯示過的提示清單
      const shownNoty = this.$localStorage.get('shownNoty') || []
      const shownYears18Noty = shownNoty.includes('years18Noty')

      // 判斷是否顯示 18 禁提示
      let showYears18Noty = false

      if (!this.loginAuthIsNotSet(this.$route.query.loginAuth)) {
        // 有 loginAuth 參數的情況
        if (loginAuthData?.IsOver18YearsOld) {
          // 使用者已年滿 18 歲
          await this.wsInit()
          await this.autologinHandler(this.$route.query.loginAuth)
        } else {
          // 使用者未滿 18 歲或未獲取到年齡資訊
          showYears18Noty = true
        }
      } else {
        // 沒有 loginAuth 參數的情況
        const years18Noty = this.$localStorage.get('years18Noty')
        const targetDate = this.$moment(years18Noty.expired, 'YYYY-MM-DD')

        // 檢查是否今天已顯示過提示，或是第一次訪問
        const isToday = this.$moment().isSame(targetDate, 'day')
        const isFirst = years18Noty.expired === '1950-01-01'
        showYears18Noty = isFirst || !isToday
      }

      if (!wbesiteMaintainOrError && !shownYears18Noty && showYears18Noty) {
        this.$nuxt.$emit('root:showYears18NotyStatus', {
          show: true,
          onConfirmNotify: fn
        })
        shownNoty.push('years18Noty')
        this.$localStorage.set('shownNoty', shownNoty)
      } else if (fn) {
        fn()
      }
    },
    async getloginAuthData() {
      const loginAuthXinKey = this.$route.query.loginAuth
      if (this.loginAuthIsNotSet(loginAuthXinKey)) return undefined
      const slotIdentity = await this.$waninNorthApi.user.getSlotIdentity({
        XinKey: loginAuthXinKey
      })
      return slotIdentity.status !== 200 ? undefined : slotIdentity.data
    },
    loginAuthIsNotSet(loginAuthXinKey) {
      return !loginAuthXinKey
    }
  }
}
