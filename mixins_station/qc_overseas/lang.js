const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

export default {
  methods: {
    setStationLang(stationName) {
      const { lang } = loadConfig.game.stationConstant.find((item) => {
        return item.stationName === stationName
      })
      if (lang) this.$i18n.setLocale(lang)
      else this.$i18n.setLocale('en')
    }
  }
}
