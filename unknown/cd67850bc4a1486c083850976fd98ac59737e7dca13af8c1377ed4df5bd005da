<template>
  <div class="d-flex">
    <v-container class="noscroll pa-0">
      <v-row v-if="maintainText" no-gutters align="center" class="mb-2">
        <v-col cols="12">
          <v-card color="grey-5" elevation="0" class="d-flex justify-center">
            <span class="grey-3--text custom-text-noto text-caption">
              {{ $t('in_maintenance') }}
            </span>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-else-if="!rtpStyleObj.showRTPTooltipStatus" no-gutters align="center" class="mb-2">
        <v-col cols="12">
          <v-card color="grey-5" elevation="0" class="d-flex justify-center">
            <span class="grey-3--text custom-text-noto text-caption">
              {{ $t('rtp_show_comming_soon') }}
            </span>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-else no-gutters align="center" class="mb-2 rtp-show-container">
        <v-col cols="4" class="px-1">
          <v-card color="grey-5" elevation="0" class="d-flex">
            <rtpShow
              text="single_daily"
              background-color="grey-5"
              :rtp="dailyRtp"
              :icon="rtpStyleObj.dailyIcon"
              :icon-color="rtpStyleObj.dailyIconColor"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
        <v-col cols="4" class="px-1">
          <v-card color="grey-5" elevation="0" class="d-flex">
            <rtpShow
              text="single_weekly"
              background-color="grey-5"
              :rtp="weeklyRtp"
              :icon="rtpStyleObj.weeklyIcon"
              :icon-color="rtpStyleObj.weeklyIconColor"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
        <v-col cols="4" class="px-1">
          <v-card color="transparent" class="grey-5 d-flex" elevation="0">
            <rtpShow
              text="single_monthly"
              background-color="grey-5"
              :rtp="monthlyRtp"
              :icon="rtpStyleObj.monthlyIcon"
              :icon-color="rtpStyleObj.monthlyIconColor"
              chip-text-color="default-content"
            />
          </v-card>
        </v-col>
      </v-row>
      <v-row no-gutters>
        <v-col cols="12">
          <v-card
            class="grey-5 d-flex justify-space-between align-center"
            elevation="0"
            height="50px"
          >
            <span class="px-4 grey-5-text custom-text-noto text-body-2">
              {{ $t('expected_value') }}
            </span>
            <span class="px-4 grey-5-text custom-text-noto text-body-2"> {{ defaultRtp }}% </span>
          </v-card>
        </v-col>
      </v-row>
      <v-row class="mt-2" no-gutters>
        <v-col cols="12">
          <template v-if="!rtpStyleObj.showRTPTooltipStatus || maintainText">
            <span class="custom-text-noto text-caption default-content--text"
              >{{ $t('expected_value_explanation_noty1') }}
            </span>
          </template>
          <template v-else>
            <p class="mb-1 custom-text-noto text-caption default-content--text">
              {{ $t('expected_value_explanation_noty1') }}
              <span class="custom-text-noto text-caption default-content--text"
                >{{ $t('expected_value_explanation_noty2') }}
              </span>
            </p>
            <p
              v-if="!maintainText"
              class="mb-1 custom-text-noto text-caption default-content--text"
            >
              {{ $t('expected_value_explanation_noty3') }}
            </p>
            <p
              v-if="!maintainText"
              class="mb-0 custom-text-noto text-caption default-content--text"
            >
              {{ $t('expected_value_explanation_noty4') }}
            </p>
          </template>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>
<script>
  const STATION = process.env.STATION
  export default {
    name: 'RtpDashBoad',
    components: {
      rtpShow: () => import(`~/components_station/${STATION}/rtp/rtpShow`)
    },
    props: {
      dailyRtp: {
        type: Number,
        default: () => {}
      },
      weeklyRtp: {
        type: Number,
        default: () => {}
      },
      monthlyRtp: {
        type: Number,
        default: () => {}
      },
      rtpStyleObj: {
        type: Object,
        default: () => {}
      },
      defaultRtp: {
        type: Number,
        default: 0
      },
      iconColor: {
        type: String,
        default: 'grey-3--text'
      },
      noExperienceText: {
        type: Boolean,
        default: false
      },
      hasRobotText: {
        type: Boolean,
        default: true
      },
      maintainText: {
        type: Boolean,
        default: false
      }
    }
  }
</script>
<style lang="scss" scoped>
  .rtp-show-container {
    margin: 0px -4px;
  }
</style>
