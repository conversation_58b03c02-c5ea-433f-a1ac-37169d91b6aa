<template>
  <v-dialog
    max-width="600"
    :fullscreen="$vuetify.breakpoint.xsOnly"
    persistent
    content-class="noscroll"
    transition="dialog-transition"
    v-model="showQcGameSettingStatusTmp"
  >
    <v-card color="transparent" elevation="0">
      <customDialogTitle title="Game Settings" @closeDialog="closeDialog" />
    </v-card>
    <v-container fluid class="pa-4 rounded-3">
      <v-row class="text-center align-center">
        <v-col cols="6" class="d-flex justify-center">
          <v-select
            v-model="vip"
            :items="vipNamesList"
            :disabled="disableBtnStatus"
            label="vip"
            data-vv-name="level"
            shaped
            filled
            class="mb-0"
            style="max-width: 300px; width: 100%"
          ></v-select>
        </v-col>
        <v-col cols="6" class="d-flex justify-center">
          <v-btn
            class="button-content--text"
            color="primary-variant-1"
            elevation="0"
            :disabled="disableBtnStatus"
            @click="setVIP()"
          >
            VIP update
          </v-btn>
        </v-col>
      </v-row>
      <v-row class="text-center align-center">
        <v-col cols="6" class="d-flex justify-center">
          <v-text-field
            ref="money"
            v-model="money"
            v-validate="'required|numeric'"
            type="number"
            :disabled="disableBtnStatus"
            :error-messages="errors.first('form.money')"
            label="money"
            data-vv-name="money"
            data-vv-scope="form"
            shaped
            filled
            hide-spin-buttons
            @keydown.enter="sendVerifyCodeMessage"
            style="max-width: 300px; width: 100%"
          />
        </v-col>
        <v-col cols="6" class="d-flex justify-center">
          <v-btn
            class="button-content--text"
            color="primary-variant-1"
            elevation="0"
            :disabled="disableBtnStatus || !isPositiveNumber"
            @click="setMoney()"
          >
            MONEY update
          </v-btn>
        </v-col>
      </v-row>
    </v-container>
  </v-dialog>
</template>
<script>
  export default {
    name: 'QcGameSetting',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showQcGameSettingStatus: {
        type: Boolean,
        required: true,
        default: false
      }
    },
    data() {
      return {
        vip: '1_LowBronze_低等青銅',
        disableBtnStatus: false,
        money: 0,
        showQcGameSettingStatusTmp: true
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      vipNamesList({ $store }) {
        // 青銅階級有 1-9 與 10 - 19 不同級距，所以多個低等青銅作區分
        const vips = ['1_LowBronze_低等青銅']
        const vipLevelInfo = $store.getters['role/vipLevelInfo']
        const vipLevelInfoMap = vipLevelInfo.map((item) => {
          return item.VipLevel + '_' + item.Vip + '_' + item.DisplayName
        })
        return [...vips, ...vipLevelInfoMap]
      },
      vipNames({ $store }) {
        // 青銅階級有 1-9 與 10 - 19 不同級距，所以多個低等青銅作區分
        const vips = ['LowBronze']
        const vipLevelInfo = $store.getters['role/vipLevelInfo'].map((item) => {
          return item.vip
        })
        return [...vips, ...vipLevelInfo]
      },
      level({ $store }) {
        return $store.getters['role/level']
      },
      isPositiveNumber() {
        return Number(this.money) >= 0
      }
    },
    watch: {
      language(val) {
        this.$store.commit('gameHall/SET_LANGUAGE', val)
      },
      showQcGameSettingStatus: {
        async handler(status) {
          this.showQcGameSettingStatusTmp = status
        }
      }
    },
    async created() {},
    mounted() {},
    methods: {
      closeDialog() {
        this.$nuxt.$emit('root:showQcGameSettingStatus', false)
      },
      async fetchGameList(lang) {
        // 為避免點擊遊戲之後剛好掛維護，使用者進入遊戲時無法正常執行，故重新排序一次遊戲清單
        const limit = 999
        await this.$store.dispatch('gameHall/fetchGameList', {
          lang,
          limit
        })
      },
      vipLevel(vip, status) {
        let settingObj = {
          vip: 0,
          level: 0
        }
        if (vip === 1) {
          if (status === 'LowBronze') {
            settingObj.vip = 1
            settingObj.level = 9
          } else {
            settingObj.vip = 1
            settingObj.level = 19
          }
        } else if (vip === 2) {
          settingObj.vip = 2
          settingObj.level = 49
        } else if (vip === 3) {
          settingObj.vip = 3
          settingObj.level = 149
        } else if (vip === 4) {
          settingObj.vip = 4
          settingObj.level = 1049
        } else if (vip === 5) {
          settingObj.vip = 5
          settingObj.level = 2049
        } else if (vip === 6) {
          settingObj.vip = 6
          settingObj.level = 5049
        } else if (vip === 7) {
          settingObj.vip = 7
          settingObj.level = 10049
        } else if (vip === 8) {
          settingObj.vip = 8
          settingObj.level = 50049
        } else if (vip === 9) {
          settingObj.vip = 9
          settingObj.level = 100049
        } else if (vip === 10) {
          settingObj.vip = 10
          settingObj.level = 1000049
        }
        return settingObj
      },
      async setVIP() {
        this.disableBtnStatus = true
        const receivedString = this.vip
        const parts = receivedString.split('_')
        const settingObj = this.vipLevel(Number(parts[0]), parts[1])

        await this.$store.dispatch('social/sendGMT', 'setlevel=' + settingObj.level)
        this.$notify.info('LEVEL SET OK !!')
        setTimeout(async () => {
          await this.$store.dispatch('social/sendGMT', 'setvip=' + settingObj.vip)
          this.$notify.info('VIP SET OK !!')
          this.disableBtnStatus = false
        }, 4000)
      },
      async setMoney() {
        const validate = await this.$validator.validate('form.*')
        if (validate) {
          this.disableBtnStatus = true
          const commandLine = 'setmoney=' + Number(this.money)
          await this.$store.dispatch('social/sendGMT', commandLine)
          this.$notify.info('MONEY SET OK !!')
          setTimeout(() => {
            this.money = 0
            this.disableBtnStatus = false
          }, 3000)
        }
      }
    }
  }
</script>
