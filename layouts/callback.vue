<template>
  <v-container class="pt-16 pb-14"> </v-container>
</template>

<script>
  import lineMgr from '@/mixins/lineMgr.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import analytics from '@/mixins/analytics.js'
  export default {
    name: 'callback',
    mixins: [lineMgr, preLoginAction, analytics],
    data() {
      return {}
    },

    created() {},
    mounted() {
      this.webLoginCheckout()
    },
    methods: {}
  }
</script>
