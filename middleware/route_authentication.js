// eslint-disable-next-line no-unused-vars
export default async ({ app, store, route, error, req, res, redirect, router }) => {
  // 只處理 play 和 demo 開頭的路徑
  const { path } = route
  const segments = path.split('/')
  // QC站只要測試遊戲，無需首頁，所以直接跳到game頁面
  const STATION = process.env.STATION
  if (STATION === 'qc_overseas') {
    if (route.path === '/') {
      // 重定向到 game 頁面，預設為馬來西亞
      redirect('/malaysia_01/game')
      return
    }
  } else if (STATION === 'qc_domestic') {
    if (route.path === '/') {
      // 重定向到 game 頁面，預設為馬來西亞
      redirect('/taiwan_01/game')
      return
    }
  }

  // 確保路徑格式為 /play/數字 或 /demo/數字
  // 用於路徑轉換，將 play 或 demo 轉換為小寫
  if (
    segments.length === 3 && // 確保路徑格式為 ["", "play|demo", "數字"]
    (segments[1].toLowerCase() === 'play' || segments[1].toLowerCase() === 'demo') &&
    !isNaN(segments[2]) && // 確保最後一段是數字
    segments[1] !== segments[1].toLowerCase() // 確保需要轉換
  ) {
    const newPath = `/${segments[1].toLowerCase()}/${segments[2]}`
    return process.client ? router.replace(newPath) : redirect(newPath)
  }

  if (!process.client) {
    const acceptLanguage = req.headers['accept-language'] || app.i18n.locale
    const [clientLang] = acceptLanguage
      .split(',')
      .map((lang) => String.prototype.toLowerCase.call(lang.split(';')[0]))

    if (app.i18n.locale !== clientLang && app.i18n.defaultLocale === app.i18n.locale) {
      const locales = app.i18n.locales.map((lang) => lang.code)
      if (locales.includes(clientLang)) {
        app.i18n.setLocale(clientLang)
      } else if (app.i18n.fallbackLocale[clientLang]) {
        app.i18n.setLocale(app.i18n.fallbackLocale[clientLang][0])
      }
    }
  }

  const userAgent = process.client ? window.navigator.userAgent : req.headers['user-agent']
  const isIE = userAgent.indexOf('MSIE') > 0 || userAgent.indexOf('Trident/') > 0
  if (isIE) {
    return error({ statusCode: 200 })
  }

  // 當前語系為預設語系，且路徑不為根目錄時，導向根目錄，否則會噴 404
  const defaultLocale = app.i18n.defaultLocale
  const locale = route.path.split('/')[1]
  if (locale === defaultLocale && route.path !== '/') {
    redirect('/')
  }

  // 換頁時檢查維護狀態
  //加await是為了第一次載入網頁SSR，等待fetch執行完畢後再將網頁送至client端
  await store.dispatch('maintain/fetch')
  /**
   * Back-forward cache 造成上一頁功能 cache 錯誤的資訊
   * 參照：https://web.dev/bfcache/
   */
  if (!process.client) {
    res.setHeader('Cache-Control', 'private, max-age=0, no-cache, no-store')
    res.setHeader('Pragma', 'no-cache')
    res.setHeader('Expires', '0')
  }
}
