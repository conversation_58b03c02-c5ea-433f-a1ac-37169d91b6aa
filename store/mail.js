import Vue from 'vue'

export const state = () => ({
  notyCount: 0,
  list: [],
  firstRecive: false,
  showNotyStatus: false,
  scrollTopStatus: false,
  canSearchStranger: false,
  openSendMailDirectly: false
})

export const getters = {
  list(state) {
    return state.list
  },
  getNotyCount(state) {
    return state.notyCount
  },
  getFirstRecive(state) {
    return state.firstRecive
  },
  getShowNotyStatus(state) {
    return state.showNotyStatus
  },
  getScrollTopStatus(state) {
    return state.scrollTopStatus
  },
  canSearchStranger(state) {
    return state.canSearchStranger
  },
  openSendMailDirectly(state) {
    return state.openSendMailDirectly
  }
}

export const mutations = {
  SET_SORT_ITEMS(state) {
    state.list.sort((a, b) => {
      if (a.isRead === false && b.isRead === true) return -1
      if (a.isRead === true && b.isRead === false) return 1
      return a.expire.getTime() - b.expire.getTime()
    })
  },
  SET_ALL_MAIL(state, data) {
    state.list = data
  },
  ADD_SINGLE_MAIL(state, data) {
    // itemType 是總部回傳的公會 id
    const existIdx = state.list.findIndex((item) => item.itemType === data.itemType)

    // 非公會邀請信 or 是公會邀請信(但不存在相同公會)，就直接加入
    if (!data.guildInvitationLetter || existIdx === -1) {
      state.list.push(data)
      return
    }

    // 存在相同公會的邀請信，比較 creatDate
    // 若新信件的 creatDate 較晚，則取代原信件
    if (this.$moment(data.creatDate) > this.$moment(state.list[existIdx].creatDate)) {
      state.list[existIdx] = data
    }
  },
  UPDATE_SINGLE_MAIL(state, { idx, data }) {
    state.list[idx] = data
  },
  DEL_SINGLE_MAIL(state, id) {
    state.list.filter((item) => item.id !== id)
  },
  READ_SINGLE_MAIL(state, num) {
    const expireTime = this.$moment(state.list[num].expire) // 讀取信件的到期時間
    const currentTime = this.$moment() // 獲取當下時間
    const daysDifference = expireTime.diff(currentTime, 'minutes') // 計算到期日與當前時間的天數差異
    // 如果到期日超過7天(10080分鐘)，則更新到期日為當前時間加7天
    if (daysDifference > 10080) state.list[num].expire = currentTime.add(10080, 'minutes').toDate() // 更新store中該筆信件的到期日
    // 創建了新的物件參考,確保響應式系統能偵測到變化
    const updatedItem = {
      ...state.list[num], // 展開原本索引值為 num 的所有屬性
      isRead: true // 覆蓋 isRead 屬性
    }
    // 使用 Vue.set 確保這個變化是響應式的
    Vue.set(state.list, num, updatedItem)
  },
  SET_NOTY_COUNT(state, data) {
    state.notyCount = data
  },
  SET_FIRST_RECIVE(state, data) {
    state.firstRecive = data
  },
  SET_SHOW_NOTY_STATUS(state, data) {
    state.showNotyStatus = data
  },
  SET_SCROLL_TOP_STATUS(state, data) {
    state.scrollTopStatus = data
  },
  SET_OPEN_SEND_MAIL_DIRECTLY(state, data) {
    state.openSendMailDirectly = data
  },
  RESET(state) {
    Vue.set(state, 'list', [])
    Vue.set(state, 'notyCount', 0)
    Vue.set(state, 'firstRecive', false)
  }
}

export const actions = {
  async init({ commit, getters }, data) {
    commit('ADD_SINGLE_MAIL', data)
    commit('SET_SORT_ITEMS')
    commit('SET_NOTY_COUNT', getters['list'].filter((val) => val.isRead === false).length)
  }
}
