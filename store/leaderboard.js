import Vue from 'vue'

export const state = () => ({
  topPlayersData: {},
  activeRank: 0,
  isLoading: true,
  showGameIntroDialogStatus: false,
  webGameList: [],
  rtpCache: new Map(),
  selectPlayerData: {},
  leaderboardCache: new Map()
})

export const getters = {
  topPlayersData(state) {
    return state.topPlayersData
  },
  activeRank(state) {
    return state.activeRank
  },
  isLoading(state) {
    return state.isLoading
  },
  showGameIntroDialogStatus(state) {
    return state.showGameIntroDialogStatus
  },
  webGameList(state) {
    return state.webGameList
  },
  getRtpData: (state) => (gameId) => {
    return state.rtpCache.get(gameId) || {}
  },
  getAllRtpData(state) {
    return state.rtpCache
  },
  selectPlayerData(state) {
    return state.selectPlayerData
  },
  leaderboardCache(state) {
    return state.leaderboardCache
  }
}

export const mutations = {
  SET_TOP_PLAYERS_DATA(state, data) {
    const { list } = data
    // TODO: remove
    list.forEach((item) => {
      if (item.username === '人生比狗難') {
        item.username = '橘紅孔丘'
        item.gameId = 633
      }
    })
    state.topPlayersData = { ...data, list }

    // state.topPlayersData = data
  },
  RESET_TOP_PLAYERS_DATA(state) {
    Vue.set(state, 'topPlayersData', {})
  },
  SET_ACTIVE_RANK(state, value) {
    state.activeRank = value
  },
  SET_IS_LOADING(state, value) {
    state.isLoading = value
  },
  SET_SHOWGAMEINTRODIALOGSTATUS(state, data) {
    state.showGameIntroDialogStatus = data
  },
  SET_WEB_GAME_LIST(state, list) {
    if (list && list.length === 1) {
      state.webGameList = [...state.webGameList, ...list]
    }
    state.webGameList = list
    console.log(`webgamelist 總數量是`, state.webGameList.length)
  },
  REMOVE_WEB_GAME_BY_PLATFORM_ID(state, platformId) {
    state.webGameList.forEach((item) => {
      if (item.platformId === platformId) item.blocked = true
    })
  },
  RESET_WEB_GAME_LIST(state) {
    Vue.set(state, 'webGameList', [])
  },
  SET_BULK_RTP_DATA(state, rtpList) {
    rtpList.forEach((item) => {
      state.rtpCache.set(item.gameId, item)
    })
    console.log(`rtpCache 總數量是`, state.rtpCache.size)
  },
  SET_SELECT_PLAYER_DATA(state, data) {
    state.selectPlayerData = data
  },
  RESET_SELECT_PLAYER_DATA(state) {
    Vue.set(state, 'selectPlayerData', {
      username: '',
      level: 0,
      levelVip: 0,
      money: 0,
      thumbUrl: '',
      online: false,
      guildName: ''
    })
  },
  SET_LEADERBOARD_CACHE(state, { params, data }) {
    const serializeKey = (obj) => {
      return Object.keys(obj)
        .sort()
        .map((k) => `${k}:${obj[k]}`)
        .join('|')
    }
    if (state.leaderboardCache.has(serializeKey(params))) return
    state.leaderboardCache.set(serializeKey(params), data)
  },
  RESET(state) {
    Vue.set(state, 'topPlayersData', {})
    Vue.set(state, 'activeRank', 0)
    Vue.set(state, 'isLoading', true)
    Vue.set(state, 'webGameList', [])
    Vue.set(state, 'rtpCache', new Map())
  }
}

export const actions = {
  async getWinRanking({ commit }, params) {
    const { data } = await this.$clientApi.leaderboard.getWinRanking(params)
    if (data?.errorCode) {
      commit('RESET_TOP_PLAYERS_DATA')
      commit('SET_ACTIVE_RANK', null)
      return
    }
    params.key = 'rank'
    commit('SET_LEADERBOARD_CACHE', { params, data })
    commit('SET_TOP_PLAYERS_DATA', data)
  },
  async getOddsRanking({ commit }, params) {
    const { data } = await this.$clientApi.leaderboard.getOddsRanking(params)
    if (data?.errorCode) {
      commit('RESET_TOP_PLAYERS_DATA')
      commit('SET_ACTIVE_RANK', null)
      return
    }
    params.key = 'odds'
    commit('SET_LEADERBOARD_CACHE', { params, data })
    commit('SET_TOP_PLAYERS_DATA', data)
  },
  async getWebGameList({ state, commit, rootGetters }, gameIds) {
    const headerData = {
      username: rootGetters['role/userName'] ? rootGetters['role/userName'] : null,
      alias: null
    }
    // 單一玩家的遊戲排名，取得遊戲資訊
    if (gameIds.length === 1) {
      const isExist = state.webGameList.some((item) => item.id === gameIds)
      if (isExist) return
    }
    const { list, errorCode } = await this.$clientApi.game.gameList(
      headerData,
      gameIds,
      this.$i18n.locale
    )
    if (errorCode) return
    commit('SET_WEB_GAME_LIST', list)
  },
  async fetchGameRtp({ state, commit }, gameIds) {
    if (!gameIds || !gameIds.length) return

    // 已存在緩存中的ID, 不進行請求
    const idsToFetch = gameIds.filter((id) => !state.rtpCache.has(id))
    if (idsToFetch.length === 0) return

    const { list, errorCode } = await this.$clientApi.game.gameRTPList(idsToFetch)
    // console.log('fetch rtp update web info, and update numbers is', list.length)
    if (errorCode) return
    commit('SET_BULK_RTP_DATA', list)
  }
}
