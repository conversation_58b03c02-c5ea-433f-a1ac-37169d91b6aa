import Vue from 'vue'
const CLIENTID = '11a86521'
export const state = () => ({
  list: [
    {
      id: 4,
      dict: 'malaysia',
      img: process.env.IMAGE_URL + `/payment_provider/${CLIENTID}/creditcard.webp`,
      maintained: false,
      paymentMethod: 2, // 1 序號輸入 2 跳轉至金流商頁面
      providerUrl: ''
    },
    {
      id: 5,
      dict: 'SINGAPORE',
      img: process.env.IMAGE_URL + `/payment_provider/${CLIENTID}/oversea.webp`,
      maintained: false,
      paymentMethod: 2, // 1 序號輸入 2 跳轉至金流商頁面
      providerUrl: ''
    }
  ],
  confirmData: {},
  storedPoint: 0,
  currentPoint: 0,
  bankEnable: false,
  //用來判斷是否可使用vip購點。此判斷為購點使用，故不放在role.js
  vipState: false,
  //此次儲值是否為VIP儲值
  isVipTopUp: false
})

export const getters = {
  list(state) {
    return state.list
  },
  confirmData(state) {
    return state.confirmData
  },
  storedPoint(state) {
    return state.storedPoint
  },
  currentPoint(state) {
    return state.currentPoint
  },
  bankEnable(state) {
    return state.bankEnable
  },
  vipState(state) {
    return state.vipState && false
  },
  isVipTopUp(state) {
    return state.isVipTopUp
  }
}

export const mutations = {
  SET_CONFIRMDATA(state, data) {
    state.confirmData = data
  },
  SET_STOREDPOINT(state, data) {
    state.storedPoint = state.storedPoint + data
  },
  SET_CURRENTPOINT(state, data) {
    state.currentPoint = data
  },
  CLEAR_STOREDPOINT(state) {
    state.storedPoint = 0
  },
  CLEAR_CURRENTPOINT(state) {
    state.currentPoint = 0
  },
  SET_MAINTAIN_STATUS(state, { index, status }) {
    state.list[index].maintained = status
  },
  SET_BANK_ENABLE(state, data) {
    state.bankEnable = data
  },
  SET_VIP_STATE(state, data) {
    state.vipState = data
  },
  SET_VIP_TOP_UP(state, data) {
    state.isVipTopUp = data
  },

  RESET(state) {
    Vue.set(state, 'confirmData', {})
    Vue.set(state, 'convertPoint', 0)
    Vue.set(state, 'storedPoint', 0)
    Vue.set(state, 'bankEnable', false)
    Vue.set(state, 'vipState', false)
    Vue.set(state, 'isVipTopUp', false)
  }
}
export const actions = {}
