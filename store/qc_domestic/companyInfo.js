import Vue from 'vue'

export const state = () => ({
  list: [
    {
      id: 1,
      name: 'terms_of_use',
      dict: 'terms_of_use'
    },
    {
      id: 2,
      name: 'privacy_policy',
      dict: 'privacy_policy'
    }
  ],
  phoneNumber: '',
  customerServiceMail: '<EMAIL>',
  customerServiceHotline: '',
  customerServiceFax: '',
  companyPolicyType: 1
})

export const getters = {
  list(state) {
    return state.list
  },
  phoneNumber(state) {
    return state.phoneNumber
  },
  serviceMail(state) {
    return state.customerServiceMail
  },
  customerServiceHotline(state) {
    return state.customerServiceHotline
  },
  customerServiceFax(state) {
    return state.customerServiceFax
  },
  companyPolicyType(state) {
    return state.companyPolicyType
  }
}

export const mutations = {
  SET_COMPANY_POLICY_TYPE(state, type) {
    state.companyPolicyType = type
  },
  RESET(state) {
    Vue.set(state, 'list', [])
  }
}
export const actions = {}
