// WebSocket 重連機制分析報告
// https://flossy-resistance-b9d.notion.site/WebSocket-6256071313604bcba96169b1fd31df1e?pvs=4
export default class xinServerReconnector {
  constructor(app) {
    this.app = app
    this.store = app ? app.store : null
    this.maxRetries = 5
    this.wsClient = app && app.$wsClient ? app.$wsClient : null
    this.cookies = app && app.$cookies ? app.$cookies : null
    this.localStorage = null // 初始時設為 null，等需要時再使用
    this.isReconnecting = false
    this.lastReconnectTime = 0
    this.heartbeatCheckInterval = null // 心跳檢查定時器
    this.isKickedByServer = false // 新增：標記是否被伺服器踢出

    // 在構造函數中自動綁定實例方法
    this.checkAndReconnect = this.checkAndReconnect.bind(this)
    this.wsInit = this.wsInit.bind(this)
    this.clearUserData = this.clearUserData.bind(this)
    this.heartbeatCheck = this.heartbeatCheck.bind(this)
    this.handleServerKick = this.handleServerKick.bind(this)

    // 啟動心跳檢查
    this.startHeartbeatCheck()

    // 檢查最基本的必要屬性
    if (!this.store || !this.wsClient || !this.cookies) {
      console.error('【WebSocketReconnector】初始化失敗: 缺少必要的基本依賴', {
        store: !!this.store,
        wsClient: !!this.wsClient,
        cookies: !!this.cookies
      })
    } else {
      console.log('【WebSocketReconnector】基本依賴已初始化')
    }
  }

  async wsInit() {
    if (!this.wsClient || !this.store) {
      console.error('【WebSocket】初始化失敗: wsClient 或 store 未定義')
      return false
    }

    console.log('【WebSocket】開始初始化連接...')
    let retryTimes = 0

    do {
      try {
        const endPoint = this.wsClient.endPoint.current
        console.log('【WebSocket】嘗試連接到:', endPoint)

        if (retryTimes > this.maxRetries) {
          console.error('【WebSocket】超過最大重試次數')
          this.store.commit('xinProtocol/SET_SERVER_MSG', 'server_kick_noty2')
          return false
        }

        retryTimes++
        this.store.commit('xinProtocol/SET_SOCKET_RETRY_TIMES', retryTimes)

        await this.wsClient.disconnect(1000)
        await this.wsClient.connect(endPoint)

        if (this.wsClient.isConnected) {
          console.log('【WebSocket】連接成功')
          this.store.commit('xinProtocol/SET_SOCKET_RETRY_TIMES', 0)
          return true
        }
      } catch (err) {
        console.error('【WebSocket】連接失敗:', err)
        this.wsClient.endPoint.next()
      }
    } while (!this.wsClient.isConnected)

    return false
  }

  async clearUserData() {
    const now = new Date()
    const timestamp = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}.${now.getMilliseconds()}`

    console.group(`%c【用戶數據清除】${timestamp}`, 'color: #e74c3c; font-weight: bold;')
    console.log('%c開始清除所有用戶登入信息...', 'color: #3498db;')

    try {
      // 清除 Cookies
      console.group('%c1. Cookie 數據', 'color: #2ecc71; font-weight: bold;')
      if (this.cookies) {
        try {
          this.cookies.remove('xinToken')
          console.log('✅ 成功移除 xinToken')
        } catch (cookieError) {
          console.warn('❌ 移除 xinToken 失敗', cookieError)
        }
      } else {
        console.warn('⚠️ cookies 未定義，跳過清除')
      }
      console.groupEnd()

      // 重置 Store
      console.group('%c3. Store 狀態', 'color: #2ecc71; font-weight: bold;')
      if (this.store) {
        // 重置心跳計數器
        try {
          this.store.dispatch('clear')
          console.log('✅ 清除 store 數據')
          console.log('✅ 成功重置心跳計數器')
        } catch (heartbeatError) {
          console.warn('❌ 重置心跳計數器失敗', heartbeatError)
        }
      } else {
        console.warn('⚠️ store 未定義，跳過重置')
      }
      console.groupEnd()

      // 關閉 WebSocket 連接
      console.group('%c4. WebSocket 連接', 'color: #2ecc71; font-weight: bold;')
      if (this.wsClient) {
        try {
          // 檢查連接是否已開啟
          if (this.wsClient.isConnected) {
            console.log('正在關閉 WebSocket 連接...')
            await this.wsClient.disconnect(1000, 'User logged out')
            console.log('✅ 成功斷開 WebSocket 連接')

            // 停止心跳檢查
            this.stopHeartbeatCheck()
            console.log('✅ 已停止心跳檢查')
          } else {
            console.log('ℹ️ WebSocket 連接已經處於關閉狀態')
          }
        } catch (wsError) {
          console.warn('❌ 斷開 WebSocket 連接失敗', wsError)
        }
      } else {
        console.warn('⚠️ wsClient 未定義，跳過斷開連接')
      }
      console.groupEnd()

      console.log('%c🎉 用戶數據清除完成', 'color: #3498db; font-weight: bold;')
    } catch (error) {
      console.error('%c❌ 清除過程中發生錯誤:', 'color: #e74c3c; font-weight: bold;', error)
    }
    console.groupEnd()
  }

  async shouldAttemptReconnect() {
    if (!this.store || !this.cookies) {
      console.error('【重連檢查】缺少必要的依賴，無法執行重連檢查')
      return false
    }

    const isLogin = this.store.getters['role/isLogin']
    const isClientConnected = this.wsClient && this.wsClient.isConnected

    // 檢查主要服務的狀態
    let hasEnabledServices = false
    let allServicesConnected = true

    try {
      // 使用 getEnabledServices 獲取已啟用的主要服務列表
      const enabledServices = await this.getEnabledServices()
      hasEnabledServices = enabledServices.length > 0

      if (hasEnabledServices) {
        // 檢查已啟用的主要服務是否都已連接
        const services = this.store.getters['xinProtocol/services']
        allServicesConnected = enabledServices.every(
          (serviceId) => services[serviceId] && services[serviceId].connected
        )

        console.group('【服務狀態詳情】')
        console.log(`✓ 已啟用主要服務數量: ${enabledServices.length}`)
        console.log(`✓ 主要服務連接狀態: ${allServicesConnected ? '全部已連接' : '部分未連接'}`)

        enabledServices.forEach((serviceId) => {
          const service = services[serviceId]
          console.log(
            `服務 ${serviceId}: ${service ? (service.connected ? '已連接' : '未連接') : '不存在'}`
          )
        })

        if (!allServicesConnected) {
          const disconnectedServices = enabledServices.filter(
            (serviceId) => !services[serviceId].connected
          )
          console.log('✗ 未連接的主要服務:', disconnectedServices)
        }
        console.groupEnd()
      } else {
        console.log('✓ 沒有已啟用的主要服務')
      }
    } catch (error) {
      console.warn('檢查服務狀態時發生錯誤:', error)
    }

    console.group('【連接狀態詳情】')
    console.log(`✓ 用戶登入狀態: ${isLogin ? '已登入' : '未登入'}`)
    console.log(`✓ WebSocket連接狀態: ${isClientConnected ? '已連接' : '未連接'}`)
    console.groupEnd()

    // 如果所有連接都正常，只檢查心跳狀態
    if (isLogin && isClientConnected && allServicesConnected) {
      console.group('【心跳狀態檢查】')
      const heartbeatTimes = this.store.getters['xinProtocol/heartbeatTimes']
      console.log(`✓ 心跳次數: ${heartbeatTimes}`)
      console.log('✓ 所有連接狀態正常，進行心跳檢查')

      // 只有在心跳異常時才需要重連
      if (heartbeatTimes > 10) {
        console.log('✅ 符合重連條件：心跳檢測異常（約10分鐘無響應），需要重新確認連接狀態')
        console.groupEnd()
        return true
      }

      console.log('❌ 心跳正常，無需重連')
      console.groupEnd()
      return false
    }

    // 以下是各種需要重連的情況
    console.group('【異常狀態檢查】')

    // 情況2：已登入但服務未連接（可能是服務中斷）
    if (isLogin) {
      console.log('✅ 符合重連條件：已登入但服務未連接，需要重新連接服務')
      console.groupEnd()
      return true
    }

    // 情況3：WebSocket斷開但狀態未正確更新
    if (isLogin && !isClientConnected) {
      console.log('✅ 符合重連條件：WebSocket未連接但服務狀態為已連接，需要重新初始化連接')
      console.groupEnd()
      return true
    }

    // 情況4：有啟用的主要服務，但部分服務未連接
    if (isLogin && isClientConnected && hasEnabledServices && !allServicesConnected) {
      console.log('✅ 符合重連條件：部分主要服務未連接，需要重新連接服務')
      console.groupEnd()
      return true
    }

    console.log('❌ 不符合任何重連條件')
    console.groupEnd()
    return false
  }

  // force 強制重連，periodic 是否為定期檢查
  async checkAndReconnect(force = false, periodic = false) {
    // 如果被伺服器踢出，直接返回
    if (this.isKickedByServer) {
      console.log('【重連檢查】用戶已被伺服器踢出，停止重連')
      return false
    }

    console.group('%c【重連流程】', 'color: #e74c3c; font-weight: bold;')
    console.log(`執行時間: ${new Date().toLocaleTimeString()}`)
    console.log(`觸發類型: ${force ? '強制重連' : periodic ? '定期檢查' : '一般觸發'}`)

    // 基本檢查：依賴、localStorage、重連狀態和網絡狀態
    if (!this.store || !this.cookies || !this.wsClient) {
      console.error('缺少必要的基本依賴，無法執行重連')
      console.groupEnd()
      return false
    }

    if (this.isReconnecting) {
      console.log('已有重連進程在進行中，跳過此次重連')
      console.groupEnd()
      return false
    }

    if (navigator && !navigator.onLine) {
      console.warn('當前網絡離線，無法執行重連')
      console.groupEnd()
      return false
    }

    try {
      // 檢查是否需要重連
      const needsReconnect = force || (await this.shouldAttemptReconnect())

      // 檢查冷卻時間（只在非定期檢查時進行）
      const now = Date.now()
      const isInCooldown = !periodic && !force && now - this.lastReconnectTime < 10000

      console.log(`需要重連: ${needsReconnect ? '是' : '否'}`)
      console.log(`冷卻狀態: ${isInCooldown ? '冷卻中' : '可重連'}`)

      if (!needsReconnect) {
        console.log('不需要進行重連操作')
        console.groupEnd()
        return false
      }

      if (isInCooldown) {
        console.log('在冷卻期內，將在冷卻結束後自動重試')
        setTimeout(() => {
          this.checkAndReconnect(true)
        }, 10000 - (now - this.lastReconnectTime))
        console.groupEnd()
        return false
      }

      this.isReconnecting = true
      this.lastReconnectTime = now

      console.groupEnd()

      // 步驟3: 初始化 WebSocket 連接 - 只初始化一次
      const isWSConnected = this.wsClient && this.wsClient.isConnected
      if (!isWSConnected) {
        console.log('步驟3: 初始化 WebSocket 連接...')
        const wsInitialized = await this.wsInit()
        if (!wsInitialized) {
          throw new Error('WebSocket 初始化失敗')
        }
      } else {
        console.log('步驟3: WebSocket 已連接，跳過初始化')
      }

      // 步驟4: 執行重連協議
      console.log('步驟4: 執行重連協議...')
      const reconnected = await this.reconnectAfterPageReload()
      if (!reconnected) {
        throw new Error('重連協議執行失敗')
      }

      console.log('🎉 重連流程執行成功')
      console.groupEnd()
      return true
    } catch (error) {
      console.error('❌ 重連過程中發生錯誤:', error)

      try {
        // 清除用戶數據
        console.log('清除用戶數據...')
        await this.clearUserData()
        // 移除心跳計數器
        this.store.commit('xinProtocol/SET_HEARTBEAT', null)

        // 顯示通知
        if (this.app && this.app.$notify) {
          // 獲取當前時間並格式化
          const now = new Date()
          const timeStr = `${now.getFullYear()}/${String(now.getMonth() + 1).padStart(
            2,
            '0'
          )}/${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(
            2,
            '0'
          )}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(
            2,
            '0'
          )}`

          // 安全地獲取翻譯
          let errorMessage = this.app.i18n.t('server_kick_noty2')
          if (this.app.i18n && typeof this.app.i18n.t === 'function') {
            errorMessage = this.app.i18n.t('server_kick_noty2')
          } else if (typeof this.app.$t === 'function') {
            errorMessage = this.app.$t('server_kick_noty2')
          } else {
            errorMessage = '連接已中斷'
          }

          // 添加時間戳和錯誤訊息
          errorMessage += timeStr

          this.app.$notify.error(errorMessage)

          if (error && error.message) {
            errorMessage += `\n(${error.message})`
          }

          console.warn('❌', errorMessage)
        }
      } catch (secondaryError) {
        console.error('❌ 處理重連失敗時發生額外錯誤:', secondaryError)
      }

      console.groupEnd()
      return false
    } finally {
      this.isReconnecting = false
    }
  }

  // 獲取已啟用的服務列表
  async getEnabledServices() {
    console.group('【服務】獲取已啟用的服務列表')
    if (!this.store) {
      console.error('【服務】store 未定義，無法獲取服務列表')
      console.groupEnd()
      return []
    }

    try {
      const services = this.store.getters['xinProtocol/services']
      const xinConfig = this.app.$xinConfig

      // 如果沒有 xinConfig，返回空數組
      if (!xinConfig) {
        console.error('【服務】xinConfig 未定義，無法獲取目標服務列表')
        console.groupEnd()
        return []
      }

      // 定義目標服務 ID 列表
      const targetServices = [
        xinConfig.LOTTERY_SERVICE.ID,
        xinConfig.GAME_SERVICE.ID,
        xinConfig.SOCIAL_SERVICE.ID,
        xinConfig.GUILD_SERVICE.ID
      ]

      const result = []
      for (const serviceId in services) {
        // 只檢查目標服務，並且必須是已啟用的
        if (targetServices.includes(Number(serviceId)) && services[serviceId].enable) {
          console.log(`【服務】服務 ${serviceId} 已啟用，加入重連列表`)
          result.push(Number(serviceId))
        }
      }

      console.log('【服務】需要重連的服務列表:', result)
      console.groupEnd()
      return result
    } catch (error) {
      console.error('【服務】獲取已啟用服務列表時發生錯誤:', error)
      console.groupEnd()
      return []
    }
  }

  // 執行重連協議
  async reconnectAfterPageReload() {
    if (!this.wsClient || !this.store || !this.app) {
      console.error('【重連協議】缺少必要依賴，無法執行重連', {
        wsClient: !!this.wsClient,
        store: !!this.store,
        app: !!this.app
      })
      return false
    }

    try {
      console.log('【重連協議】開始執行重連流程...', new Date().toISOString())

      // 檢查連接狀態
      if (!this.wsClient.isConnected) {
        console.error('【重連協議】WebSocket 未連接，無法執行重連')
        return false
      }

      // 獲取已啟用的服務列表
      const serviceIds = await this.getEnabledServices()
      if (!serviceIds || serviceIds.length === 0) {
        throw new Error('無可用的服務')
      }

      // 獲取連接信息
      const connectionInfo = this.store.getters['role/connInfo']
      if (!connectionInfo || !connectionInfo.clientId || !connectionInfo.token) {
        throw new Error('連接信息不可用')
      }

      console.log('【重連協議】發送重連請求...')
      // 發送重連請求
      const wsPacketFactory = this.app.$wsPacketFactory
      const xinConfig = this.app.$xinConfig

      if (!wsPacketFactory || !xinConfig) {
        throw new Error('缺少封包生成器或協議配置')
      }

      this.wsClient.send(
        wsPacketFactory.reconnectionPacket(
          serviceIds[0],
          connectionInfo.clientId,
          connectionInfo.token
        )
      )

      // 等待重連響應
      const xinUtility = this.app.$xinUtility
      if (!xinUtility) {
        throw new Error('缺少工具函數')
      }

      try {
        await xinUtility.waitEvent(
          this.wsClient.receivedListeners,
          (data) => {
            return data.protocolId == xinConfig.PROTOCOL_ID.RECONNECT
          },
          3000
        )
        console.log('【重連協議】重連請求成功')
      } catch (error) {
        console.error('【重連協議】重連請求失敗:', error)
        throw new Error('重連請求失敗')
      }

      // 重連當前服務
      const services = this.store.getters['xinProtocol/services']
      console.log('【重連協議】開始重連服務...')

      for (const serviceId of serviceIds) {
        if (!services[serviceId].enable) {
          console.error(`【重連協議】服務 ${serviceId} 尚未啟用`)
          continue
        }

        if (this.isKickedByServer) {
          console.log('【重連協議】用戶已被伺服器踢出，停止重連')
          break
        }
        const receiveId = services[serviceId].receiveId
        let retryCount = 0
        const maxRetries = 3
        let joinSuccess = false

        console.group(`%c【重連協議】服務 ${serviceId}`, 'color: #3498db; font-weight: bold;')

        while (retryCount < maxRetries && !joinSuccess) {
          retryCount++
          try {
            console.log(`第 ${retryCount}/${maxRetries} 次嘗試加入服務，receiveId: ${receiveId}`)

            // 如果被伺服器踢出，直接返回
            if (this.isKickedByServer) {
              console.log('【重連協議】用戶已被伺服器踢出，停止重連')
              break
            }

            // 發送服務加入請求
            this.wsClient.send(wsPacketFactory.getServiceJoin(serviceId, receiveId + 1))

            // 等待服務加入響應
            const response = await xinUtility.waitEvent(
              this.wsClient.receivedListeners,
              (data) => data.protocolId == xinConfig.PROTOCOL_ID.REJOINED_SERVICE,
              10000 // 10秒超時
            )

            // 更新服務狀態
            this.store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: response.serviceId,
              connected: true,
              sendId: response.sendId - 1
            })

            console.log(`✅ 服務加入成功！`)
            joinSuccess = true
          } catch (error) {
            if (retryCount < maxRetries) {
              console.warn(`❌ 第 ${retryCount} 次嘗試失敗，準備重試...`, error)
              // 等待一小段時間再重試
              await new Promise((resolve) => setTimeout(resolve, 1000))
            } else {
              console.error(`❌ 服務加入失敗，已嘗試 ${maxRetries} 次`, error)
              throw new Error(`服務 ${serviceId} 加入失敗，已重試 ${maxRetries} 次`)
            }
          }
        }

        console.groupEnd()
      }

      // 重置心跳計數
      this.store.commit('xinProtocol/SET_HEARTBEAT_TIMES', 0)
      console.log('【重連協議】重連流程完成')
      return true
    } catch (error) {
      console.error('【重連協議】重連過程發生錯誤:', error)
      this.store.commit('xinProtocol/SET_HEARTBEAT', null)
      return false
    }
  }

  // 啟動心跳檢查
  startHeartbeatCheck() {
    if (process.client) {
      // 清除可能存在的舊定時器
      this.stopHeartbeatCheck()

      // 每30秒檢查一次心跳狀態
      this.heartbeatCheckInterval = setInterval(() => {
        this.heartbeatCheck()
      }, 30000)

      console.log('【心跳檢查】已啟動心跳狀態監控')
    }
  }

  // 停止心跳檢查
  stopHeartbeatCheck() {
    if (this.heartbeatCheckInterval) {
      clearInterval(this.heartbeatCheckInterval)
      this.heartbeatCheckInterval = null
      console.log('【心跳檢查】已停止心跳狀態監控')
    }
  }

  // 檢查心跳狀態
  heartbeatCheck() {
    if (!this.store) {
      console.error('【心跳檢查】store 未定義，無法檢查心跳狀態')
      return
    }

    try {
      // 獲取登入狀態和連接狀態
      const isLogin = this.store.getters['role/isLogin']
      const isWSConnected = this.wsClient && this.wsClient.isConnected

      // 檢查心跳計數
      const heartbeatTimes = this.store.getters['xinProtocol/heartbeatTimes']
      console.log(`【心跳檢查】當前心跳計數: ${heartbeatTimes}`)

      // 如果用戶未登入或服務未連接，但心跳計數不為0，則重置心跳計數器
      if ((!isLogin || !isWSConnected) && heartbeatTimes > 0) {
        console.warn(
          `【心跳檢查】檢測到異常狀態：登入=${isLogin}，WS連接=${isWSConnected}，但心跳計數=${heartbeatTimes}`
        )
        console.log('【心跳檢查】重置心跳計數器和定時器')

        this.store.commit('xinProtocol/SET_HEARTBEAT_TIMES', 0)
        this.store.commit('xinProtocol/CLEAR_HEARTBEAT')

        return
      }

      // 如果心跳計數異常高（超過120次，約12分鐘無響應），也重置
      if (heartbeatTimes > 120) {
        console.warn(`【心跳檢查】心跳計數異常高：${heartbeatTimes}，重置計數器和定時器`)
        this.store.commit('xinProtocol/SET_HEARTBEAT_TIMES', 0)
        this.store.commit('xinProtocol/CLEAR_HEARTBEAT')

        // 如果用戶已登入並且服務已連接，觸發一次重連檢查
        if (isLogin && isWSConnected) {
          console.log('【心跳檢查】觸發重連檢查...')
          this.checkAndReconnect(true)
        }
      }
    } catch (error) {
      console.error('【心跳檢查】檢查過程中發生錯誤:', error)
    }
  }

  // 處理伺服器踢出
  async handleServerKick() {
    console.group('%c【伺服器踢出】處理伺服器踢出事件', 'color: #e74c3c; font-weight: bold;')

    try {
      // 設置踢出標記
      this.isKickedByServer = true

      // 停止所有重連嘗試
      this.isReconnecting = false

      // 停止心跳檢查
      this.stopHeartbeatCheck()

      // 清除用戶數據
      await this.clearUserData()

      // 顯示通知
      if (this.app && this.app.$notify) {
        const now = new Date()
        const timeStr = `${now.getFullYear()}/${String(now.getMonth() + 1).padStart(
          2,
          '0'
        )}/${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(
          2,
          '0'
        )}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(
          2,
          '0'
        )}`

        let errorMessage = ''
        errorMessage = this.app.i18n.t('server_kick_noty2')
        errorMessage += ` (${timeStr})`

        // 如果在遊戲頁面，需要返回首頁
        if (
          window.location.pathname.includes('/play/') ||
          window.location.pathname.includes('/demo/')
        ) {
          window.location.href = '/'
        }

        this.app.$notify.error(errorMessage)
      }

      console.log('伺服器踢出處理完成')
    } catch (error) {
      console.error('處理伺服器踢出時發生錯誤:', error)
    }

    console.groupEnd()
  }

  // 新增：重置踢出狀態（用於用戶重新登入時）
  resetKickStatus() {
    this.isKickedByServer = false
  }
}
