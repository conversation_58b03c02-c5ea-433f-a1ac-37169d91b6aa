'use strict'

import RequestBase from '../base'
const STATION = process.env.STATION
const Config = require(`@/plugins/xin-socket/protocolConfig/${STATION}/protocol.config`).default

class UserCategory extends RequestBase {
  constructor() {
    super(Config.PROTOCOL_ID.SERVICE)
    this.serviceId = Config.SOCIAL_SERVICE.ID
    this.serial = 0
    this.type = Config.SOCIAL_SERVICE.TYPE.PLAYER_INFO.ID
    this.command = Config.SOCIAL_SERVICE.TYPE.PLAYER_INFO.COMMAND.CATEGORY
    this.username = ''
  }

  get size() {
    let result = 7
    result = result + 2 + this.username.length * 2
    return result
  }

  packing(writer) {
    writer.addUint16(this.serviceId) // 服務編號
    writer.addUint16(this.serial) // 流水號
    writer.addUint8(0)
    writer.addUint8(this.type)
    writer.addUint8(this.command)
    writer.addString16WithLength(this.username)
  }
}

export { UserCategory as default }
