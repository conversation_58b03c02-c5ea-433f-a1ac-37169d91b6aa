# packet_definitions 協議定義大綱

生成時間: 2025-04-21 15:55:34

總協議數量: 125

## 目錄

- [Ability](#ability) - 角色能力資訊封包  未發現使用
- [AcceptGuildMembers](#acceptguildmembers) - 接受公會邀請響應封包
- [AdGiftBag](#adgiftbag) - 廣告禮包
- [AddApplicationNotify](#addapplicationnotify) - 新增申請通知響應封包
- [AddCharacter](#addcharacter) - 新增角色
- [AddGuildVicePresident](#addguildvicepresident) - 新增副會長響應封包
- [AddMemberNotify](#addmembernotify) - 新增成員通知響應封包
- [AddVicePresidentNotify](#addvicepresidentnotify) - 新增副會長通知響應封包
- [Alive](#alive) - 心跳
- [AllowList](#allowlist) - 白名單
- [Announcement](#announcement) - 公告
- [AppLicantsList](#applicantslist) - 加入公會許可證通知響應封包
- [BanSpeaking](#banspeaking) - 禁言
- [BanSpeakingGuild](#banspeakingguild) - 公會禁言
- [BindPhone](#bindphone) - 綁定手機
- [BindPhoneVerifiedMessage](#bindphoneverifiedmessage) - 驗證訊息
- [BuyRebateCard](#buyrebatecard) - 購買回饋卡響應封包
- [ChangeCaptionNotify](#changecaptionnotify) - 改變公會名稱通知響應封包
- [ChangeGuildCaption](#changeguildcaption) - 改變公會名稱響應封包
- [ChangeGuildName](#changeguildname) - 改變公會名稱響應封包
- [ChangeGuildTotem](#changeguildtotem) - 改變公會圖騰響應封包
- [ChangeNameNotify](#changenamenotify) - 改變公會名稱通知響應封包
- [ChangeTotemNotify](#changetotemnotify) - 改變公會圖騰通知響應封包
- [ChangedRelation](#changedrelation) - 好友關係變更響應封包
- [ChannelInfo](#channelinfo) - 頻道信息響應封包
- [ChannelList](#channellist) - 頻道列表響應封包
- [ChannelRemove](#channelremove) - 頻道刪除響應封包
- [ChannelUpdate](#channelupdate) - 頻道更新響應封包
- [ChannelUserExit](#channeluserexit) - 頻道用戶退出響應封包
- [ChannelUserJoin](#channeluserjoin) - 頻道用戶加入響應封包
- [CharacterList](#characterlist) - 角色列表
- [ChatMessage](#chatmessage) - 聊天訊息
- [ChatRoomMessage](#chatroommessage) - 聊天室訊息響應封包
- [CheckGiftBag](#checkgiftbag) - 檢查禮包
- [ConnectionSuccessed](#connectionsuccessed) - 連線成功
- [CreatUserFail](#creatuserfail) - 創建用戶失敗
- [CustomerServiceMessage](#customerservicemessage) - 客服訊息
- [Echo](#echo) - 回應與延遲測量
- [EnabledGameList](#enabledgamelist) - 啟用遊戲列表
- [ExchangePoint](#exchangepoint) - 兌換點數響應封包
- [FriendInit](#friendinit) - 好友初始化封包
- [FriendOffline](#friendoffline) - 好友離線響應封包
- [FriendOnline](#friendonline) - 好友上線響應封包
- [GameMissionNotify](#gamemissionnotify) - 遊戲任務通知響應封包
- [GetAchievement](#getachievement) - 獲取成就
- [GetBetReport](#getbetreport) - 獲取投注報告
- [GetGameLink](#getgamelink) - 獲取遊戲連結
- [GetGuildChat](#getguildchat) - 公會聊天記錄響應封包
- [GetHonor](#gethonor) - 獲取榮譽
- [GetMahjongRank](#getmahjongrank) - 獲取麻將排名
- [GetOfficialNames](#getofficialnames) - 獲取官方暱稱
- [GetUserGuild](#getuserguild) - 用戶公會響應封包
- [GiftBagList](#giftbaglist) - 禮包列表
- [GrandPrizeNoty](#grandprizenoty) - 大獎通知響應封包
- [GuildAddMembership](#guildaddmembership) - 公會加入響應封包
- [GuildChatError](#guildchaterror) - 公會聊天錯誤響應封包
- [GuildDetail](#guilddetail) - 公會詳細信息封包
- [GuildGetMission](#guildgetmission) - 公會任務響應封包
- [GuildGetRanking](#guildgetranking) - 公會排名響應封包
- [GuildInit](#guildinit) - 公會初始化響應封包
- [GuildLeave](#guildleave) - 公會離開響應封包
- [GuildMemberLeaveNotify](#guildmemberleavenotify) - 公會成員離開通知響應封包
- [GuildMemberOfflineNotify](#guildmemberofflinenotify) - 公會成員離線通知響應封包
- [GuildMemberOnlineNotify](#guildmemberonlinenotify) - 公會成員上線通知響應封包
- [GuildMessageTimeoutNotify](#guildmessagetimeoutnotify) - 公會消息超時通知響應封包
- [GuildPointClearNotify](#guildpointclearnotify) - 公會點數清零通知響應封包
- [GuildPointRefreshNotify](#guildpointrefreshnotify) - 公會點數刷新通知響應封包
- [GuildRejectMembership](#guildrejectmembership) - 公會拒絕加入響應封包
- [GuildRemoveApplicationNotify](#guildremoveapplicationnotify) - 公會移除申請響應封包
- [GuildRemoveMemberNotify](#guildremovemembernotify) - 公會移除成員通知響應封包
- [GuildRemoveMembership](#guildremovemembership) - 公會移除成員響應封包
- [GuildRemoveVicePresident](#guildremovevicepresident) - 公會移除副會長響應封包
- [GuildRemoveVicePresidentNotify](#guildremovevicepresidentnotify) - 公會移除副會長通知響應封包
- [HealthyCheck](#healthycheck) - 健康檢查
- [Info](#info) - 銀行資訊響應封包
- [LoginFailed](#loginfailed) - 登入失敗
- [LoginRecord](#loginrecord) - 登入記錄
- [LoginSucceed](#loginsucceed) - 登入成功
- [LoginVerifiedMessage](#loginverifiedmessage) - 驗證訊息
- [Logout](#logout) - 登出
- [MailCoinLimit](#mailcoinlimit) - 郵件金額限制
- [MailDelete](#maildelete) - 郵件刪除
- [MailDeleteAll](#maildeleteall) - 郵件刪除所有
- [MailGetNew](#mailgetnew) - 郵件獲取
- [MiniGameAnswer](#minigameanswer) - 小遊戲答題響應封包
- [MiniGameClearCanvas](#minigameclearcanvas) - 小遊戲清除畫布封包
- [MiniGameEnd](#minigameend) - 小遊戲結束封包
- [MiniGameForceClose](#minigameforceclose) - 小遊戲強制關閉封包
- [MiniGameSetHost](#minigamesethost) - 小遊戲設置主持人封包
- [MiniGameStart](#minigamestart) - 小遊戲開始封包
- [MiniGameUpdateCanvas](#minigameupdatecanvas) - 小遊戲畫布更新封包
- [NeedToAddCharacter](#needtoaddcharacter) - 需要新增角色
- [OtpMessage](#otpmessage) - OTP訊息
- [PasswordType](#passwordtype) - 密碼類型響應封包
- [PaymentUrl](#paymenturl) - 支付URL
- [PaymentUrlForVip](#paymenturlforvip) - 支付URL
- [PersonalBadge](#personalbadge) - 個人徽章響應封包
- [Ranking](#ranking) - 排行榜
- [ReadAllMail](#readallmail) - 閱讀所有郵件
- [ReadMail](#readmail) - 閱讀郵件
- [ReconnectionSuccessed](#reconnectionsuccessed) - 重新連接成功
- [RedeemCode](#redeemcode) - 兌換碼
- [RedeemGift](#redeemgift) - 兌換禮物
- [RedeemProperty](#redeemproperty) - 兌換禮物
- [RejoinedService](#rejoinedservice) - 重新加入服務
- [SafeBox](#safebox) - 保險箱操作響應封包
- [SendMail](#sendmail) - 發送郵件
- [SetPassword](#setpassword) - 設定密碼
- [SuccessfullyStored](#successfullystored) - 成功儲存點數響應封包
- [SystemMessage](#systemmessage) - 系統訊息
- [UpdateCopper](#updatecopper) - 更新銅幣
- [UpdateGameId](#updategameid) - 更新遊戲ID
- [UpdateGameState](#updategamestate) - 更新遊戲狀態
- [UpdateLevel](#updatelevel) - 更新等級
- [UpdateMoney](#updatemoney) - 更新金錢
- [UpdateReferralMoney](#updatereferralmoney) - 更新推薦金錢
- [UpdateUserCoinItem](#updateusercoinitem) - 更新用戶代幣類道具
- [UpdateUserStatusItem](#updateuserstatusitem) - 更新用戶狀態類道具
- [InitStatusInventory](#initstatusinventory) - 初始化狀態道具清單封包
- [getGameInventory](#getgameinventory) - 取得遊戲道具清單封包
- [UpdateVip](#updatevip) - 更新VIP
- [UseGashCard](#usegashcard) - Gash卡訊息響應封包
- [UserDetail](#userdetail) - 用戶詳細資訊
- [UserInfo](#userinfo) - 用戶資訊
- [UserKey](#userkey) - 用戶金鑰
- [Withdraw](#withdraw) - 提款
- [XinToMahjong](#xintomahjong) - 星幣轉換麻將
- [YoeCardMessage](#yoecardmessage) - Yoe卡訊息響應封包


## 協議詳情

### Ability

- **描述**: 角色能力資訊封包  未發現使用
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [999, 999, 999]
  - isFeature: [171]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **count** (uint8): 能力數量
  - **abilities** (array): 能力列表
    - 選項: `count={count}`
    - 子字段:
      - **id** (uint8): 能力ID
      - **increases** (uint8): 能力提升值

### AcceptGuildMembers

- **描述**: 接受公會邀請響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [45]
  - isFeature: [187]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### AdGiftBag

- **描述**: 廣告禮包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [60000, 60901, 60902, 60904]
  - typeArray: [0, 187, 8]
  - isFeature: [223]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **adName** (string): 廣告名稱
    - 選項: `withLength16`
  - **gameID** (uint16): 遊戲ID
    - 選項: `bigEndian`
  - **action** (uint8): 動作
  - **url** (derived): 連結
    - 選項: `transform=''`
    - 條件: `reader.isEOF()`
  - **url** (string): 連結
    - 選項: `withLength16`
    - 條件: `!reader.isEOF()`
  - **gameCategory** (derived): 遊戲類型
    - 選項: `transform=0`
    - 條件: `reader.isEOF()`
  - **gameCategory** (long): 遊戲類型
    - 選項: `littleEndian`
    - 條件: `!reader.isEOF()`

### AddApplicationNotify

- **描述**: 新增申請通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [73]
  - isFeature: [196]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### AddCharacter

- **描述**: 新增角色
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 134]
  - isFeature: [91]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **photo** (uint8): 照片
  - **online** (uint8): 是否在線
    - 選項: `transform=value==1`
  - **level** (uint24): 等級
    - 選項: `bigEndian`
  - **username** (string): 用戶名
    - 選項: `withLength16`

### AddGuildVicePresident

- **描述**: 新增副會長響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [48]
  - isFeature: [190]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### AddMemberNotify

- **描述**: 新增成員通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [75]
  - isFeature: [198]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`
  - **isOnline** (uint8): 是否在線
    - 選項: `transform=value==1`

### AddVicePresidentNotify

- **描述**: 新增副會長通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [77]
  - isFeature: [200]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### Alive

- **描述**: 心跳
- **識別信息**:
  - protocolId: 255
  - serviceId: 255
  - typeArray: [0, 0]
  - ignoreServiceId: True
  - ignoreTypeArray: True
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **type** (value_array): 類型
    - 選項: `count=2`

### AllowList

- **描述**: 白名單
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 150]
  - isFeature: [12]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **_deviceCount** (uint16): 設備數量
  - **deviceList** (array): 設備列表
    - 選項: `count={_deviceCount}`
    - 子字段:
      - **id** (string): 設備ID
      - **name** (string): 設備名稱

### Announcement

- **描述**: 公告
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 25]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **message** (string): 訊息
    - 選項: `remaining`

### AppLicantsList

- **描述**: 加入公會許可證通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [85]
  - isFeature: [208]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **userCount** (uint8): 名字數量
  - **username** (value_array): 名字列表
    - 選項: `type=withLength16,count={userCount}`

### BanSpeaking

- **描述**: 禁言
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903]
  - typeArray: [0, 103]
  - isFeature: [130]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **expiryDate** (string): 內容
    - 選項: `remaining`

### BanSpeakingGuild

- **描述**: 公會禁言
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [253]
  - isFeature: [130]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **_skipData** (bytes): 跳過資料
    - 選項: `length=11`
  - **content** (string): 內容
    - 選項: `withLength16`

### BindPhone

- **描述**: 綁定手機
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 14, 1]
  - isFeature: [31]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **isSuccess** (uint8): 是否成功
    - 選項: `transform=value==1`
  - **message** (string): 回應
    - 選項: `withLength16`
    - 條件: `isSuccess===false`
  - **phoneNumber** (string): 手機號碼
    - 選項: `withLength16`
    - 條件: `isSuccess===true`

### BindPhoneVerifiedMessage

- **描述**: 驗證訊息
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 14, 2]
  - isFeature: [110]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **isSuccess** (uint8): 是否成功
    - 選項: `transform=value==1`
  - **message** (string): 回應
    - 選項: `withLength16`
    - 條件: `isSuccess===false`
  - **enableSafetyCode** (uint8): 是否啟用安全碼
    - 選項: `transform=value==1`
    - 條件: `isSuccess===true`
  - **phoneNumber** (string): 手機號碼
    - 選項: `withLength16`
    - 條件: `isSuccess===true`

### BuyRebateCard

- **描述**: 購買回饋卡響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60002
  - typeArray: [5]
  - isFeature: [84]
- **字段列表**:
  - **packetpacketSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **isSuccess** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **money** (ulong): 星幣
    - 選項: `littleEndian`
    - 條件: `isSuccess == true`
  - **point** (ulong): 點數
    - 選項: `littleEndian`
    - 條件: `isSuccess == true`
  - **expiryAt** (string): 到期日期
    - 選項: `withLength16`
    - 條件: `isSuccess == true`

### ChangeCaptionNotify

- **描述**: 改變公會名稱通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [83]
  - isFeature: [206]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### ChangeGuildCaption

- **描述**: 改變公會名稱響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [43]
  - isFeature: [185]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### ChangeGuildName

- **描述**: 改變公會名稱響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [41]
  - isFeature: [183]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### ChangeGuildTotem

- **描述**: 改變公會圖騰響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [42]
  - isFeature: [184]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### ChangeNameNotify

- **描述**: 改變公會名稱通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [81]
  - isFeature: [204]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **guildname** (string): 公會名稱
    - 選項: `byteCount`

### ChangeTotemNotify

- **描述**: 改變公會圖騰通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [82]
  - isFeature: [205]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`

### ChangedRelation

- **描述**: 好友關係變更響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 50, 1]
  - isFeature: [103]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **success** (uint8): 無描述
    - 選項: `transform=value === 1`
  - **online** (uint8): 無描述
    - 選項: `transform=value === 1`
    - 條件: `success`
  - **relationLength** (uint16): 關係數量
    - 條件: `success`
  - **relation** (uint8): 關係類型
    - 條件: `success && relationLength > 0`
  - **username** (string): 用戶名
    - 選項: `withLength16`
    - 條件: `!online && success`
  - **usernameLength** (uint8): 用戶名長度
    - 條件: `success && online && relationLength > 0`
  - **username** (string): 用戶名
    - 選項: `length={usernameLength*2},utf16`
    - 條件: `success && online && relationLength > 0`
  - **rank** (uint8): 等級
    - 條件: `success && online && relationLength > 0`
  - **errorMessage** (string): 錯誤信息
    - 選項: `withLength16`
    - 條件: `!success`
- **後處理邏輯**:
  - 字段 `gender`: `rank >= 100 ? 2 : 0`
  - 字段 `rank`: `rank >= 100 ? rank - 100 : rank`
  - 字段 `facebookId`: `username.includes('\t') ? username.split('\t')[1] : ''`
  - 字段 `username`: `username.includes('\t') ? username.split('\t')[0] : username`

### ChannelInfo

- **描述**: 頻道信息響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903]
  - typeArray: [0, 18, 12]
  - isFeature: [121]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **name** (string): 頻道名稱
    - 選項: `withLength16`
  - **users** (array): 使用者列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **_usernameLength** (uint8): 使用者名稱長度
      - **_rawUsername** (string): 原始使用者名稱
      - **_rawRank** (uint8): 原始等級
      - **vipLevel** (uint8): VIP等級
      - **_isHighRank** (derived): 是否高等級
      - **rank** (derived): 實際等級
      - **gender** (derived): 性別
      - **_usernameParts** (derived): 用戶名部分
      - **username** (derived): 用戶名
      - **facebookId** (derived): Facebook ID
      - **username** (derived): 用戶名
      - **facebookId** (derived): Facebook ID

### ChannelList

- **描述**: 頻道列表響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 18, 5]
  - isFeature: [122]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **channels** (array): 頻道列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **name** (string): 頻道名稱
      - **population** (uint16): 人數

### ChannelRemove

- **描述**: 頻道刪除響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 18, 7]
  - isFeature: [123]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **name** (string): 頻道名稱
    - 選項: `withLength16`

### ChannelUpdate

- **描述**: 頻道更新響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 18, 6]
  - isFeature: [124]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **channels** (array): 頻道列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **name** (string): 頻道名稱
      - **population** (uint16): 人數

### ChannelUserExit

- **描述**: 頻道用戶退出響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 18, 0]
  - isFeature: [125]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **users** (array): 用戶列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **username** (string): 用戶名稱

### ChannelUserJoin

- **描述**: 頻道用戶加入響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 18, 11]
  - isFeature: [126]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **users** (array): 使用者列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **_usernameLength** (uint8): 使用者名稱長度
      - **_rawUsername** (string): 原始使用者名稱
      - **_rawRank** (uint8): 原始等級
      - **vipLevel** (uint8): VIP等級
      - **_isHighRank** (derived): 是否高等級
      - **rank** (derived): 實際等級
      - **gender** (derived): 性別
      - **_usernameParts** (derived): 用戶名部分
      - **username** (derived): 用戶名
      - **facebookId** (derived): Facebook ID
      - **username** (derived): 用戶名
      - **facebookId** (derived): Facebook ID

### CharacterList

- **描述**: 角色列表
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 133]
  - isFeature: [10, 30]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **accountType** (uint8): 帳號類型
  - **hasPassword** (uint8): 是否設定密碼
    - 選項: `transform=value==1`
  - **timeused** (ulong): 使用時間
    - 選項: `littleEndian`
  - **referral** (string): 推薦人
    - 選項: `withLength16`
  - **charsCount** (uint8): 角色數量
  - **chars** (array): 角色列表
    - 選項: `count={charsCount}`
    - 子字段:
      - **photo** (uint8): 照片
      - **online** (uint8): 是否在線
      - **level** (uint24): 等級
      - **username** (string): 用戶名

### ChatMessage

- **描述**: 聊天訊息
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [999, 999]
  - isFeature: [52, 53]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **messageType** (uint8): 訊息類型
  - **red** (uint8): 紅色
  - **green** (uint8): 綠色
  - **blue** (uint8): 藍色
  - **username** (string): 用戶名
    - 選項: `withLength16`
  - **rank** (uint8): 階級
  - **code** (uint16): 代碼
    - 選項: `bigEndian`
  - **message** (string): 訊息
    - 選項: `remaining`

### ChatRoomMessage

- **描述**: 聊天室訊息響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903]
  - typeArray: [0, 17]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **messageType** (uint8): 訊息類型
  - **_items** (array): 暫存數據
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **contentType** (uint8): 內容類型
      - **userColorRed** (uint8): 用戶顏色R
      - **userColorGreen** (uint8): 用戶顏色G
      - **userColorBlue** (uint8): 用戶顏色B
      - **username** (string): 用戶名
      - **usernameLength** (uint8): 用戶名長度
      - **rawUsername** (string): 原始用戶名
      - **rank** (uint8): 等級
      - **message** (string): 消息內容
      - **stickerId** (uint32): 貼圖ID
      - **cardId** (uint16): 卡片ID
      - **customMessageLength** (uint16): 自定義消息長度
      - **customMessage** (string): 自定義消息
      - **customUrlLength** (uint16): 自定義URL長度
      - **customUrl** (string): 自定義URL
      - **soundUrlLength** (uint16): 聲音URL長度
      - **soundUrl** (string): 聲音URL
      - **cloudMessage** (string): 雲端消息
      - **collectedHeart** (uint8): 收集愛心數
  - **_isContentType1** (derived): 是否為內容類型1
    - 選項: `transform=function() { if(!_items || !Array.isArray(_items)) return false; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 1) return true; } return false; }()`
  - **_isContentType2** (derived): 是否為內容類型2
    - 選項: `transform=function() { if(!_items || !Array.isArray(_items)) return false; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 2) return true; } return false; }()`
  - **_username1** (derived): 類型1用戶名
    - 選項: `transform=function() { if(!_items || !Array.isArray(_items)) return ''; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 1) return _items[i].username; } return ''; }()`
    - 條件: `_isContentType1 === true`
  - **_rawUsername2** (derived): 類型2原始用戶名
    - 選項: `transform=function() { if(!_items || !Array.isArray(_items)) return ''; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 2) return _items[i].rawUsername; } return ''; }()`
    - 條件: `_isContentType2 === true`
  - **_rawRank2** (derived): 類型2原始等級
    - 選項: `transform=function() { if(!_items || !Array.isArray(_items)) return 0; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 2) return _items[i].rank; } return 0; }()`
    - 條件: `_isContentType2 === true`
  - **_isHighRank** (derived): 是否高等級
    - 選項: `transform=_rawRank2>=100`
    - 條件: `_isContentType2 === true`
  - **_usernameParts** (derived): 用戶名部分
    - 選項: `transform=_rawUsername2.split('\t')`
    - 條件: `_isContentType2 === true && _isHighRank === true`
  - **_username2** (derived): 類型2用戶名
    - 選項: `transform=_isHighRank ? _usernameParts[0] : _rawUsername2`
    - 條件: `_isContentType2 === true`
  - **_userName** (derived): 用戶名稱
    - 選項: `transform=function() { if(_isContentType1) return _username1 || ''; if(_isContentType2) return _username2 || ''; return ''; }()`
  - **_userPath** (md5): 用戶路徑
    - 選項: `source=_userName,prefix=星城_`
- **後處理邏輯**:
  - 字段 `user`: `function() { var result = { name: _userName, path: _userPath, color: { red: 0, green: 0, blue: 0 } }; if(!_items || !Array.isArray(_items)) return result; for(var i = 0; i < _items.length; i++) { var item = _items[i]; if(item.contentType === 1) { result.color.red = item.userColorRed ; result.color.green = item.userColorGreen ; result.color.blue = item.userColorBlue ; break; } else if(item.contentType === 2) { var rank = item.rank; if(rank >= 100) { result.facebookId = _usernameParts.length > 1 ? _usernameParts[1].replace('2-', '') : ''; rank = rank - 100; } result.rank = rank; result.color.red = item.userColorRed ; result.color.green = item.userColorGreen ; result.color.blue = item.userColorBlue ; break; } } return result; }()`
  - 字段 `content`: `function() { var storageBaseURL = externalData.storageBaseURL; var result = { type: -1, color: { red: 255, green: 255, blue: 0 }, allMessage: '' }; if(!_items || !Array.isArray(_items)) return result; var lastContentItem = null; var allMessage = ''; for(var i = 0; i < _items.length; i++) { var item = _items[i]; if(item.contentType === 1) { allMessage += item.username; } else if(item.contentType === 2) { var username = item.rawUsername; if(item.rank >= 100) { username = username.split('\t')[0]; } allMessage += username; } else if(item.contentType === 3) { allMessage += item.message; lastContentItem = { type: item.contentType, message: item.message, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; } else if(item.contentType === 4) { allMessage += item.stickerId; lastContentItem = { type: item.contentType, stickerId: item.stickerId }; } else if(item.contentType === 5) { allMessage += item.cardId; lastContentItem = { type: item.contentType, cardId: item.cardId, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; } else if(item.contentType === 6) { allMessage += item.customMessage + ' ' + item.customUrl; lastContentItem = { type: item.contentType, message: item.customMessage, url: item.customUrl }; } else if(item.contentType === 7) { allMessage += item.soundUrl; lastContentItem = { type: item.contentType, url: item.soundUrl }; } else if(item.contentType === 8) { var [type, token, path1, path2] = item.cloudMessage.split(' '); if(type == 1) { lastContentItem = { type: item.contentType, dataType: 1, message: item.cloudMessage, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; var thumbUrl = storageBaseURL + 'userfile\/' + _userPath + '\/' + path2.toUpperCase() + '\/data.png?t=' + token; var imageUrl = storageBaseURL + 'userfile\/' + _userPath + '\/' + path1.toUpperCase() + '\/data.png?t=' + token; lastContentItem.thumbUrl = thumbUrl; lastContentItem.imageUrl = imageUrl; } else if(type == 2) { lastContentItem = { type: item.contentType, dataType: 2, message: item.cloudMessage, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; var voiceUrl = storageBaseURL + 'userfile\/' + _userPath + '\/' + path1.toUpperCase() + '\/data.mp3?t=' + token; lastContentItem.voiceUrl = voiceUrl; } } else if(item.contentType === 100) { allMessage += item.collectedHeart; lastContentItem = { type: item.contentType, collectedHeart: item.collectedHeart, message: item.collectedHeart }; } } if(lastContentItem) { Object.assign(result, lastContentItem); } result.allMessage = allMessage; return result; }()`

### CheckGiftBag

- **描述**: 檢查禮包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 19, 2]
  - isFeature: [222]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **success** (uint8): 是否成功
    - 選項: `transform=value==1`
  - **errorCode** (uint8): 錯誤代碼
    - 條件: `success===false`
  - **orderErrorCode** (uint8): 訂單錯誤代碼
    - 條件: `success===false && reader.offset < packetSize`

### ConnectionSuccessed

- **描述**: 連線成功
- **識別信息**:
  - protocolId: 112
  - serviceId: 0
  - typeArray: [0, 0]
  - ignoreServiceId: True
  - ignoreTypeArray: True
  - isFeature: [1]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **clientId** (uint32): 連線ID
    - 選項: `bigEndian`
  - **token** (uint32): token
    - 選項: `bigEndian`

### CreatUserFail

- **描述**: 創建用戶失敗
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [240, 22]
  - isFeature: [91]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **accountType** (uint8): 帳號類型
  - **_skipData** (bytes): 跳過資料
    - 選項: `length=1`
  - **message** (string): 訊息
    - 選項: `remaining`

### CustomerServiceMessage

- **描述**: 客服訊息
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 29]
  - isFeature: [140]
  - skipTypeZero: False
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **_colorR** (uint8): 顏色R
  - **_colorG** (uint8): 顏色G
  - **_colorB** (uint8): 顏色B
  - **message** (string): 訊息內容
    - 選項: `remaining`
- **後處理邏輯**:
  - 字段 `color`: `{r:_colorR,g:_colorG,b:_colorB}`

### Echo

- **描述**: 回應與延遲測量
- **識別信息**:
  - protocolId: 38
  - serviceId: 0
  - typeArray: [0, 0]
  - ignoreServiceId: True
  - ignoreTypeArray: True
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **_timestamp** (string): 時間戳訊息
    - 選項: `withLength16`
- **後處理邏輯**:
  - 字段 `latency`: `function() { var now = new Date().getTime(); return now - parseInt(_timestamp); }()`

### EnabledGameList

- **描述**: 啟用遊戲列表
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [0, 100]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **_gameCount** (uint24): 遊戲數量
  - **gameIds** (value_array): 遊戲ID列表
    - 選項: `type=uint16,count={_gameCount}`
- **後處理邏輯**:
  - 字段 `gameIds`: `gameIds.sort((a, b) => { return a - b })`

### ExchangePoint

- **描述**: 兌換點數響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60002
  - typeArray: [40]
  - isFeature: [84]
- **字段列表**:
  - **packetpacketSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **exchangedPoint** (uint24): 兌換點數
    - 選項: `bigEndian`
  - **money** (ulong): 星幣
    - 選項: `littleEndian`
  - **point** (ulong): 點數
    - 選項: `littleEndian`

### FriendInit

- **描述**: 好友初始化封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 50, 31]
  - isFeature: [70]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **count** (uint16): 數量
    - 選項: `bigEndian`
  - **_users** (array): 使用者列表
    - 選項: `count={count}`
    - 子字段:
      - **online** (uint8): 是否在線
      - **_relationCount** (uint16): 關係數量
      - **_relations** (value_array): 關係代碼列表
      - **relation** (derived): 關係
      - **_usernameLength** (uint8): 用戶名長度
      - **_rawUsername** (string): 原始用戶名
      - **_rawRank** (uint8): 原始等級
      - **username** (string): 用戶名
      - **level** (uint32): 等級
      - **vip** (uint8): VIP等級
      - **_isHighRank** (derived): 是否高等級
      - **rank** (derived): 實際等級
      - **rank** (derived): 實際等級
      - **gender** (derived): 性別
      - **gender** (derived): 性別
      - **_usernameParts** (derived): 用戶名部分
      - **username** (derived): 用戶名
      - **facebookId** (derived): Facebook ID
      - **facebookId** (derived): Facebook ID
- **後處理邏輯**:
  - 字段 `friendList`: `_users.filter(function(u){return u.relation&0x01})`
  - 字段 `blockList`: `_users.filter(function(u){return u.relation&0x02})`

### FriendOffline

- **描述**: 好友離線響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 50, 20]
  - isFeature: [102]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **username** (string): 使用者名稱
    - 選項: `withLength16`

### FriendOnline

- **描述**: 好友上線響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 50, 21]
  - isFeature: [101]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **usernameLength** (uint8): 使用者名稱長度
  - **username** (string): 使用者名稱
    - 選項: `utf16,length={usernameLength*2}`
  - **rank** (uint8): 性別
  - **gender** (derived): 性別
    - 選項: `transform=2`
    - 條件: `rank >= 100`
  - **facebookId** (derived): Facebook ID
    - 選項: `transform=username.split('	')[1]`
    - 條件: `rank >= 100`
  - **username** (derived): 使用者名稱
    - 選項: `transform=username.split('	')[0]`
    - 條件: `rank >= 100`
  - **rank** (derived): 性別
    - 選項: `transform=rank - 100`
    - 條件: `rank >= 100`

### GameMissionNotify

- **描述**: 遊戲任務通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [88]
  - isFeature: [211]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **msgList** (uint8): 消息列表
  - **msgList** (array): 消息列表
    - 選項: `count={msgList}`
    - 子字段:
      - **unKnow** (uint16): 未知資料
      - **gamemission** (string): 遊戲任務

### GetAchievement

- **描述**: 獲取成就
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 15, 8]
  - isFeature: [46]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **username** (string): 用戶名
    - 選項: `withLength16`
  - **achievements** (array): 成就列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **gameNo** (uint16): 遊戲編號
      - **type** (uint16): 成就類型
      - **multiple** (uint32): 倍數
      - **score** (uint32): 分數

### GetBetReport

- **描述**: 獲取投注報告
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 15, 6]
  - isFeature: [45]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **username** (string): 用戶名
    - 選項: `withLength16`
  - **_skipData** (bytes): 跳過當天資料
    - 選項: `length=16`
  - **daily** (array): 每日報告
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **totalBet** (ulong): 總下注
      - **totalWin** (ulong): 總贏分
      - **_currentDate** (derived): 當前日期
      - **date** (derived): 日期

### GetGameLink

- **描述**: 獲取遊戲連結
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [107, 0]
  - isFeature: [51]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **url** (string): 連結
    - 選項: `withLength16`

### GetGuildChat

- **描述**: 公會聊天記錄響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [70]
  - isFeature: [193]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **time** (long): 時間
    - 選項: `littleEndian`
  - **chatType** (uint8): 聊天類型
  - **username** (string): 用戶名稱
    - 選項: `withLength16`
  - **message** (string): 消息內容
    - 選項: `withLength16`

### GetHonor

- **描述**: 獲取榮譽
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 15, 5]
  - isFeature: [41]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **online** (uint8): 是否在線
    - 選項: `transform=value==1`
  - **username** (string): 用戶名
    - 選項: `withLength16`
    - 條件: `online===true`
  - **value** (ulong): 榮譽值
    - 選項: `littleEndian`
    - 條件: `online===true`

### GetMahjongRank

- **描述**: 獲取麻將排名
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 170, 8, 1]
  - isFeature: [76]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=4`
  - **_rankLength** (uint8): 長度
  - **rank** (string): 排名
    - 選項: `utf16,length=_rankLength`

### GetOfficialNames

- **描述**: 獲取官方暱稱
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 101]
  - isFeature: [75]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **list** (array): 列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **id** (uint8): ID
      - **name** (string): 暱稱

### GetUserGuild

- **描述**: 用戶公會響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [14]
  - isFeature: [181]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **username** (string): 用戶名稱
    - 選項: `withLength16`
  - **guildId** (ulong): 公會ID
    - 選項: `littleEndian`
  - **hasGuild** (derived): 是否存在公會
    - 選項: `transform=guildId > 0`
  - **guildName** (string): 公會名稱
    - 選項: `withLength16`
    - 條件: `hasGuild == true`

### GiftBagList

- **描述**: 禮包列表
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 19, 1]
  - isFeature: [221]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **_itemCount** (uint16): 禮包數量
  - **list** (array): 物品列表
    - 選項: `count={_itemCount}`
    - 子字段:
      - **id** (string): 物品ID
      - **amount** (float64): 金額
      - **maxBuyLimit** (uint16): 最大購買限制
      - **userBuyCount** (uint16): 用戶購買次數
      - **_rawDate** (ulong): 原始日期
      - **date** (derived): 日期
      - **serialNumber** (uint8): 序號
  - **remainingBalance** (long): 剩餘餘額
    - 選項: `littleEndian`
- **後處理邏輯**:
  - 字段 `list`: `list.sort((a, b) => { return b.serialNumber - a.serialNumber })`

### GrandPrizeNoty

- **描述**: 大獎通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [60000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 47]
  - isFeature: [230]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **gameId** (uint16): 遊戲ID
    - 選項: `bigEndian`
  - **mappingType** (derived): 映射類型
    - 選項: `transform=externalData.gameMapping.find(item => item.gameUnpackIdList.includes(gameId))?.gameUnpackType || 7`
  - **prizeId** (uint16): 獎品ID
    - 條件: `mappingType === 0`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 0`
  - **prizeParam1** (uint8): 參數1
    - 條件: `mappingType === 1`
  - **prizeParam2** (uint8): 參數2
    - 條件: `mappingType === 1`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 1`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 1`
  - **prizeId** (string): 獎品ID
    - 選項: `withLength16`
    - 條件: `mappingType === 2`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 2`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 2`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 3`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 3`
  - **prizeId** (uint16): 獎品ID
    - 條件: `mappingType === 4`
  - **prizeParam1** (uint8): 參數1
    - 條件: `mappingType === 4`
  - **prizeParam2** (uint8): 參數2
    - 條件: `mappingType === 4`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 4`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 4`
  - **fishType** (uint32): 魚類型
    - 條件: `mappingType === 5`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 5`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 5`
  - **skillNo** (uint16): 技能編號
    - 條件: `mappingType === 6`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 6`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 6`
  - **prizeId** (uint16): 獎品ID
    - 條件: `mappingType === 7`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 7`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 7`
  - **fishType** (uint32): 魚類型
    - 條件: `mappingType === 8`
  - **betRange** (uint8): 下注範圍
    - 條件: `mappingType === 8`
  - **userTakeType** (uint8): 用戶獲取類型
    - 條件: `mappingType === 8`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 8`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 8`
    - **prizeId** (uint32): 獎品ID
    - 條件: `mappingType === 9`
  - **scorePoint** (uint32): 分數
    - 條件: `mappingType === 9`
  - **name** (string): 名稱
    - 選項: `withLength16`
    - 條件: `mappingType === 9`

### GuildAddMembership

- **描述**: 公會加入響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [44]
  - isFeature: [186]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildChatError

- **描述**: 公會聊天錯誤響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [0, 102]
  - isFeature: [212]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **unKnow** (uint16): 未知訊息
  - **response** (string): 回應
    - 選項: `remaining`

### GuildDetail

- **描述**: 公會詳細信息封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [11]
  - isFeature: [194]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
    - 選項: `bigEndian`
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **guildId** (ulong): 公會ID
    - 選項: `littleEndian`
    - 條件: `success == true`
  - **guildname** (string): 公會名稱
    - 選項: `withLength16`
    - 條件: `success == true`
  - **pointcount** (uint8): 點數類型數量
    - 條件: `success == true`
  - **guildpoints** (value_array): 公會點數列表
    - 選項: `type=uint32,count={pointcount},bigEndian`
    - 條件: `success == true`
  - **presidentname** (string): 會長名稱
    - 選項: `withLength16`
    - 條件: `success == true`
  - **guildcaption** (string): 公會宣言
    - 選項: `withLength16`
    - 條件: `success == true`
  - **vicepresidentcount** (uint8): 副會長數量
    - 條件: `success == true`
  - **vicepresidentnames** (value_array): 副會長名稱列表
    - 選項: `type=withLength16,count={vicepresidentcount}`
    - 條件: `success == true`
  - **membercount** (uint16): 成員數量
    - 選項: `bigEndian`
    - 條件: `success == true`
  - **members** (array): 成員列表
    - 選項: `count={membercount}`
    - 條件: `success == true`
    - 子字段:
      - **name** (string): 成員名稱
      - **rank** (derived): 成員等級
      - **points** (uint32): 貢獻點數
      - **onlines** (uint8): 在線狀態
- **後處理邏輯**:
  - 字段 `members`: `members.sort((a, b) => { if (a.rank !== b.rank) return b.rank - a.rank; if (a.onlines !== b.onlines) return b.onlines - a.onlines; return b.points - a.points; })`

### GuildGetMission

- **描述**: 公會任務響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [13]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **missionIds** (uint8): 任務ID
  - **missionRate** (uint8): 任務完成率
    - 條件: `missionIds != 0`
  - **missionLimit** (uint8): 任務限制
    - 條件: `missionIds != 0`

### GuildGetRanking

- **描述**: 公會排名響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [12]
  - isFeature: [182]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **guildAward** (ulong): 公會獎勵
    - 選項: `littleEndian`
  - **time** (string): 時間
    - 選項: `withLength16`
  - **count** (uint16): 數量
    - 選項: `bigEndian`
  - **guildDatas** (array): 公會列表
    - 選項: `count={count}`
    - 子字段:
      - **id** (ulong): 公會ID
      - **name** (string): 公會名稱
      - **onlines** (uint16): 在線人數
      - **members** (uint16): 成員數量
      - **points** (uint32): 貢獻點數
  - **unKnow** (uint8): 未知訊息
  - **rateCount** (uint8): 排名數量
  - **weeklyRates** (value_array): 排名列表
    - 選項: `count={rateCount}`
- **後處理邏輯**:
  - 字段 `guildDatas`: `guildDatas.sort((a, b) => { return b.points - a.points })`

### GuildInit

- **描述**: 公會初始化響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [72]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **hasGuild** (uint8): 是否存在公會
    - 選項: `transform=value==1`
  - **guildId** (ulong): 公會ID
    - 選項: `littleEndian`
    - 條件: `hasGuild == true`
  - **guildName** (string): 公會名稱
    - 選項: `withLength16`
    - 條件: `hasGuild == true`
  - **rankCount** (uint8): 排名數量
    - 條件: `hasGuild == true`
  - **ranks** (value_array): 排名列表
    - 選項: `type=uint32,count={rankCount}`
    - 條件: `hasGuild == true`
  - **owner** (string): 會長名稱
    - 選項: `withLength16`
    - 條件: `hasGuild == true`
  - **direction** (string): 公會方向
    - 選項: `withLength16`
    - 條件: `hasGuild == true`
  - **vicepresidentCount** (uint8): 副會長數量
    - 條件: `hasGuild == true`
  - **vicepresidentNames** (value_array): 副會長名稱列表
    - 選項: `type=withLength16,count={vicepresidentCount}`
    - 條件: `hasGuild == true`
  - **memberCount** (uint16): 成員數量
    - 選項: `bigEndian`
    - 條件: `hasGuild == true`
  - **members** (array): 成員列表
    - 選項: `count={memberCount}`
    - 條件: `hasGuild == true`
    - 子字段:
      - **username** (string): 用戶名稱
      - **value** (uint32): 貢獻點數
      - **online** (uint8): 在線狀態

### GuildLeave

- **描述**: 公會離開響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [50]
  - isFeature: [192]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildMemberLeaveNotify

- **描述**: 公會成員離開通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [84]
  - isFeature: [207]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **name** (string): 用戶名稱
    - 選項: `withLength16`

### GuildMemberOfflineNotify

- **描述**: 公會成員離線通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [80]
  - isFeature: [203]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildMemberOnlineNotify

- **描述**: 公會成員上線通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [79]
  - isFeature: [202]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildMessageTimeoutNotify

- **描述**: 公會消息超時通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [71]
  - isFeature: [195]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **time** (ulong): 時間
    - 選項: `littleEndian`

### GuildPointClearNotify

- **描述**: 公會點數清零通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [87]
  - isFeature: [210]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildPointRefreshNotify

- **描述**: 公會點數刷新通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [86]
  - isFeature: [209]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **pointcount** (uint8): 點數類型數量
  - **points** (value_array): 點數列表
    - 選項: `type=uint32,count={pointcount}`
  - **username** (string): 用戶名稱
    - 選項: `withLength16`
  - **userpoint** (uint32): 用戶點數
    - 選項: `littleEndian`

### GuildRejectMembership

- **描述**: 公會拒絕加入響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [46]
  - isFeature: [188]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildRemoveApplicationNotify

- **描述**: 公會移除申請響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [74]
  - isFeature: [197]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildRemoveMemberNotify

- **描述**: 公會移除成員通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [76]
  - isFeature: [199]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildRemoveMembership

- **描述**: 公會移除成員響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [47]
  - isFeature: [189]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildRemoveVicePresident

- **描述**: 公會移除副會長響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [49]
  - isFeature: [209]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **success** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **response** (string): 回應
    - 選項: `withLength16`

### GuildRemoveVicePresidentNotify

- **描述**: 公會移除副會長通知響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [78]
  - isFeature: [201]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **response** (string): 回應
    - 選項: `withLength16`

### HealthyCheck

- **描述**: 健康檢查
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60891, 60903]
  - typeArray: [0, 99]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **_year** (uint8): 民國年
  - **_month** (uint8): 月
  - **_day** (uint8): 日
  - **_hour** (uint8): 時
  - **_minute** (uint8): 分
  - **_second** (uint8): 秒
  - **_fullYear** (derived): 西元年
    - 選項: `transform=_year+1911`
  - **serverTime** (derived): 伺服器時間
    - 選項: `transform=function(){var d=new Date();d.setFullYear(_fullYear);d.setMonth(_month-1);d.setDate(_day);d.setHours(_hour);d.setMinutes(_minute);d.setSeconds(_second);d.setMilliseconds(0);return d}()`

### Info

- **描述**: 銀行資訊響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60002
  - typeArray: [1]
  - isFeature: [81]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **_bankData** (string): 銀行數據
    - 選項: `utf16,remaining`
- **後處理邏輯**:
  - 字段 `money`: `parseInt(_bankData.split(',')[0])`
  - 字段 `point`: `parseInt(_bankData.split(',')[1])`
  - 字段 `rewardMoney`: `parseInt(_bankData.split(',')[2])`
  - 字段 `safe`: `parseInt(_bankData.split(',')[3])`
  - 字段 `payMonthlyExpireAt`: `_bankData.split(',')[4]`
  - 字段 `rebateCardExpireAt`: `_bankData.split(',')[5]`
  - 字段 `isEnableQpp`: `_bankData.split(',')[6]==1`

### LoginFailed

- **描述**: 登入失敗
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 22]
  - isFeature: [10, 30]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`

### LoginRecord

- **描述**: 登入記錄
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 151]
  - isFeature: [13]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **_count** (uint16): 記錄數量
  - **records** (array): 記錄列表
    - 選項: `count={_count}`
    - 子字段:
      - **_rawData** (string): 原始資料
      - **_parts** (derived): 分割資料
      - **date** (derived): 日期
      - **ip** (derived): IP位址
      - **device** (derived): 裝置

### LoginSucceed

- **描述**: 登入成功
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [0, 1]
  - isFeature: [11]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **value** (ulong): 值
    - 選項: `littleEndian`

### LoginVerifiedMessage

- **描述**: 驗證訊息
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 135]
  - isFeature: [10, 20, 30, 91, 70]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **title** (string): 標題
    - 選項: `withLength16`
  - **message** (string): 訊息
    - 選項: `withLength16`
  - **action** (uint8): 動作

### Logout

- **描述**: 登出
- **識別信息**:
  - protocolId: 33
  - serviceId: 0
  - typeArray: [0, 0]
  - ignoreServiceId: True
  - ignoreTypeArray: True
  - isFeature: [20]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`

### MailCoinLimit

- **描述**: 郵件金額限制
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 254]
  - isFeature: [75]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **coinLimit** (ulong): 金額限制
    - 選項: `littleEndian`

### MailDelete

- **描述**: 郵件刪除
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 3]
  - isFeature: [74]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **mailId** (ulong): 郵件ID
    - 選項: `littleEndian`

### MailDeleteAll

- **描述**: 郵件刪除所有
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 6]
  - isFeature: [74]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **isSuccess** (uint8): 是否成功
    - 選項: `transform=value==1`
  - **message** (string): 回應
    - 選項: `remaining`

### MailGetNew

- **描述**: 郵件獲取
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 11]
  - isFeature: [71]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **mailId** (ulong): 郵件ID
    - 選項: `littleEndian`
  - **from** (string): 寄件者
    - 選項: `withLength16`
  - **expire** (ulong): 期限
    - 選項: `littleEndian`
  - **title** (string): 標題
    - 選項: `withLength16`
  - **content** (string): 內容
    - 選項: `withLength16`
  - **link** (string): 連結
    - 選項: `withLength16`
  - **itemType** (ulong): 物品類型
    - 選項: `littleEndian`
  - **count** (uint32): 數量
    - 選項: `bigEndian`
  - **status** (uint8): 狀態
  - **isRead** (derived): 是否已讀
    - 選項: `transform=status!=0`
  - **creatDate** (ulong): 發信時間
    - 選項: `littleEndian`
  - **itemsLength** (uint8): 物品數量
  - **items** (array): 物品列表
    - 選項: `count={itemsLength}`
    - 子字段:
      - **id** (ulong): 物品ID
      - **count** (uint32): 數量
  - **canReceive** (derived): 是否可領取
    - 選項: `transform=externalData.arrayData.find(item => item.id === itemType.toString())?.mail.canReceive && items.every((item) => externalData.arrayData.find(dataItem => dataItem.id === item.id.toString())?.mail.canReceive)`
- **後處理邏輯**:
  - 字段 `expire`: `expire ? new Date(Date.UTC(1601, 0, 1) + (+expire.toString().substring(0, expire.toString().length - 4))) : null`
  - 字段 `creatDate`: `creatDate ? new Date(Date.UTC(1601, 0, 1) + (+creatDate.toString().substring(0, creatDate.toString().length - 4))) : null`

### MiniGameAnswer

- **描述**: 小遊戲答題響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 70, 3]
  - isFeature: [243]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 服務類型
    - 選項: `count=3`
  - **answerType** (uint8): 答題類型 1:次數已滿, 2:答題正確

### MiniGameClearCanvas

- **描述**: 小遊戲清除畫布封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 70, 9]
  - isFeature: [249]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 服務類型
    - 選項: `count=3`

### MiniGameEnd

- **描述**: 小遊戲結束封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 70, 4]
  - isFeature: [244]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 服務類型
    - 選項: `count=3`
  - **endType** (uint8): 結束類型 1:活動已結束, 2:等待下回合

### MiniGameForceClose

- **描述**: 小遊戲強制關閉封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 70, 10]
  - isFeature: [250]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 服務類型
    - 選項: `count=3`

### MiniGameSetHost

- **描述**: 小遊戲設置主持人封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 70, 1]
  - isFeature: [241]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 服務類型
    - 選項: `count=3`
  - **gameType** (uint8): 遊戲類型 1:搶答 2:你畫我猜
  - **unknown** (uint8): 未知
  - **answerCount** (uint8): 答案數量
  - **answers** (value_array): 答案列表
    - 選項: `type=byteCount,count={answerCount}`

### MiniGameStart

- **描述**: 小遊戲開始封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 70, 2]
  - isFeature: [242]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 服務類型
    - 選項: `count=3`
  - **gameType** (uint8): 遊戲類型 1:搶答 2:你畫我猜
  - **countDownTime** (uint8): 倒計時
  - **moderatorLength** (uint8): 用戶名長度
  - **moderator** (string): 主持人
    - 選項: `utf16,length={moderatorLength}`
  - **headingLength** (uint8): 用戶名長度
    - 條件: `gameType == 1`
  - **heading** (string): 標題
    - 選項: `utf16,length={headingLength}`
    - 條件: `gameType == 1`

### MiniGameUpdateCanvas

- **描述**: 小遊戲畫布更新封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 70, 8]
  - isFeature: [248]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 服務類型
    - 選項: `count=3`
  - **pointSize** (uint16): 點數量
    - 選項: `bigEndian`
  - **answers** (array): 答案列表
    - 選項: `count={pointSize}`
    - 子字段:
      - **x** (uint8): X座標
      - **y** (uint8): Y座標
      - **color** (uint8): 顏色

### NeedToAddCharacter

- **描述**: 需要新增角色
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 132]
  - isFeature: [10]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **accountType** (uint8): 帳號類型
  - **providerUserId** (string): 提供者用戶ID
    - 選項: `withLength16`
  - **_asciiString** (string): ASCII字串
    - 選項: `withLength16`
    - 條件: `accountType === 2`
  - **providerUsername** (derived): 提供者用戶名
    - 選項: `transform=function(value) { return JSON.parse('"' + _asciiString + '"'); }()`
    - 條件: `accountType === 2`

### OtpMessage

- **描述**: OTP訊息
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 136]
  - isFeature: [10, 30]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **title** (string): 標題
    - 選項: `withLength16`
  - **message** (string): 訊息
    - 選項: `withLength16`
  - **_skip** (bytes): 跳過2位元組
    - 選項: `length=2`
  - **_otherType** (uint8): OTP類型
  - **_otherImput** (uint8): OTP輸入
  - **_otherSmsType** (uint8): 簡訊類型
  - **_otherIsSuccessed** (uint8): 是否成功
    - 條件: `_otherSmsType === 2`
  - **_otherToken** (string): Token
    - 選項: `withLength16`
    - 條件: `_otherSmsType === 2 && _otherIsSuccessed === 1`
- **後處理邏輯**:
  - 字段 `other`: `function() { var o = { type: _otherType, imput: _otherImput, smsType: _otherSmsType, isSuccessed: false, token: null }; if (_otherSmsType === 2) { o.isSuccessed = _otherIsSuccessed === 1; if (_otherIsSuccessed === 1 && _otherToken) { o.token = _otherToken; } } return o; }()`

### PasswordType

- **描述**: 密碼類型響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60002
  - typeArray: [5]
  - isFeature: [82]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **passwordType** (uint8): 密碼類型

### PaymentUrl

- **描述**: 支付URL
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 15, 20]
  - isFeature: [83]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **isSuccess** (uint8): 是否成功
    - 選項: `transform=value==1`
  - **url** (string): URL
    - 選項: `withLength16`
    - 條件: `isSuccess===true`
  - **message** (string): 訊息
    - 選項: `remaining`
    - 條件: `isSuccess===false`

### PaymentUrlForVip

- **描述**: 支付URL
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 15, 21]
  - isFeature: [83]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **isSuccess** (uint8): 是否成功
    - 選項: `transform=value==1`
  - **discount** (uint8): 折扣
    - 條件: `isSuccess===true`
  - **url** (string): URL
    - 選項: `withLength16`
    - 條件: `isSuccess===true`
  - **message** (string): 訊息
    - 選項: `remaining`
    - 條件: `isSuccess===false`

### PersonalBadge

- **描述**: 個人徽章響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60904
  - typeArray: [9]
  - isFeature: [47]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **skip** (uint8): 數量  總部說跳過
  - **badges** (array): 徽章列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **id** (long): 徽章ID
      - **properties** (uint8): 屬性數量
      - **propertyDetails** (value_array): 屬性詳情

### Ranking

- **描述**: 排行榜
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 15, 10]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **rankType** (uint8): 排行榜類型
  - **isEmpty** (uint8): 是否為空
    - 選項: `transform=value!=1`
  - **_count** (derived): 數量
    - 選項: `transform=0`
  - **list** (array): 排行榜列表
    - 選項: `while=reader.offset < packetSize`
    - 子字段:
      - **position** (derived): 位置
      - **name** (string): 名稱
      - **winLose** (long): 勝負
      - **win** (ulong): 勝利
      - **bet** (ulong): 投注

### ReadAllMail

- **描述**: 閱讀所有郵件
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 12]
  - isFeature: [73]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **mailIds** (value_array): 郵件ID列表
    - 選項: `type=ulong,while=reader.offset < packetSize,littleEndian`

### ReadMail

- **描述**: 閱讀郵件
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 2]
  - isFeature: [73]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **mailId** (ulong): 郵件ID
    - 選項: `littleEndian`
  - **isRead** (uint8): 是否已讀
    - 選項: `transform=value!=0`
  - **message** (string): 回應
    - 選項: `remaining`

### ReconnectionSuccessed

- **描述**: 重新連接成功
- **識別信息**:
  - protocolId: 113
  - serviceId: 0
  - typeArray: [0, 0]
  - ignoreServiceId: True
  - ignoreTypeArray: True
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **clientId** (uint32): 連線ID
    - 選項: `bigEndian`
  - **token** (uint32): token
    - 選項: `bigEndian`

### RedeemCode

- **描述**: 兌換碼
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [186]
  - isFeature: [151]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **kind** (uint8): 類型

### RedeemGift

- **描述**: 兌換禮物
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 4, 2]
  - isFeature: [152]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **coin** (ulong): 金額
    - 選項: `littleEndian`

### RedeemProperty

- **描述**: 兌換禮物
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 187, 12]
  - isFeature: [151]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **_itemsLength** (uint8): 列表長度
  - **_items** (array): 列表
    - 選項: `count={_itemsLength}`
    - 子字段:
      - **id** (uint8): 道具ID
      - **count** (uint8): 數量

### RejoinedService

- **描述**: 重新加入服務
- **識別信息**:
  - protocolId: 34
  - serviceId: 0
  - typeArray: [0, 0]
  - ignoreServiceId: True
  - ignoreTypeArray: True
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **sendId** (uint16): 最後請求ID
    - 選項: `bigEndian`

### SafeBox

- **描述**: 保險箱操作響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60002
  - typeArray: [33]
  - isFeature: [88, 89]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **safe** (ulong): 保險箱額度
    - 選項: `littleEndian`
  - **money** (ulong): 星幣
    - 選項: `littleEndian`

### SendMail

- **描述**: 發送郵件
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [6000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 170, 4]
  - isFeature: [72]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **isSuccess** (uint8): 是否成功
    - 選項: `transform=value==1`
  - **message** (string): 回應
    - 選項: `remaining`

### SetPassword

- **描述**: 設定密碼
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [250, 139]
  - isFeature: [30]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`

### SuccessfullyStored

- **描述**: 成功儲存點數響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60002
  - typeArray: [50]
  - typeArrayAliases: [[51]]
  - isFeature: [86]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **storeType** (uint8): 儲存類型
  - **storedPoint** (uint24): 已儲存點數
    - 選項: `bigEndian`
    - 條件: `storeType !== 99`
  - **point** (uint24): 總點數
    - 選項: `bigEndian`
    - 條件: `storeType !== 99`

### SystemMessage

- **描述**: 系統訊息
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - aliases: [60002, 60891, 60901, 60902, 60903]
  - typeArray: [0, 253]
  - typeArrayAliases: [[0, 102]]
  - isFeature: [51, 52, 53, 54, 87, 102, 151]
  - skipTypeZero: False
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **code** (uint16): 代碼
  - **message** (string): 訊息
    - 選項: `remaining`

### UpdateCopper

- **描述**: 更新銅幣
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903, 60891]
  - typeArray: [0, 4, 9]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **copper** (ulong): 銅幣
    - 選項: `littleEndian`

### UpdateGameId

- **描述**: 更新遊戲ID
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903]
  - typeArray: [0, 4, 7]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **gameId** (uint16): 遊戲ID
    - 選項: `bigEndian`

### UpdateGameState

- **描述**: 更新遊戲狀態
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [0, 999]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **isEnable** (uint8): 是否啟用
    - 選項: `transform=value==1`
  - **gameId** (uint8): 遊戲ID

### UpdateLevel

- **描述**: 更新等級
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903, 60891]
  - typeArray: [0, 4, 4]
  - isFeature: [42]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **level** (uint32): 等級
    - 選項: `bigEndian`

### UpdateMoney

- **描述**: 更新金錢
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903, 60891]
  - typeArray: [0, 4, 5]
  - isFeature: [43]
  - skipTypeZero: False
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **money** (ulong): 金錢
    - 選項: `littleEndian`
  - **silver** (ulong): 銀幣
    - 選項: `littleEndian`

### UpdateReferralMoney

- **描述**: 更新推薦金錢
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903]
  - typeArray: [0, 4, 8]
  - isFeature: [14]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **referralMoney** (ulong): 推薦金錢
    - 選項: `littleEndian`

### UpdateUserCoinItem

- **描述**: 更新用戶代幣類道具
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - aliases: [60000, 60901, 60902, 60891, 60904]
  - typeArray: [0, 48, 3]
  - isFeature: [77]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **id** (ulong): 道具ID
    - 選項: `littleEndian`
  - **count** (ulong): 數量
    - 選項: `littleEndian`

### UpdateUserStatusItem

- **描述**: 更新用戶狀態類道具
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 4, 41]
  - isFeature: [50]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **id** (uint16): 道具ID
    - 選項: `bigEndian`
  - **count** (uint16): 數量
    - 選項: `bigEndian`

### InitStatusInventory

- **描述**: 初始化狀態道具清單封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - typeArray: [0, 4, 99]
  - isFeature: [48]
  - skipTypeZero: False
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **items** (array): 道具清單
    - 選項: `while=!reader.isEOF()`
    - 子字段:
      - **id** (uint16): 道具ID
        - 選項: `bigEndian`
      - **count** (uint24): 數量
        - 選項: `bigEndian`

### getGameInventory

- **描述**: 取得遊戲道具清單封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60000
  - aliases: [60901, 60902, 60903, 60904, 60905, 60891]
  - typeArray: [0, 4, 100]
  - isFeature: [49]
  - skipTypeZero: False
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **_count** (uint16): 道具數量
    - 選項: `bigEndian`
  - **items** (array): 道具清單
    - 選項: `count={_count}`
    - 子字段:
      - **date** (ulong): 取得日期
        - 選項: `littleEndian, transform=function(){return new Date(value).toISOString()}()`
      - **id** (uint16): 道具ID
        - 選項: `bigEndian`
      - **cardType** (uint8): 卡片類型
      - **coinType** (uint8): 貨幣類型
      - **betAmount** (uint24): 投注金額
        - 選項: `bigEndian`

### UpdateVip

- **描述**: 更新VIP
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60901, 60903, 60891]
  - typeArray: [0, 98]
  - isFeature: [44]
  - skipTypeZero: False
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **_parameters** (array): 參數列表
    - 選項: `while=!reader.isEOF()`
    - 子字段:
      - **id** (uint16): 參數ID
      - **value** (string): 參數值
- **後處理邏輯**:
  - 字段 `level`: `_parameters.find(p => p.id === 1)?.value`
  - 字段 `money`: `_parameters.find(p => p.id === 2)?.value`
  - 字段 `silver`: `_parameters.find(p => p.id === 3)?.value`
  - 字段 `referralMonany`: `_parameters.find(p => p.id === 4)?.value`
  - 字段 `safed`: `_parameters.find(p => p.id === 5)?.value`
  - 字段 `point`: `_parameters.find(p => p.id === 6)?.value`
  - 字段 `activeValue`: `parseInt(_parameters.find(p => p.id === 7)?.value)`
  - 字段 `honor`: `_parameters.find(p => p.id === 8)?.value`
  - 字段 `vip`: `parseInt(_parameters.find(p => p.id === 9)?.value)`
  - 字段 `pkValue`: `_parameters.find(p => p.id === 10)?.value`

### UseGashCard

- **描述**: Gash卡訊息響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 186, 52]
  - isFeature: [85]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **isSuccess** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **storedPoint** (uint32): 已儲存點數
    - 選項: `littleEndian`
    - 條件: `isSuccess === true`
  - **point** (uint32): 總點數
    - 選項: `littleEndian`
    - 條件: `isSuccess === true`
  - **message** (string): 消息內容
    - 選項: `withLength16`
    - 條件: `isSuccess === false`
  - **isGash** (derived): 是否為Gash卡
    - 選項: `transform=true`

### UserDetail

- **描述**: 用戶詳細資訊
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 15, 15]
  - isFeature: [40]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **username** (string): 用戶名
    - 選項: `withLength16`
  - **online** (uint8): 是否在線
    - 選項: `transform=value==1`
  - **accountType** (uint8): 賬號類型
    - 條件: `online===true`
  - **rank** (uint8): 等級
    - 條件: `online===true`
  - **level** (uint32): 等級
    - 選項: `bigEndian`
    - 條件: `online===true`
  - **money** (ulong): 金幣
    - 選項: `littleEndian`
    - 條件: `online===true`
  - **silver** (ulong): 銀幣
    - 選項: `littleEndian`
    - 條件: `online===true`
  - **gameId** (uint16): 遊戲ID
    - 選項: `bigEndian`
    - 條件: `online===true`
  - **platformId** (uint8): 平台ID
    - 條件: `online===true`
  - **levelVip** (uint8): VIP等級
    - 條件: `online===true`
  - **isLevelUpVipFancyDiamondEnabled** (uint8): 是否可升級為7級
    - 選項: `transform=value==1`
    - 條件: `online===true`
  - **discount** (uint8): 折扣
    - 條件: `online===true`
  - **limit** (uint32): 限額
    - 選項: `bigEndian`
    - 條件: `online===true`
  - **activeValue** (uint32): 活耀值
    - 選項: `bigEndian`
    - 條件: `online===true`
  - **honor** (uint32): 榮譽值
    - 選項: `bigEndian`
    - 條件: `online===true`
  - **isBind** (uint8): 是否綁定
    - 選項: `transform=value==1`
    - 條件: `online===true`
  - **isEnableSafetyCode** (uint8): 是否啟用安全碼
    - 選項: `transform=value==1`
    - 條件: `online===true && isBind===true`
  - **phoneNumber** (string): 手機號碼
    - 選項: `withLength16`
    - 條件: `online===true && isBind===true`
  - **phoneNumber** (derived): 手機號碼
    - 選項: `transform=''`
    - 條件: `online===false || isBind===false`

### UserInfo

- **描述**: 用戶資訊
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - aliases: [60000, 60901, 60903]
  - typeArray: [0, 4, 1]
  - isFeature: [10, 14]
  - skipTypeZero: False
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **_usernameSize** (uint8): 用戶名大小
  - **_rawUsername** (string): 原始用戶名
    - 選項: `utf16,length={_usernameSize*2}`
  - **_rawRank** (uint8): 原始等級
  - **_isHighRank** (derived): 是否高等級
    - 選項: `transform=_rawRank>=100`
  - **rank** (derived): 等級
    - 選項: `transform=_isHighRank?_rawRank-100:_rawRank`
  - **_usernameParts** (derived): 用戶名部分
    - 選項: `transform=_rawUsername.split('\t')`
  - **username** (derived): 用戶名
    - 選項: `transform=_isHighRank?_usernameParts[0]:_rawUsername`
  - **facebookId** (derived): Facebook ID
    - 選項: `transform=_isHighRank&&_usernameParts.length>1?_usernameParts[1].substring(2):''`
  - **level** (uint32): 等級
    - 選項: `bigEndian`
  - **money** (ulong): 金幣
    - 選項: `littleEndian`
  - **silver** (ulong): 銀幣
    - 選項: `littleEndian`
  - **referralMonany** (ulong): 推薦金錢
    - 選項: `littleEndian`
  - **referral** (string): 推薦人
    - 選項: `withLength16`
  - **phoneNumber** (string): 手機號碼
    - 選項: `withLength16`
  - **lastLogoutTime** (string): 最後登出時間
    - 選項: `withLength16`
  - **password** (uint8): 密碼
  - **liveMaster** (uint8): 直播主
  - **levelVip** (uint8): VIP等級
  - **loginType** (uint8): 登入類型
  - **coinCount** (uint16): 金幣數量
    - 選項: `bigEndian`
  - **coins** (array): 金幣列表
    - 選項: `count={coinCount+1}`
    - 子字段:
      - **id** (ulong): ID
      - **balance** (ulong): 金幣

### UserKey

- **描述**: 用戶金鑰
- **識別信息**:
  - protocolId: 30
  - serviceId: 60891
  - aliases: [60902]
  - typeArray: [0, 116]
  - isFeature: [54]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=2`
  - **key** (string): 金鑰
    - 選項: `withLength16`
  - **userAlias** (string): 用戶別名
    - 選項: `withLength16`

### Withdraw

- **描述**: 提款
- **識別信息**:
  - protocolId: 30
  - serviceId: 60902
  - typeArray: [107, 4]
  - isFeature: [52]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=3`
  - **providerId** (uint32): 提供者ID
    - 選項: `bigEndian`
  - **amount** (ulong): 金額
    - 選項: `littleEndian`
  - **balance** (ulong): 餘額
    - 選項: `littleEndian`

### XinToMahjong

- **描述**: 星幣轉換麻將
- **識別信息**:
  - protocolId: 30
  - serviceId: 60903
  - typeArray: [0, 170, 8, 2]
  - isFeature: [76]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=4`
  - **state** (uint8): 狀態
  - **msg** (string): 訊息
    - 選項: `withLength16`
    - 條件: `state===0`

### YoeCardMessage

- **描述**: Yoe卡訊息響應封包
- **識別信息**:
  - protocolId: 30
  - serviceId: 60002
  - typeArray: [49]
  - isFeature: [85]
- **字段列表**:
  - **packetSize** (uint24): 封包長度
    - 選項: `bigEndian`
  - **protocolId** (uint8): 協議ID
  - **serviceId** (uint16): 服務ID
    - 選項: `bigEndian`
  - **serial** (uint16): 序列號
    - 選項: `bigEndian`
  - **serviceType** (value_array): 類型
    - 選項: `count=1`
  - **isSuccess** (uint8): 成功標誌
    - 選項: `transform=value==1`
  - **message** (string): 消息內容
    - 選項: `remaining`
