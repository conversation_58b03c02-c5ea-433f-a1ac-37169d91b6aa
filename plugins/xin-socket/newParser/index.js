const PacketManager = require('./src/packet_manager')
const packetDefinitions = require('./packet_definitions.json')

class PacketSystem {
  constructor() {
    this.packetManager = null
  }

  async initialize() {
    try {
      this.packetManager = new PacketManager()
      this.packetManager.loadDefinitions(packetDefinitions)

      console.log('Packet system initialized')
      console.log('Definitions version:', this.packetManager.getDefinitionsVersion())
      console.log('Available packets:', this.packetManager.getLoadedPacketNames())

      return this
    } catch (error) {
      console.error('Failed to initialize packet system:', error)
      throw error
    }
  }

  parse(buffer) {
    return this.packetManager.autoparse(buffer)
  }

  parseByName(name, buffer) {
    return this.packetManager.parse(name, buffer)
  }
}

// 導出單例
let instance = null
async function getInstance() {
  if (!instance) {
    instance = await new PacketSystem().initialize()
  }
  return instance
}

module.exports = {
  getInstance,
  PacketSystem
}
