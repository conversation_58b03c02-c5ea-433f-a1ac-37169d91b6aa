/**
 * 純 JavaScript 實現的 MD5 函數
 * 來源: <PERSON> 的 MD5 實現，經過修改以支持 UTF-8
 * @param {string} input - 要計算 MD5 的輸入字符串
 * @return {string} 32 位小寫 MD5 哈希值
 */
function md5(input) {
  // 首先將輸入字符串轉換為 UTF-8 字節數組
  let bytes
  if (typeof TextEncoder !== 'undefined') {
    // 使用 TextEncoder 進行 UTF-8 編碼
    const encoder = new TextEncoder()
    bytes = encoder.encode(input)
  } else {
    // 手動實現 UTF-8 編碼
    bytes = []
    for (let i = 0; i < input.length; i++) {
      const c = input.charCodeAt(i)
      if (c < 0x80) {
        bytes.push(c)
      } else if (c < 0x800) {
        bytes.push(0xc0 | (c >> 6), 0x80 | (c & 0x3f))
      } else if (c < 0xd800 || c >= 0xe000) {
        bytes.push(0xe0 | (c >> 12), 0x80 | ((c >> 6) & 0x3f), 0x80 | (c & 0x3f))
      } else {
        // 處理 UTF-16 代理對
        i++
        const c2 = input.charCodeAt(i)
        const cp = (0x10000 + ((c & 0x3ff) << 10)) | (c2 & 0x3ff)
        bytes.push(
          0xf0 | (cp >> 18),
          0x80 | ((cp >> 12) & 0x3f),
          0x80 | ((cp >> 6) & 0x3f),
          0x80 | (cp & 0x3f)
        )
      }
    }
  }

  // 直接使用字節數組進行 MD5 計算，而不轉換為字符串
  return md5ByteArray(bytes)
}

/**
 * 對字節數組進行 MD5 哈希計算
 * @param {Uint8Array|Array} bytes - 輸入字節數組
 * @return {string} 32 位小寫 MD5 哈希值
 */
function md5ByteArray(bytes) {
  // MD5 算法工作在 32 位整數上，我們需要將字節數組轉換為 32 位整數數組
  const blocks = []
  let i

  // 將字節數組分成 16 個 32 位塊（每塊 4 字節）
  for (i = 0; i < bytes.length; i += 4) {
    blocks[i >> 2] =
      (bytes[i] || 0) |
      ((bytes[i + 1] || 0) << 8) |
      ((bytes[i + 2] || 0) << 16) |
      ((bytes[i + 3] || 0) << 24)
  }

  // 以下是 Joseph Myers 的 MD5 實現
  function add32(a, b) {
    return (a + b) & 0xffffffff
  }

  function cmn(q, a, b, x, s, t) {
    a = add32(add32(a, q), add32(x, t))
    return add32((a << s) | (a >>> (32 - s)), b)
  }

  function ff(a, b, c, d, x, s, t) {
    return cmn((b & c) | (~b & d), a, b, x, s, t)
  }

  function gg(a, b, c, d, x, s, t) {
    return cmn((b & d) | (c & ~d), a, b, x, s, t)
  }

  function hh(a, b, c, d, x, s, t) {
    return cmn(b ^ c ^ d, a, b, x, s, t)
  }

  function ii(a, b, c, d, x, s, t) {
    return cmn(c ^ (b | ~d), a, b, x, s, t)
  }

  function md5cycle(x, k) {
    let a = x[0],
      b = x[1],
      c = x[2],
      d = x[3]

    a = ff(a, b, c, d, k[0], 7, -680876936)
    d = ff(d, a, b, c, k[1], 12, -389564586)
    c = ff(c, d, a, b, k[2], 17, 606105819)
    b = ff(b, c, d, a, k[3], 22, -1044525330)
    a = ff(a, b, c, d, k[4], 7, -176418897)
    d = ff(d, a, b, c, k[5], 12, 1200080426)
    c = ff(c, d, a, b, k[6], 17, -1473231341)
    b = ff(b, c, d, a, k[7], 22, -45705983)
    a = ff(a, b, c, d, k[8], 7, 1770035416)
    d = ff(d, a, b, c, k[9], 12, -1958414417)
    c = ff(c, d, a, b, k[10], 17, -42063)
    b = ff(b, c, d, a, k[11], 22, -1990404162)
    a = ff(a, b, c, d, k[12], 7, 1804603682)
    d = ff(d, a, b, c, k[13], 12, -40341101)
    c = ff(c, d, a, b, k[14], 17, -1502002290)
    b = ff(b, c, d, a, k[15], 22, 1236535329)

    a = gg(a, b, c, d, k[1], 5, -165796510)
    d = gg(d, a, b, c, k[6], 9, -1069501632)
    c = gg(c, d, a, b, k[11], 14, 643717713)
    b = gg(b, c, d, a, k[0], 20, -373897302)
    a = gg(a, b, c, d, k[5], 5, -701558691)
    d = gg(d, a, b, c, k[10], 9, 38016083)
    c = gg(c, d, a, b, k[15], 14, -660478335)
    b = gg(b, c, d, a, k[4], 20, -405537848)
    a = gg(a, b, c, d, k[9], 5, 568446438)
    d = gg(d, a, b, c, k[14], 9, -1019803690)
    c = gg(c, d, a, b, k[3], 14, -187363961)
    b = gg(b, c, d, a, k[8], 20, 1163531501)
    a = gg(a, b, c, d, k[13], 5, -1444681467)
    d = gg(d, a, b, c, k[2], 9, -51403784)
    c = gg(c, d, a, b, k[7], 14, 1735328473)
    b = gg(b, c, d, a, k[12], 20, -1926607734)

    a = hh(a, b, c, d, k[5], 4, -378558)
    d = hh(d, a, b, c, k[8], 11, -2022574463)
    c = hh(c, d, a, b, k[11], 16, 1839030562)
    b = hh(b, c, d, a, k[14], 23, -35309556)
    a = hh(a, b, c, d, k[1], 4, -1530992060)
    d = hh(d, a, b, c, k[4], 11, 1272893353)
    c = hh(c, d, a, b, k[7], 16, -155497632)
    b = hh(b, c, d, a, k[10], 23, -1094730640)
    a = hh(a, b, c, d, k[13], 4, 681279174)
    d = hh(d, a, b, c, k[0], 11, -358537222)
    c = hh(c, d, a, b, k[3], 16, -722521979)
    b = hh(b, c, d, a, k[6], 23, 76029189)
    a = hh(a, b, c, d, k[9], 4, -640364487)
    d = hh(d, a, b, c, k[12], 11, -421815835)
    c = hh(c, d, a, b, k[15], 16, 530742520)
    b = hh(b, c, d, a, k[2], 23, -995338651)

    a = ii(a, b, c, d, k[0], 6, -198630844)
    d = ii(d, a, b, c, k[7], 10, 1126891415)
    c = ii(c, d, a, b, k[14], 15, -1416354905)
    b = ii(b, c, d, a, k[5], 21, -57434055)
    a = ii(a, b, c, d, k[12], 6, 1700485571)
    d = ii(d, a, b, c, k[3], 10, -1894986606)
    c = ii(c, d, a, b, k[10], 15, -1051523)
    b = ii(b, c, d, a, k[1], 21, -2054922799)
    a = ii(a, b, c, d, k[8], 6, 1873313359)
    d = ii(d, a, b, c, k[15], 10, -30611744)
    c = ii(c, d, a, b, k[6], 15, -1560198380)
    b = ii(b, c, d, a, k[13], 21, 1309151649)
    a = ii(a, b, c, d, k[4], 6, -145523070)
    d = ii(d, a, b, c, k[11], 10, -1120210379)
    c = ii(c, d, a, b, k[2], 15, 718787259)
    b = ii(b, c, d, a, k[9], 21, -343485551)

    x[0] = add32(a, x[0])
    x[1] = add32(b, x[1])
    x[2] = add32(c, x[2])
    x[3] = add32(d, x[3])
  }

  const state = [1732584193, -271733879, -1732584194, 271733878]

  // 處理完整的 64 字節塊
  const blockCount = Math.floor(bytes.length / 64)
  for (i = 0; i < blockCount; i++) {
    const block = new Array(16)
    for (let j = 0; j < 16; j++) {
      const offset = i * 64 + j * 4
      block[j] =
        (bytes[offset] || 0) |
        ((bytes[offset + 1] || 0) << 8) |
        ((bytes[offset + 2] || 0) << 16) |
        ((bytes[offset + 3] || 0) << 24)
    }
    md5cycle(state, block)
  }

  // 處理剩餘的字節
  const remainder = bytes.slice(blockCount * 64)

  // 創建填充後的塊
  const paddedBlock = new Array(16).fill(0)

  // 複製餘下的字節到填充塊
  for (i = 0; i < remainder.length; i++) {
    paddedBlock[i >> 2] |= remainder[i] << (i % 4 << 3)
  }

  // 添加終止符 0x80
  paddedBlock[i >> 2] |= 0x80 << (i % 4 << 3)

  // 如果沒有足夠的空間放置長度，需要處理這個塊並創建一個新的
  if (remainder.length > 55) {
    md5cycle(state, paddedBlock)
    paddedBlock.fill(0)
  }

  // 添加原始消息的長度（以位為單位）作為 64 位整數
  const bitLength = bytes.length * 8
  paddedBlock[14] = bitLength & 0xffffffff
  paddedBlock[15] = Math.floor(bitLength / 0x100000000)

  md5cycle(state, paddedBlock)

  // 將結果轉換為十六進制字符串
  let result = ''
  for (i = 0; i < 4; i++) {
    for (let j = 0; j < 4; j++) {
      const byte = (state[i] >> (j * 8)) & 0xff
      result += (byte < 16 ? '0' : '') + byte.toString(16)
    }
  }

  return result
}

// 只保留導出功能
if (typeof module !== 'undefined' && module.exports) {
  module.exports = md5
} else if (typeof self !== 'undefined') {
  self.md5 = md5
}
