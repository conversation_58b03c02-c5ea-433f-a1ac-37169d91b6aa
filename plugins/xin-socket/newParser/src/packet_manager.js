const PacketParser = require('./packet_parser')
const Reader = require('./reader')

// 預緩存常用鍵值和標識符
const PACKET_TYPES = {
  UNKNOWN: 'unknown_packet',
  UNDEFINED: 'undefined_packet'
}

const PACKET_DESC = {
  UNKNOWN: '未知的封包',
  UNDEFINED: '未定義的封包'
}

class PacketManager {
  /**
   * @param {Boolean} [isDebug=false] - Enable debug mode.
   */
  constructor(isDebug = false, protocolConfig = null) {
    this.version = ''
    this.packetDefinitions = new Map()
    this.parsers = new Map()
    this.serviceIdMap = new Map()
    this.isDebug = isDebug // Explicitly assign the parameter with default value false
    this.parsePacketName = ''
    this._identifyKeyCache = new Map()
    this.protocolConfig = protocolConfig
    this._initDefaultDefinitions()
    if (this.isDebug) {
      console.debug('PacketManager initialized in debug mode')
    }
  }

  _initDefaultDefinitions() {
    const COMMON_IDENTIFY = Object.freeze({
      protocolId: -1,
      serviceId: -1,
      ignoreServiceId: true,
      ignoreTypeArray: true
    })

    const undefinedPacketFields = Array(8)
    undefinedPacketFields[0] = {
      name: 'packetpacketSize',
      type: 'uint24',
      options: 'bigEndian'
    }
    undefinedPacketFields[1] = {
      name: 'protocolId',
      type: 'uint8',
      options: ''
    }
    undefinedPacketFields[2] = {
      name: 'serviceId',
      type: 'uint16',
      options: 'bigEndian'
    }
    undefinedPacketFields[3] = {
      name: 'serial',
      type: 'uint16',
      options: 'bigEndian',
      condition: 'reader.EOFCount() >= 2'
    }
    undefinedPacketFields[4] = {
      name: 'type',
      type: 'value_array',
      options: 'count=1',
      condition: 'reader.EOFCount() === 1'
    }

    undefinedPacketFields[5] = {
      name: 'type',
      type: 'value_array',
      options: 'count=2',
      condition: 'reader.EOFCount() === 2'
    }
    undefinedPacketFields[6] = {
      name: 'type',
      type: 'value_array',
      options: 'count=3',
      condition: 'reader.EOFCount() === 3'
    }
    undefinedPacketFields[7] = {
      name: 'type',
      type: 'value_array',
      options: 'count=4',
      condition: 'reader.EOFCount() >= 4'
    }

    const undefinedPacketDefinition = {
      name: PACKET_TYPES.UNDEFINED,
      description: PACKET_DESC.UNDEFINED,
      identify: COMMON_IDENTIFY,
      fields: undefinedPacketFields
    }

    const unknownPacketFields = new Array(12)
    for (let i = 0; i < 12; i++) {
      unknownPacketFields[i] = {
        name: 'type',
        type: 'value_array',
        options: `count=${i + 1}`,
        condition: i === 11 ? 'reader.EOFCount() >= 12' : `reader.EOFCount() === ${i + 1}`
      }
    }

    const unknownPacketDefinition = {
      name: PACKET_TYPES.UNKNOWN,
      description: PACKET_DESC.UNKNOWN,
      identify: COMMON_IDENTIFY,
      fields: unknownPacketFields
    }

    this.unknownParser = new PacketParser(unknownPacketDefinition)
    this.undefinedParser = new PacketParser(undefinedPacketDefinition)
    this._resultTemplate = Object.create(null)
  }

  async loadDefinitions(definitions, externalDatas) {
    try {
      this.version = definitions.version
      if (this.isDebug) console.log(`Loading packet definitions version ${this.version}`)

      this.packetDefinitions.clear()
      this.parsers.clear()
      this.serviceIdMap.clear()
      this._identifyKeyCache.clear()

      const entries = Object.entries(definitions.packets)
      const batchSize = 50

      for (let i = 0; i < entries.length; i += batchSize) {
        const batch = entries.slice(i, i + batchSize)
        await Promise.all(
          batch.map(([name, definition]) =>
            this.loadSingleDefinition(name, definition, externalDatas)
          )
        )
      }

      if (this.isDebug) console.log(`Loaded ${this.packetDefinitions.size} packet definitions`)
      return true
    } catch (error) {
      console.error('Error loading packet definitions:', error)
      throw error
    }
  }

  async loadSingleDefinition(name, definition, externalDatas) {
    try {
      // Make a deep copy if we need to modify the definition
      let processedDefinition = definition

      // Check if we need to convert fields from array to object format
      if (
        definition.fields &&
        Array.isArray(definition.fields) &&
        definition.fields.length > 0 &&
        Array.isArray(definition.fields[0])
      ) {
        processedDefinition = JSON.parse(JSON.stringify(definition))
        processedDefinition.fields = PacketParser.convertFieldsToObjectFormat(definition.fields)
      }

      this.packetDefinitions.set(name, processedDefinition)

      let externalData = { protocolConfig: this.protocolConfig }

      if (processedDefinition.externalData) {
        const managerExternalData = externalDatas.find(
          (data) => data.path === processedDefinition.externalData.path
        )
        if (Array.isArray(managerExternalData.data)) {
          externalData.arrayData = managerExternalData.data
        } else {
          externalData = {
            ...externalData,
            ...managerExternalData.data
          }
        }
      }

      const parser = new PacketParser(processedDefinition, externalData)
      this.parsers.set(name, parser)

      const { identify } = processedDefinition
      const protocolId = identify.protocolId

      if (identify.ignoreTypeArray) {
        this._addServiceIdMapping(name, protocolId, identify.serviceId, null)
        if (identify.aliases?.length) {
          for (let i = 0; i < identify.aliases.length; i++) {
            this._addServiceIdMapping(name, protocolId, identify.aliases[i], null)
          }
        }
      } else {
        this._processTypeArrays(name, protocolId, identify)
      }
    } catch (error) {
      console.error(`Error loading packet definition ${name}:`, error)
      throw error
    }
  }

  _addServiceIdMapping(name, protocolId, serviceId, typeArray) {
    const key = this._getIdentifyKeyInternal(protocolId, serviceId, typeArray)
    this.serviceIdMap.set(key, name)
  }

  _processTypeArrays(name, protocolId, identify) {
    const mainTypeArray = identify.typeArray

    this._addServiceIdMapping(name, protocolId, identify.serviceId, mainTypeArray)

    if (identify.aliases?.length) {
      for (let i = 0; i < identify.aliases.length; i++) {
        this._addServiceIdMapping(name, protocolId, identify.aliases[i], mainTypeArray)
      }
    }

    if (identify.typeArrayAliases?.length) {
      for (let i = 0; i < identify.typeArrayAliases.length; i++) {
        const aliasTypeArray = identify.typeArrayAliases[i]

        this._addServiceIdMapping(name, protocolId, identify.serviceId, aliasTypeArray)

        if (identify.aliases?.length) {
          for (let j = 0; j < identify.aliases.length; j++) {
            this._addServiceIdMapping(name, protocolId, identify.aliases[j], aliasTypeArray)
          }
        }
      }
    }
  }

  getParserByName(name) {
    return this.parsers.get(name)
  }

  getParserByIdentify(serviceId, types) {
    const typeArray = Array.isArray(types) ? types : [types]
    const key = this._getIdentifyKeyInternal(-1, serviceId, typeArray)
    const name = this.serviceIdMap.get(key)
    return name ? this.parsers.get(name) : null
  }

  getIdentifyKey(identify) {
    return this._getIdentifyKeyInternal(identify.protocolId, identify.serviceId, identify.typeArray)
  }

  _getIdentifyKeyInternal(protocolId, serviceId, typeArray) {
    if (!typeArray) {
      return `${protocolId}_${serviceId}`
    }

    const cacheKey = `${protocolId}_${serviceId}_${typeArray.length}`
    let keyGen = this._identifyKeyCache.get(cacheKey)

    if (!keyGen) {
      if (typeArray.length === 1) {
        keyGen = (p, s, t) => `${p}_${s}_${t[0]}`
      } else if (typeArray.length === 2) {
        keyGen = (p, s, t) => `${p}_${s}_${t[0]}_${t[1]}`
      } else if (typeArray.length === 3) {
        keyGen = (p, s, t) => `${p}_${s}_${t[0]}_${t[1]}_${t[2]}`
      } else {
        keyGen = (p, s, t) => `${p}_${s}_${t.join('_')}`
      }

      if (this._identifyKeyCache.size < 100) {
        this._identifyKeyCache.set(cacheKey, keyGen)
      }
    }

    return keyGen(protocolId, serviceId, typeArray)
  }

  parse(name, buffer) {
    const parser = this.parsers.get(name)
    if (!parser) {
      throw new Error(`No parser found for packet: ${name}`)
    }
    return parser.parse(buffer)
  }

  autoparse(buffer) {
    const reader = new Reader(buffer)

    // 1. 讀取基本頭部信息
    const size = reader.getUint24()
    const protocolId = reader.getUint8()
    const serviceId = !reader.isEOF() ? reader.getUint16() : undefined

    // 2. 檢查封包長度
    if (size > buffer.byteLength) {
      return this._handlePacket(PACKET_TYPES.UNKNOWN, this.unknownParser, buffer)
    }

    // 3. 查找可能的封包定義
    const possiblePackets = this.findPacketsByIds(protocolId, serviceId)
    if (possiblePackets.length === 0) {
      return this._handlePacket(PACKET_TYPES.UNDEFINED, this.undefinedParser, buffer)
    }

    // 4. 匹配具體的封包定義
    const matchResult = this.findMatchingPacketByTypes(reader, possiblePackets)
    if (!matchResult) {
      return this._handlePacket(PACKET_TYPES.UNDEFINED, this.undefinedParser, buffer)
    }

    try {
      // 5. 解析封包內容
      return this._handlePacket(matchResult.definition.name, matchResult.parser, buffer)
    } catch (error) {
      if (this.isDebug) {
        console.error(`Error auto-parsing packet ${matchResult.definition.name}: `, error)
      }
      return this._handlePacket(PACKET_TYPES.UNDEFINED, this.undefinedParser, buffer)
    }
  }

  _handlePacket(description, parser, buffer) {
    const result = Object.create(this._resultTemplate)
    if (this.isDebug) this.parsePacketName = description

    const parsedData = parser.parse(buffer)
    for (const key in parsedData) {
      result[key] = parsedData[key]
    }

    return result
  }

  findPacketsByIds(protocolId, serviceId) {
    const matches = []

    for (const [name, definition] of this.packetDefinitions) {
      if (definition.identify.protocolId !== protocolId) continue

      if (definition.identify.ignoreServiceId || this.isServiceIdMatch(definition, serviceId)) {
        matches.push({
          name,
          definition,
          parser: this.parsers.get(name)
        })
      }
    }
    return matches
  }

  isServiceIdMatch(definition, serviceId) {
    if (definition.identify.serviceId === serviceId) {
      return true
    }

    if (definition.identify.aliases?.length) {
      for (let i = 0; i < definition.identify.aliases.length; i++) {
        if (definition.identify.aliases[i] === serviceId) {
          return true
        }
      }
    }

    return false
  }

  findMatchingPacketByTypes(reader, possiblePackets) {
    const originalOffset = reader.offset
    try {
      reader.offset = originalOffset + 2

      for (let i = 0; i < possiblePackets.length; i++) {
        if (possiblePackets[i].definition.identify.ignoreTypeArray) {
          return possiblePackets[i]
        }
      }

      let candidatePackets = []
      let candidateCount = 0

      for (let i = 0; i < possiblePackets.length; i++) {
        const packet = possiblePackets[i]
        const typeArraysCount = 1 + (packet.definition.identify.typeArrayAliases?.length || 0)
        candidateCount += typeArraysCount
      }

      candidatePackets = new Array(candidateCount)
      let index = 0

      for (let i = 0; i < possiblePackets.length; i++) {
        const packet = possiblePackets[i]

        candidatePackets[index++] = {
          name: packet.name,
          definition: packet.definition,
          parser: packet.parser,
          matchingTypeArray: packet.definition.identify.typeArray,
          typeIndex: 0
        }

        if (packet.definition.identify.typeArrayAliases?.length) {
          const typeArrayAliases = packet.definition.identify.typeArrayAliases
          for (let j = 0; j < typeArrayAliases.length; j++) {
            candidatePackets[index++] = {
              name: packet.name,
              definition: packet.definition,
              parser: packet.parser,
              matchingTypeArray: typeArrayAliases[j],
              typeIndex: 0
            }
          }
        }
      }

      candidatePackets.length = index

      while (candidatePackets.length > 0 && !reader.isEOF()) {
        const currentByte = reader.getUint8()

        let validCount = 0
        for (let i = 0; i < candidatePackets.length; i++) {
          const candidate = candidatePackets[i]

          if (
            candidate.typeIndex < candidate.matchingTypeArray.length &&
            candidate.matchingTypeArray[candidate.typeIndex] === currentByte
          ) {
            candidate.typeIndex++

            if (candidate.matchingTypeArray.length === candidate.typeIndex) {
              return candidate
            }

            candidatePackets[validCount++] = candidate
          }
        }

        candidatePackets.length = validCount
      }

      return null
    } finally {
      reader.offset = originalOffset
    }
  }

  /**
   * Get the version of the loaded packet definitions
   * @returns {string} The version of the loaded packet definitions
   */
  getDefinitionsVersion() {
    return this.version
  }

  /**
   * Get a list of all loaded packet names
   * @returns {Array<string>} List of packet names
   */
  getLoadedPacketNames() {
    return Array.from(this.packetDefinitions.keys())
  }

  /**
   * Get parse packet name
   * @returns {string} The parse packet name
   */
  getParsePacketName() {
    return this.parsePacketName
  }
}

module.exports = PacketManager
