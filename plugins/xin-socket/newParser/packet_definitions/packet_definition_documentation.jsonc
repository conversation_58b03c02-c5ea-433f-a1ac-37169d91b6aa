/**
 * 封包定義完整文檔
 * 此文件詳細說明了封包定義的所有可能配置和使用方法
 */
{
  // === 版本信息 ===
  "version": "1.0.0",  // 定義文件版本

  // === 封包定義集合 ===
  "packets": {
    // 每個封包定義的格式
    "PacketName": {
      // === 基本信息 ===
      
      // 封包名稱（必填）
      // 用於識別和調試，建議使用有意義的名稱
      "name": "PacketName",

      // 封包描述（選填）
      // 可以提供有關此封包用途的詳細信息
      "description": "封包用途描述",

      // === 識別信息 ===
      // 用於確定哪個解析器應處理特定封包
      "identify": {
        // 協議ID（選填）
        // 默認值為30
        "protocolId": 30,
        
        // 服務ID（必填）
        // 通常是封包頭部的前2字節
        "serviceId": 12345,
        
        // 服務ID別名（選填）
        // 定義其他可能匹配的服務ID
        "aliases": [12346, 12347],

        // 類型數組（必填）
        // 用於進一步區分相同服務ID的不同封包
        "typeArray": [1, 2, 3],
        
        // 類型數組別名（選填）
        // 定義其他可能匹配的類型數組
        "typeArrayAliases": [[1, 2, 4], [1, 2, 5]],
        
        // 特性標識（選填）
        // 用於標記此封包的功能特性ID
        "isFeature": [101, 102],
        
        // 是否忽略類型0（選填）
        // 當設置為true時，如果類型數組的第一個元素為0，將被忽略
        "skipTypeZero": true,
        
        // 是否忽略serviceId（選填）
        // 當設置為true時，不檢查服務ID匹配
        "ignoreServiceId": false,
        
        // 是否忽略typeArray（選填）
        // 當設置為true時，不檢查類型數組匹配
        "ignoreTypeArray": false
      },
      
      // === 外部數據 ===
      // 定義需要從外部源加載的數據
      "externalData": {
        // 數據源類型
        // 'GCP': 從Google Cloud Platform加載
        // 'PROVIDERS': 從配置提供者加載
        // 'UIConfig': 從UI配置加載
        "source": "GCP",
        
        // 資源路徑
        "path": "data.json"
      },

      // === 字段定義 ===
      // 字段定義數組（必填）
      // 新格式：使用對象定義每個字段
      "fields": [
        // === 標準頭部字段 ===
        {
          "name": "packetLength",
          "type": "uint24",
          "description": "封包長度",
          "options": "bigEndian"
        },
        {
          "name": "protocolId",
          "type": "uint8",
          "description": "協議ID",
          "options": ""
        },
        {
          "name": "serviceId",
          "type": "uint16",
          "description": "服務ID",
          "options": "bigEndian"
        },
        
        // === 類型數組讀取 ===
        // 從封包中讀取指定數量的字節作為類型數組
        {
          "name": "serviceType",
          "type": "value_array",
          "description": "類型數組",
          "options": "count=2"
        },
        
        // 讀取預定義的類型數組（根據服務類型）
        {
          "name": "typeArray",
          "type": "predefined_type_array",
          "description": "預定義類型數組",
          "options": ""
        },
        
        // === 基本數據類型 ===
        {
          "name": "uint8Field",
          "type": "uint8",
          "description": "8位無符號整數",
          "options": ""
        },
        {
          "name": "uint16Field",
          "type": "uint16",
          "description": "16位無符號整數",
          "options": "bigEndian"
        },
        {
          "name": "uint24Field",
          "type": "uint24",
          "description": "24位無符號整數",
          "options": "littleEndian"
        },
        {
          "name": "uint32Field",
          "type": "uint32",
          "description": "32位無符號整數",
          "options": "bigEndian"
        },
        {
          "name": "ulongField",
          "type": "ulong",
          "description": "64位無符號整數",
          "options": "bigEndian"
        },
        {
          "name": "int8Field",
          "type": "int8",
          "description": "8位有符號整數",
          "options": ""
        },
        {
          "name": "int16Field",
          "type": "int16",
          "description": "16位有符號整數",
          "options": "bigEndian"
        },
        {
          "name": "int32Field",
          "type": "int32",
          "description": "32位有符號整數",
          "options": "littleEndian"
        },
        {
          "name": "longField",
          "type": "long",
          "description": "64位有符號整數",
          "options": "bigEndian"
        },
        {
          "name": "float32Field",
          "type": "float32",
          "description": "32位浮點數",
          "options": "littleEndian"
        },
        {
          "name": "float64Field",
          "type": "float64",
          "description": "64位浮點數",
          "options": "bigEndian"
        },
        {
          "name": "bytesField",
          "type": "bytes",
          "description": "字節數組",
          "options": "length=10"
        },
        
        // === MD5哈希 ===
        // 計算指定字段的MD5哈希值
        // prefix 選項會從字串中提取前綴，格式為 prefix=值
        // 如果找不到前綴或格式不正確，將返回空字串
        {
          "name": "usernameMD5",
          "type": "md5",
          "description": "用戶名MD5哈希",
          "options": "source=username"
        },
        {
          "name": "prefixedMD5",
          "type": "md5",
          "description": "帶前綴的MD5哈希，從字串中提取前綴",
          "options": "source=username,prefix=星城_"
        },
        // 前綴提取示例：
        // "prefix=星城_&other=123" -> 提取 "星城_"
        // "prefix=Web國際_" -> 提取 "Web國際_"
        // "noprefix" 或 "prefix=" -> 返回空字串

        // === 字符串類型 ===
        {
          "name": "fixedString",
          "type": "string",
          "description": "固定長度字符串",
          "options": "length=10"
        },
        {
          "name": "utf16String",
          "type": "string",
          "description": "UTF16字符串",
          "options": "utf16,length=10"
        },
        {
          "name": "dynamicString",
          "type": "string",
          "description": "動態長度字符串",
          "options": "length={stringLength}"
        },
        {
          "name": "withLengthString",
          "type": "string",
          "description": "帶長度前綴字符串",
          "options": "withLength"
        },
        {
          "name": "withLength16String",
          "type": "string",
          "description": "帶長度前綴UTF16字符串",
          "options": "withLength16"
        },
        {
          "name": "byteCountString",
          "type": "string",
          "description": "帶字節計數字符串",
          "options": "byteCount"
        },
        {
          "name": "remainingString",
          "type": "string",
          "description": "讀取剩餘字節",
          "options": "remaining"
        },

        // === 數組類型 ===
        // 固定長度數組
        {
          "name": "fixedArray",
          "type": "array",
          "description": "固定長度數組",
          "options": "count=3",
          "subFields": [
            {
              "name": "value",
              "type": "uint16",
              "description": "數值",
              "options": "bigEndian"
            }
          ]
        },

        // 動態長度數組
        {
          "name": "dynamicArray",
          "type": "array",
          "description": "動態長度數組",
          "options": "count={arrayCount}",
          "subFields": [
            {
              "name": "id",
              "type": "uint32",
              "description": "ID",
              "options": "bigEndian"
            },
            {
              "name": "name",
              "type": "string",
              "description": "名稱",
              "options": "withLength16"
            }
          ]
        },

        // 帶條件的數組
        {
          "name": "conditionalArray",
          "type": "array",
          "description": "條件數組",
          "options": "count={arrayCount}",
          "subFields": [
            {
              "name": "id",
              "type": "uint32",
              "description": "ID",
              "options": "bigEndian"
            },
            {
              "name": "name",
              "type": "string",
              "description": "名稱",
              "options": "withLength16"
            }
          ],
          "condition": "hasArray == true"
        },

        // While循環數組
        {
          "name": "whileArray",
          "type": "array",
          "description": "While循環數組",
          "options": "while=peek(value != 0xFF)",
          "subFields": [
            {
              "name": "value",
              "type": "uint8",
              "description": "數值",
              "options": ""
            },
            {
              "name": "data",
              "type": "uint16",
              "description": "數據",
              "options": "bigEndian"
            }
          ]
        },
        
        // 讀取到封包結尾的數組
        {
          "name": "toEndArray",
          "type": "array",
          "description": "讀取到結尾的數組",
          "options": "while=!reader.isEOF()",
          "subFields": [
            {
              "name": "id",
              "type": "uint16",
              "description": "ID",
              "options": "bigEndian"
            },
            {
              "name": "value",
              "type": "string",
              "description": "值",
              "options": "withLength16"
            }
          ]
        },

        // === 數字數組 ===
        // 讀取固定數量的數字
        {
          "name": "fixedNumberArray",
          "type": "value_array",
          "description": "固定數量數字數組",
          "options": "type=uint8,count=5"
        },
        // 讀取動態數量的數字
        {
          "name": "dynamicNumberArray",
          "type": "value_array",
          "description": "動態數量數字數組",
          "options": "type=uint16,count={arrayLength},bigEndian"
        },
        // 讀取直到特定條件的數字
        {
          "name": "whileNumberArray",
          "type": "value_array",
          "description": "條件終止數字數組",
          "options": "type=uint8,while=value!=0xFF"
        },

        // 派生字段：不讀取數據，僅使用transform計算
        {
          "name": "rank",
          "type": "derived",
          "description": "成員等級",
          "options": "transform=name===presidentname?3:(vicepresidentnames.some(vp=>vp.name===name)?2:1)"
        },
        // 固定值
        {
          "name": "role",
          "type": "derived",
          "description": "角色類型",
          "options": "transform='member'"
        },
        // 基於其他值的計算
        {
          "name": "totalValue",
          "type": "derived",
          "description": "總值",
          "options": "transform=value1 + value2 * 10"
        },
        // 使用外部數據計算的值
        {
          "name": "isValidItem",
          "type": "derived",
          "description": "是否有效物品",
          "options": "transform=externalData.validItems.includes(itemId)"
        },

        // 嵌套數組
        {
          "name": "nestedArray",
          "type": "array",
          "description": "嵌套數組",
          "options": "count=2",
          "subFields": [
            {
              "name": "subCount",
              "type": "uint8",
              "description": "子數組數量",
              "options": ""
            },
            {
              "name": "subItems",
              "type": "array",
              "description": "子數組",
              "options": "count={subCount}",
              "subFields": [
                {
                  "name": "subId",
                  "type": "uint16",
                  "description": "子項ID",
                  "options": "bigEndian"
                }
              ]
            }
          ]
        },

        // === 條件字段 ===
        {
          "name": "hasExtra",
          "type": "uint8",
          "description": "額外數據標誌",
          "options": "transform=value==1"
        },
        {
          "name": "extraData",
          "type": "uint32",
          "description": "額外數據",
          "options": "bigEndian",
          "condition": "hasExtra == true"
        },

        // === 轉換功能 ===
        {
          "name": "boolField",
          "type": "uint8",
          "description": "布爾值",
          "options": "transform=value==1"
        },
        {
          "name": "scaledValue",
          "type": "uint16",
          "description": "縮放值",
          "options": "bigEndian,transform=value*0.1"
        },
        {
          "name": "statusText",
          "type": "uint8",
          "description": "狀態",
          "options": "transform=value==1?'active':(value==2?'idle':'offline')"
        },

        // === 特殊用途字段 ===
        {
          "name": "_skip",
          "type": "skip",
          "description": "跳過的字節",
          "options": "length=4"
        },
        {
          "name": "_internalField",
          "type": "uint16",
          "description": "內部字段",
          "options": "bigEndian"
        },
        
        // === 位掩碼處理 ===
        {
          "name": "flagsByte",
          "type": "uint8",
          "description": "標誌字節",
          "options": ""
        },
        {
          "name": "flag1",
          "type": "derived",
          "description": "標誌1",
          "options": "transform=(flagsByte & 0x01) > 0"
        },
        {
          "name": "flag2",
          "type": "derived",
          "description": "標誌2",
          "options": "transform=(flagsByte & 0x02) > 0"
        }
      ],

      // === 後處理邏輯 ===
      // 在所有字段解析完成後執行的處理
      "postProcess": [
        // 1. 基本後處理（無條件）
        {
          "field": "sortedArray",
          "expression": "items.sort((a, b) => a.id - b.id)"
        },
        // 2. 帶條件的後處理
        {
          "field": "filteredArray",
          "expression": "items.filter(item => item.value > 50)",
          "condition": "items && items.length > 0"
        },
        // 3. 複雜條件的後處理
        {
          "field": "statistics",
          "expression": "{ count: items.length, total: items.reduce((sum, item) => sum + item.value, 0), average: items.reduce((sum, item) => sum + item.value, 0) / items.length }",
          "condition": "success && items && items.length > 0"
        },
        // 4. 總值計算（無條件）
        {
          "field": "total",
          "expression": "items.reduce((sum, item) => sum + item.value, 0)"
        },
        // 5. 基於條件的狀態設置
        {
          "field": "status",
          "expression": "items.length > 0 ? 'hasData' : 'empty'"
        },
        // 6. 帶條件的狀態設置
        {
          "field": "qualityLevel",
          "expression": "total > 1000 ? 'high' : total > 500 ? 'medium' : 'low'",
          "condition": "total !== undefined"
        },
        // 7. 時間戳
        {
          "field": "processedAt",
          "expression": "new Date().toISOString()"
        },
        // 8. 從字符串解析值
        {
          "field": "parsedValues",
          "expression": "textData.split(',').map(item => parseInt(item))",
          "condition": "textData && textData.includes(',')"
        },
        // 9. 使用外部數據進行處理
        {
          "field": "enrichedData",
          "expression": "items.map(item => ({ ...item, details: externalData.itemDetails[item.id] }))",
          "condition": "externalData && externalData.itemDetails"
        }
      ]
    }
  }
}

/**
 * 字段定義格式說明：
 * 
 * 舊格式（數組）:
 * ["字段名", "數據類型", "描述", "選項字符串", 子字段數組, {額外選項}]
 * 
 * 新格式（對象）:
 * {
 *   "name": "字段名",
 *   "type": "數據類型",
 *   "description": "描述",
 *   "options": "選項字符串",
 *   "subFields": [子字段對象數組],  // 僅用於array類型
 *   "condition": "條件表達式"       // 選填的條件表達式
 * }
 * 
 * 選項字符串支持的配置：
 * 
 * 1. 字節序
 * - "bigEndian"    : 大端序
 * - "littleEndian" : 小端序
 * 
 * 2. 字符串選項
 * - "length=N"          : 固定長度
 * - "length={expr}"     : 動態長度
 * - "withLength"        : 帶長度前綴
 * - "withLength16"      : 帶UTF16長度前綴
 * - "byteCount"         : 帶字節計數
 * - "remaining"         : 讀取剩餘所有
 * - "utf16"             : UTF16編碼
 * 
 * 3. 數組選項
 * - "count=N"          : 固定數量
 * - "count={expr}"     : 動態數量
 * - "while=condition"  : while循環條件
 * - "peek(condition)"  : 預覽條件
 * 
 * 4. 轉換選項
 * - "transform=expr"   : 值轉換表達式
 * 
 * 5. md5選項
 * - "source=fieldName" : 指定要計算MD5的源字段
 * - "prefix=text"      : 可選前綴文本
 * 
 * 6. value_array選項
 * - "count=N"          : 指定要讀取的字節數量
 * 
 * 後處理對象格式:
 * {
 *   "field": "輸出字段名稱",
 *   "expression": "表達式",
 *   "condition": "可選的條件表達式"
 * }
 *
 * 外部數據源支持的類型：
 * - GCP：從Google Cloud Platform加載數據
 * - PROVIDERS：從配置提供者加載數據
 * - UIConfig：從UI配置加載數據
 */