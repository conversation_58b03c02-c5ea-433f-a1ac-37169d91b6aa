import Qs from 'qs'
import FormData from 'form-data'
import camelcaseKeys from 'camelcase-keys'
import snakeCaseKeys from 'snakecase-keys'
import { setupProgress } from './basic'
import jwt from '~/utils/jwt.js'
const apiDomain = require(`~/station/${process.env.STATION}/apiDomain.js`).default
const outpostList = require(`~/station/outpostList.js`).default
import RequestManager from '~/utils/requestManage.js'

export default (ctx, inject) => {
  let baseURL = apiDomain().backend.baseURL

  const axiosOptions = {
    baseURL,
    paramsSerializer: (params) => Qs.stringify(params, { arrayFormat: 'indices', encode: true })
  }

  const axios = ctx.$axios.create(axiosOptions)
  setupProgress(axios)

  const requestManager = new RequestManager()

  axios.onRequest(async (req) => {
    let jwtToken = ''
    let no_cache = 0

    if (req.cancelDuplicateKey || req.abortableRequestKey) {
      const cancelToken = axios.CancelToken.source()
      req.cancelToken = cancelToken.token
      if (req.cancelDuplicateKey) {
        requestManager.addRequest(req.cancelDuplicateKey, cancelToken)
      }
      if (req.abortableRequestKey) {
        requestManager.addAbortableRequest(req.abortableRequestKey, cancelToken)
      }
    }

    if (req.headers.nuxtStopLoading) {
      // axios尚未提供custom config, 暫用headers
      req.progress = false
      delete req.headers.nuxtStopLoading
    }
    if (process.client) {
      const browserDomainName = window.location.hostname
      const outpost = outpostList().includes(browserDomainName) ? 1 : 0
      if (outpost || process.env.NUXT_ENV !== 'production') {
        no_cache = 1
      }
    }

    if (req.data && typeof req.data === 'object' && !(req.data instanceof FormData)) {
      req.data = snakeCaseKeys(req.data)
      jwtToken = await jwt.sign(req.data)
    } else if (req.params && typeof req.params === 'object') {
      req.params = snakeCaseKeys(req.params)
      if (no_cache) {
        req.params.nocache = 1
      }
      jwtToken = await jwt.sign(req.params)
    } else if (req.params === undefined) {
      req.params = {}
      if (no_cache) {
        req.params.nocache = 1
      }
      jwtToken = await jwt.sign(req.params)
    } else if (req.data === undefined) {
      req.data = {}
      jwtToken = await jwt.sign(req.data)
    }

    if (jwtToken !== undefined && jwtToken !== null) {
      req.headers.Authorization = `Bearer ${jwtToken}`
    }

    return req
  })

  axios.onResponse((res) => {
    if (res.config.cancelDuplicateKey || res.config.abortableRequestKey) {
      if (res.config.cancelDuplicateKey) {
        requestManager.removePendingRequest(res.config.cancelDuplicateKey)
      }
      if (res.config.abortableRequestKey) {
        requestManager.removeAbortableRequest(res.config.abortableRequestKey)
      }
    }
    class RequestData {
      constructor() {
        if (res.data && Array.isArray(res.data)) {
          const itemIsObject = res.data.every((el) => typeof el === 'object')
          this.list = itemIsObject ? camelcaseKeys(res.data, { deep: true }) : res.data
        } else if (res.data && typeof res.data === 'object') {
          for (const [key, value] of Object.entries(camelcaseKeys(res.data, { deep: true }))) {
            this[key] = value
          }
        }
      }

      get $method() {
        return res.config.method
      }

      get $body() {
        let result = {}
        if (res.config.data && typeof res.config.data === 'object') {
          result = camelcaseKeys(JSON.parse(res.config.data), { deep: true })
        }

        return result
      }

      get $params() {
        let result = {}
        if (res.config.params && typeof res.config.params === 'object') {
          result = camelcaseKeys(res.config.params, { deep: true })
        }

        return result
      }
    }

    res.data = new RequestData()

    // IP不在範圍內
    if (res.data && res.data.errorCode === 30013) {
      return process.client ? ctx.app.router.push('/noservice') : ctx.redirect('/noservice')
    }
    return res
  })

  // axios.interceptors.response.use(
  //   (response) => {
  //     if (response.data.errorCode === 60026) {
  //       return response
  //     }
  //     console.log(response.data.errorCode)

  //     return response
  //   },
  //   (error) => {
  //     if (error.response && error.response.data) {
  //       return Promise.reject(error.response.data)
  //     }
  //     console.info('✉️ ', error)

  //     return Promise.reject(error)
  //   }
  // )

  axios.onError((err) => {
    let result = {}
    if (err.message === 'cancel_request') {
      console.log('cancel_request', err)
    } else if (err.response) {
      if (err.response.data && typeof err.response.data === 'object') {
        result = camelcaseKeys(err.response.data, { deep: true })
        if (result.errorCode && result.errorCode === 99001) {
          // 全站維護
          ctx.store.commit('maintain/SET_SYSTEM', [
            {
              id: 1,
              maintaining: true,
              maintainBeginAt: result.options.maintainBeginAt,
              maintainEndAt: result.options.maintainEndAt
            }
          ])
        }
      }
    } else if (err.config && err.config.url === '/api/client/game/rtp_list') {
      return { error: 'timeout' }
    } else {
      ctx.app.$notify.backendError('no_internet')
    }
    return { data: result }
  })
  /* eslint-disable new-cap */
  const clientApi = {
    requestManager,
    news: new (require('~/api/news').default)(axios, ctx),
    banner: new (require('~/api/banner').default)(axios),
    game: new (require('~/api/game').default)(axios),
    maintain: new (require('~/api/maintain').default)(axios, ctx),
    login: new (require('~/api/login').default)(axios),
    guild: new (require('~/api/guild').default)(axios),
    payment: new (require('~/api/payment').default)(axios),
    leaderboard: new (require('~/api/leaderboard').default)(axios)
  }

  inject('clientApi', clientApi)
}
