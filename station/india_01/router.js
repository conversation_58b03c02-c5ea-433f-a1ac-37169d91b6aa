const STATION = process.env.STATION
export default {
  router: {
    middleware: 'route_authentication',
    extendRoutes(routes, resolve) {
      routes.splice(0, routes.length)
      routes.push(
        // 404
        {
          name: 'error',
          path: '*',
          component: resolve(__dirname, '~/layouts/error.vue'),
          props: { error: { statusCode: 404 } }
        },
        {
          name: `index`,
          path: '/',
          component: resolve(__dirname, `~/pages_station/${STATION}/index.vue`)
        },
        {
          name: 'goShare',
          path: '/goShare/:inviteCode',
          component: resolve(__dirname, `~/pages_station/${STATION}/index.vue`)
        },
        {
          name: `pwa`,
          path: '/teach/pwa',
          component: resolve(__dirname, `~/pages_station/${STATION}/teach/pwa/index.vue`)
        },
        {
          name: `game`,
          path: '/game',
          component: resolve(__dirname, `~/pages_station/${STATION}/game/index.vue`)
        },
        {
          name: 'playgame',
          path: '/:mode/:gameId',
          component: resolve(__dirname, `~/pages_station/${STATION}/game/playgame.vue`),
          props: true
        },
        {
          name: `news`,
          path: '/news',
          component: resolve(__dirname, `~/pages_station/${STATION}/news/index.vue`)
        },
        {
          name: `no_service`,
          path: '/noservice',
          component: resolve(__dirname, `~/pages_station/${STATION}/no_service.vue`)
        },
        {
          name: `leaderboard`,
          path: '/leaderboard',
          component: resolve(__dirname, `~/pages_station/${STATION}/leaderboard/index.vue`)
        },
        //player_info
        {
          name: `daily_list`,
          path: '/player/ranking/daily',
          component: resolve(__dirname, `~/pages_station/${STATION}/player/ranking/daily.vue`)
        },
        {
          name: `friend`,
          path: '/player/info/friend',
          component: resolve(__dirname, `~/pages_station/${STATION}/player/info/friend.vue`)
        },
        {
          name: `login_info`,
          path: '/player/info/login',
          component: resolve(__dirname, `~/pages_station/${STATION}/player/info/login.vue`)
        },
        {
          name: `player_file`,
          path: '/player/info/file',
          component: resolve(__dirname, `~/pages_station/${STATION}/player/info/file.vue`)
        },
        {
          name: `player_status`,
          path: '/player/info/status',
          component: resolve(__dirname, `~/pages_station/${STATION}/player/info/status.vue`)
        },
        {
          name: `guild_info`,
          path: '/guild/info',
          component: resolve(__dirname, `~/pages_station/${STATION}/guild/info.vue`)
        },
        {
          name: `guild_list`,
          path: '/guild/list',
          component: resolve(__dirname, `~/pages_station/${STATION}/guild/list.vue`)
        }
      )
    }
  }
}
