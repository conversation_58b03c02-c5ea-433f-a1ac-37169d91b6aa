export default () => {
  const isProduction = process.env.NUXT_ENV === 'production'
  return {
    backend: {
      baseURL: isProduction ? 'https://lr-api.xinverse.net' : 'https://living-room-api.xincity.xyz'
    },
    qpp: {
      autoGoToStatus: false,
      baseURL: isProduction ? 'https://ssl.wanin.tw' : 'https://ssl.wanin.tw',
      getno: '/1604/login/getno',
      getio: '/1604/login/getio',
      vendorID: '10007'
    }
  }
}
