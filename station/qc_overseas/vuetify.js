const STATION = process.env.STATION
export default {
  css: [`@/assets/scss/${STATION}/main.scss`],

  styleResources: {
    scss: [`@/assets/scss/${STATION}/variables.scss`]
  },
  // Vuetify module configuration (https://go.nuxtjs.dev/config-vuetify)
  vuetify: {
    treeShake: true,
    theme: {
      dark: true,
      themes: {
        light: {
          primary: '#FFED30',
          secondary: '#6923FF',
          warning: '#FF8329',
          error: '#F62C2C',
          white: '#ffffff',
          success: '#05C753',
          'primary-variant-1': '#FFC700',
          'primary-variant-2': '#FFF85B',
          'primary-variant-3': '#FFF6AA',
          'grey-0': '#252C33',
          'grey-1': '#E2E2E8',
          'grey-2': '#ADAFC2',
          'grey-25': '#8A94B1',
          'grey-3': '#898CA3',
          'grey-4': '#3D4056',
          'grey-5': '#1C255F',
          'grey-6': '#0D1234'
        },
        dark: {
          primary: '#FFED30',
          secondary: '#6923FF',
          warning: '#FF8329',
          error: '#F62C2C',
          white: '#ffffff',
          black: '#000000',
          success: '#05C753',
          info: '#2253FF',
          'primary-variant-1': '#FFC700',
          'primary-variant-2': '#FFF85B',
          'primary-variant-3': '#FFF6AA',
          'secondary-variant-1': '#440106',
          'grey-1': '#E2E2E8',
          'grey-2': '#ADAFC2',
          'grey-3': '#898CA3',
          'grey-4': '#3D4056',
          'grey-5': '#1C255F',
          'grey-6': '#0D1234',
          'default-content': '#FFFFFF',
          'button-content': '#050A1A',
          'dialog-fill': '#151050',
          'dialog-fill-2': '#020B3E',
          divider: '#ffff66',
          'footer-fill': '#0C105B',
          'iframe-bar': '#09051A',
          'footer-item': '#4d4545',
          'app-bar-item': '#0C105B',
          'button-icon': '#292222',
          'game-hover': '#48454D',
          'card-gradient-2': '#1E0968',
          'card-fill': '#1E0968',
          'pagination-fill': '#6923FF',
          'scroll-fill': '#27235E',
          scroll: '#3E35A8',
          'letter-info': '#8FAFFF',
          'footer-button': '#BBBFC9',
          'btn-disable': '#ffff4d',
          offline: '#b2aaaa',
          private: '#FFFFFF',
          'name-private': '#36bf36',
          'app-bar_line': '#FFED30',
          'vip-g-1': '#FEC600',
          'vip-g-2': '#DC1646'
        }
      }
    },
    breakpoint: {
      //與預設不同，md & lg 需考慮到 scrollbar 寬度
      thresholds: {
        xs: 600,
        sm: 960,
        md: 1270,
        lg: 1910
      },
      scrollBarWidth: 6
    }
  }
}
