const config = {
  //站台功能開關
  lock: {
    //公會開關
    guild: true,
    //OPP開關
    useQpp: false,
    //星城Online下載顯示開關
    appDownload: false,
    //reminder顯示開關
    otherStarCityOnlinePlatformsReminderDisabled: true,
    //華義實名
    getSlotIdentity: false,
    //啟用Station翻譯>> lang_XXX
    stationConvert: true,
    //檢查綁定手機
    checkBindPhoneNumber: true,
    //文字獄
    literalPrison: true,
    specialGame: {
      live: true,
      top_and_hot: true,
      fishing: false,
      featured: true,
      newest_and_recommend: true,
      chess_and_card: false
    },
    chatroom: {
      playerSemantic: false,
      msgSticker: false,
      msgCustomSticker: false,
      customerServiceCustomSticker: true
    },
    // 拉彩公告
    grandPrizeNoty: false,
    unlockOfficial: 0
  },
  vipLevel: {
    vipLevelTitle: [
      'none_vip',
      'bronze',
      'silver',
      'gold',
      'whiteGold',
      'platinum',
      'diamond',
      'fancyDiamond',
      'moonDazzle',
      'sunDazzle',
      'starDazzle'
    ],
    vipLevelImgFileName: [
      '0-1_None',
      '1-1_Bronze',
      '2-1_Silver',
      '3-1_Gold',
      '4-1_WhiteGold',
      '5-1_Platinum',
      '6-1_Diamond',
      '7-1_FancyDiamond',
      '8-1_MoonDazzle',
      '9-1_SunDazzle',
      '10-1_StarDazzle'
    ],
    vipLevelImg3xFileName: [
      '0-1_None@3x',
      '1-1_Bronze@3x',
      '2-1_Silver@3x',
      '3-1_Gold@3x',
      '4-1_WhiteGold@3x',
      '5-1_Platinum@3x',
      '6-1_Diamond@3x',
      '7-1_FancyDiamond@3x',
      '8-1_MoonDazzle@3x',
      '9-1_SunDazzle@3x',
      '10-1_StarDazzle@3x'
    ],
    vipLevelIconFileName: [
      '0_None_icon.png',
      '1_Bronze_icon.png',
      '2_Silver_icon.png',
      '3_Gold_icon.png',
      '4_WhiteGold_icon.png',
      '5_Platinum_icon.png',
      '6_Diamond_icon.png',
      '7_FancyDiamond_icon.png',
      '8_MoonDazzle_icon.png',
      '9_SunDazzle_icon.png',
      '10_StarDazzle_icon.png'
    ]
  },
  miniGame: {
    enable: false,
    keyWords: []
  },
  timeStamp: {
    format: 'HH:mm:ss [{0}] DD/MM/YYYY',
    formatDate: 'DD/MM/YYYY',
    formatNewsNote: 'HH:mm:ss [{0}] DD/MM/YYYY',
    formatError: 'HH:mm:ss [{0}] DD/MM/YYYY',
    formatLogin: 'HH:mm:ss [{0}] DD/MM/YYYY',
    timezone: ''
  },
  qpp: {
    showQPPIcon: false
  },
  defaultBtnColor: 'primary',
  //facebbok使用版本
  facebookVersion: 19,
  customerService: {
    title: 'gradient-primary-left',
    announcementColor: { red: 255, blue: 48, green: 237 }
  },
  chatroom: {
    mutedText: '{0} {1}',
    banSpeaking: '{0}',
    //客服是否使用電話號碼>>電話:信箱
    usePhoneNumber: false,
    welcomeMessageContent: ''
  },
  msgBar: {
    customerBtn: { icon: 'mdi-face-agent', color: 'primary-variant-1', isVuetifyIcon: true },
    sendIconColor: 'white'
  },
  msgBox: {
    dateTimeText: 'hh:mm A'
  },
  specialGameCard: {
    fontWeight: 'font-weight-bold'
  },
  gameCard: {
    backGroundColor: () => {
      return 'rgba(255,255,255,0.3)'
    }
  },
  newLogin: {
    companyInfoList: { herf: false, click: true, style: 'text-decoration: underline' },
    phoneIconColor: 'black--text',
    loginNotyArray: [
      'before_login_notice2',
      'before_login_notice3',
      'before_login_notice5',
      'before_login_notice4',
      'infringement_notice',
      'before_login_notice6',
      'free_play_no_calculate_notice'
    ],
    loginMethod: [
      {
        id: 4,
        icon: null,
        iconFile: require('~/assets/image/login/google_login.svg'),
        title: 'Google',
        status: 1
      }
    ]
  },
  guildInfoPage: {
    showLeaveCountDown: true,
    infoListOptions: [10, 30, 100]
  },
  guildEditDialog: {
    totemEnable: true,
    nameEnable: true,
    notyEnable: true,
    textFullWidthValidEnable: true
  },
  guildListPage: {
    backGroundColor: 'card-fill',
    showGuildRank: false,
    guildRankExpiredDate: null,
    countDownTextSetting: 'mr-1',
    countDownNumberColor: 'primary-variant-1--text',
    guildBannerSetting: ''
  },
  guildAcceptList: {
    showAcceptNotyDialog: true
  },
  guildPinMsg: {
    enable: false,
    defaultMsg: '',
    splitMark: '－＄－'
  },
  phoneNumber: {
    dialogWidth: '480px',
    showQRCode: false,
    clearPhoneNumber: true
  },
  cookieNotice: {
    herf: null,
    target: null,
    click: true,
    companyInfoIdx: 2,
    companySetting: 'SET_COMPANY_POLICY_TYPE',
    reflashOnAccept: true
  },

  coffer: {
    saveStarCoin: {
      title: '{0} {1}',
      balance: '{0} {1}',
      input: '{0}'
    },
    withdrawCoin: {
      title: '{0} {1}',
      balance: '{0} {1}',
      input: '{0}'
    }
  },
  mailIndex: {
    fontWeightRegular: false,
    mobileSendMailBtnColor: 'gradient-button',
    confirmDeleteDialogWidth: '380px',
    delieteReadAllBtnColor: 'primary',
    mailTitleClass: 'primary-variant-1--text',
    mailTimeDay: '{0}d left',
    mailTimeHr: '{0}hr left',
    sendNoty: 'mail_send_noty6',
    sendMailLevel: 1
  },
  paymentIndex: {
    title: '{1}',
    xinCoinFormat: '{0} {1}+{2} {3} {4}',
    redeemPoints: '{2}',
    redeemXinCoins: '{0}',
    convertClass: '',
    dictUpper: true,
    paymentDict: '{1}\r\n({0})',
    showGiftPack: true,
    showVipTab: true
  },
  dailyList: {
    backGroundColor: 'card-fill',
    description: false
  },
  role: {
    roleDialogWidth: '400px',
    userNameColor: 'default-content--text',
    titleBackgroundColor: 'gradient-primary-left',
    roleDescriptionSetting: ''
  },
  error: {
    showErrorInfo: false,
    maintenanceText: 'default-content--text',
    imgClass: 'mt-10'
  },
  playerInfoCardSetting: {
    dailyListEnable: true,
    achievementEnable: false,
    dailyListTableText: '',
    useVisitBtn: true
  },
  badge: {
    classSetting: 'app-bar-item'
  },
  gamePage: {
    background: 'card-fill',
    sortGameName: 'newest_games',
    resetUrlValue: false,
    inputValidate: 'special_character'
  },
  stationPage: {
    backGround: 'card-fill'
  },
  easyPlayerInfo: {
    backGround: 'card-fill',
    showLevel: false
  },
  menu: {
    backGround: 'transparent'
  },
  yoeShop: {
    url: '{0}/funonegames/main?mode=web'
  },
  gameIframe: {
    logoHeight: '20',
    showNoExpDialog: true
  },
  swiperBox: {
    gameCardWidth: {
      lg: 'calc(16.67% - 15.9px)',
      md: 'calc(24.2% - 15.9px)',
      sm: 'calc(32.5% - 16px)',
      xs: 'calc(49% - 16px)'
    },
    featuredGameCardMinWidth: {
      lg: '260px'
    },
    featuredGameCardWidth: {
      lg: 'calc(25% - 15.9px)',
      md: '320px',
      sm: '268px',
      xs: '300px'
    },
    giftPackCardWidth: {
      lg: 'calc(100%/3 - 32px/3)',
      md: 'calc(100%/3 - 32px/3)',
      sm: 'calc(100%/3 - 32px/3)',
      xs: 'calc(100%/3 - 32px/3)',
      twoGiftPack: 'calc(100%/2 - 8px/2)'
    },
    giftPackGradientBG: {
      background:
        'radial-gradient(95.52% 27.88% at 50% -12.07%,#fff 0%,rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #5282ff 0%, rgba(82, 130, 255, 0) 100%),conic-gradient(from {0}deg at 100% 100%,rgba(82, 130, 255, 0) 0deg,rgba(82, 130, 255, 0.3) 360deg),#5282ff',
      vipBackground:
        'radial-gradient(95.52% 27.88% at 50% -12.07%,#fff 0%,rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #2b3dde 0%, rgba(43, 61, 222, 0) 100%),conic-gradient(from {0}deg at 100% 100%,rgba(43, 61, 222, 0) 0deg,rgba(43, 61, 222, 0.3) 360deg),#2b3dde'
    },
    giftPackCardBg: {
      bigCardBase: function (deg) {
        return `radial-gradient(95.52% 27.88% at 50% -12.07%,#fff 0%,rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #5282ff 0%, rgba(82, 130, 255, 0) 100%),conic-gradient(from ${deg}deg at 100% 100%,rgba(82, 130, 255, 0) 0deg,rgba(82, 130, 255, 0.3) 360deg),#5282ff`
      },
      bigVipBase: function (deg) {
        return `radial-gradient(95.52% 27.88% at 50% -12.07%,#fff 0%,rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #2b3dde 0%, rgba(43, 61, 222, 0) 100%),conic-gradient(from ${deg}deg at 100% 100%,rgba(43, 61, 222, 0) 0deg,rgba(43, 61, 222, 0.3) 360deg),#2b3dde`
      },
      smallCardBase: function (deg) {
        return `radial-gradient(95.52% 27.88% at 50% -12.07%,#fff 0%,rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #5282ff 0%, rgba(82, 130, 255, 0) 100%),conic-gradient(from ${deg}deg at 100% 100%,rgba(82, 130, 255, 0) 0deg,rgba(82, 130, 255, 0.3) 360deg),#5282ff`
      },
      cardBg:
        'linear-gradient(270deg, rgba(173, 196, 255, 0) 0%, rgba(173, 196, 255, 0.8) 51%, rgba(173, 196, 255, 0) 100%)',
      cardVipBg:
        'linear-gradient(270deg, rgba(130, 134, 211, 0) 0%, rgba(130, 134, 211, 0.8) 51%, rgba(130, 134, 211, 0) 100%)'
    }
  },
  giftPackCurrency: 'RM',
  footbar: {
    gameCategoryColor: 'white',
    menuBackgroundColor: ''
  },
  footer: {
    footerNotyArray: [
      'footer_warning1',
      'footer_warning2',
      'footer_warning3',
      'footer_warning4',
      'infringement_notice',
      'footer_warning5',
      'free_play_no_calculate_notice'
    ],
    communityList: [
      {
        name: 'facebook',
        icon: null,
        iconFile: 'facebook.svg',
        link: 'https://www.facebook.com/profile.php?id=61558098544512'
      },
      {
        name: 'instagram',
        iconFile: 'instagram.svg',
        link: 'https://www.instagram.com/joyous_palace/'
      },
      {
        name: 'twitter',
        icon: null,
        iconFile: 'X icon.svg',
        link: 'https://twitter.com/JoyousPalace'
      }
    ]
  },
  characterInfo: {
    showLevel: false,
    showVipLevel: true,
    showGuild: true,
    showCoin: true,
    showHonor: false,
    showActive: true,
    showSilverCoin: false
  },
  vipLevelDescDialog: {
    vipImgTitle: 'rank'
  },
  gameIntro: {
    coverWidth: {
      mdAndUp: '212px',
      smAndDown: '160px'
    }
  },
  // 系統限制設定
  restriction: {
    reportInterval: 30 // 檢舉冷卻時間（秒）
  }
}
// eslint-disable-next-line no-unused-vars
export default ({ app }, inject) => {
  // 将 config 注入到应用中
  inject('UIConfig', config)
}
