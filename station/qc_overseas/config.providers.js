/* eslint valid-jsdoc: "off" */

'use strict'
// 總部 WEBSOCKETS 伺服器以及金流伺服器清單
export default {
  development: {
    serverVersion: '250',
    clientVersion: '**********',
    servers: ['wss://bpdev.xin-stars.com:13212'],
    gash: {
      providerId: 'C001520000259',
      domains: ['https://dev-g1.xin-stars.com:40006/']
    },
    fileService: {
      storage: 'https://storage.pacifyservice.com/pacifyservice_my_web_dev/',
      management: 'https://dev-gameapi.joyouspalace.com',
      userNamePrefix: 'Web國際_'
    }
  },
  staging: {
    serverVersion: '250',
    clientVersion: '**********',
    servers: ['wss://bpdev.xin-stars.com:13212'],
    gash: {
      providerId: 'C001520000259',
      domains: ['https://dev-g1.xin-stars.com:40006/']
    },
    fileService: {
      storage: 'https://storage.pacifyservice.com/pacifyservice_my_web_dev/',
      management: 'https://dev-gameapi.joyouspalace.com',
      userNamePrefix: 'Web國際_'
    }
  },
  production: {
    serverVersion: '520',
    clientVersion: '**********',
    servers: [
      'wss://bp2.xin-stars.com:13202',
      'wss://bp13.xin-stars.com:13202',
      'wss://bp16.xin-stars.com:13202',
      'wss://bp19.xin-stars.com:13202',
      'wss://bp20.xin-stars.com:13202',
      'wss://bp21.xin-stars.com:13202',
      'wss://bp22.xin-stars.com:13202',
      'wss://bp23.xin-stars.com:13202',
      'wss://bp24.xin-stars.com:13202',
      'wss://bp25.xin-stars.com:13202'
    ],
    gash: {
      providerId: 'C001040000194',
      domains: [
        'https://g1.xin-stars.com:40005/',
        'https://g2.xin-stars.com:40005/',
        'https://g3.xin-stars.com:40005/',
        'https://g4.xin-stars.com:40005/'
      ]
    },
    fileService: {
      storage: 'https://storage.pacifyservice.com/pacifyservice_my_web/',
      management: 'https://gameapi.joyouspalace.com',
      userNamePrefix: 'Web國際_'
    }
  }
}
