const STATION = process.env.STATION
export default {
  head() {
    return this.$nuxtI18nHead({ addSeoAttributes: true })
  },
  // Global page headers (https://go.nuxtjs.dev/config-head)
  // eslint-disable-next-line no-dupe-keys
  head: {
    meta: [
      { charset: 'utf-8' },
      {
        name: 'viewport',
        content:
          'width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no,viewport-fit=cover'
      },
      {
        hid: 'description',
        name: 'description',
        content: 'JOYOUS PALACE | Dive into an array of thrilling gaming experiences!'
      },
      { name: 'mobile-web-app-capable', content: 'yes' },
      { name: 'apple-touch-fullscreen', content: 'yes' },
      { name: 'apple-mobile-web-app-title', content: 'JOYOUS PALACE' },
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
      { name: 'facebook-domain-verification', content: '' },
      { property: 'og:title', content: 'JOYOUS PALACE' },
      {
        property: 'og:description',
        content: 'JOYOUS PALACE | Dive into an array of thrilling gaming experiences!'
      }
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: `/${STATION}/favicon.ico?v=1` },
      { rel: 'apple-touch-icon', href: `/${STATION}/apple-touch-icon.png?v=1` },
      { rel: 'apple-touch-icon', sizes: '180x180', href: `/${STATION}/apple-touch-icon.png?v=1` },
      { rel: 'icon', type: 'image/png', sizes: '32x32', href: `/${STATION}/favicon-32x32.png?v=1` },
      { rel: 'icon', type: 'image/png', sizes: '16x16', href: `/${STATION}/favicon-16x16.png?v=1` },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '192x192',
        href: `/${STATION}/android-chrome-192x192.png?v=1`
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '256x256',
        href: `/${STATION}/android-chrome-256x256.png?v=1`
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '512x512',
        href: `/${STATION}/android-chrome-512x512.png?v=1`
      },
      { rel: 'mask-icon', href: '/safari-pinned-tab.svg?v=1', color: '#5bbad5' },
      {
        rel: 'dns-prefetch',
        href: 'https://cdn.jsdelivr.net/'
      },
      {
        rel: 'dns-prefetch',
        href: 'https://fonts.googleapis.com/'
      },
      { rel: 'preconnect', href: 'https://cdn.jsdelivr.net/' },
      { rel: 'preconnect', href: 'https://fonts.googleapis.com/' },
      {
        rel: 'preload',
        href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0',
        as: 'style'
      },
      {
        rel: 'stylesheet',
        href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0'
      },
      {
        rel: 'stylesheet',
        href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,400,1,0'
      }
    ]
  }
}
