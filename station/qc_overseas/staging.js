export default {
  //qc站無法使用localhost登入，若要測試請先切換成馬來環境
  // client_id: '6a42c516',
  // secret_key: '636979ebd7087c184895fdd8895a8793',
  client_id: '11a86521',
  secret_key: '842417ef317c19b64f2d953572923f0b',
  env: {
    BASE_URL:
      process.env.NODE_ENV === 'production'
        ? 'https://www.joyouspalace.com/'
        : 'https://malaysia-client.xincity.xyz/',
    NUXT_ENV: process.env.NUXT_ENV || process.env.NODE_ENV || 'development',
    IMAGE_URL:
      process.env.NUXT_ENV === 'production'
        ? 'https://img.xinverse.xyz'
        : 'https://img.xincity.xyz',
    CLIENT_VERSION: process.env.CLIENT_VERSION || '20230802100001',
    PLATFORM_URL:
      process.env.NUXT_ENV === 'production'
        ? 'https://www.joyouspalace.com/'
        : 'https://malaysia-client.xincity.xyz/',
    AVATAR_BASE_URL: 'https://images.joyouspalace.com',
    STATION: process.env.STATION || undefined
  },
  lock: {
    guild: true,
    useQpp: false,
    literalPrison: true,
    appDownload: false,
    autoCreatChar: true,
    chatroom: {
      otherStarCityOnlinePlatformsReminderDisabled: true
    },
    specialGame: {
      live: true,
      top_and_hot: true,
      fishing: false,
      featured: true,
      newest_and_recommend: true
    }
  },
  game: {
    stationConstant: [
      {
        serverConstant: 20000,
        stationName: 'malaysia_01',
        station: 1,
        lang: 'en'
      },
      {
        serverConstant: 30000,
        stationName: 'india_01',
        station: 2,
        lang: 'en'
      },
      {
        serverConstant: 40000,
        stationName: 'japan_01',
        station: 3,
        lang: 'en'
      },
      {
        serverConstant: 50000,
        stationName: 'vietnam_01',
        station: 4,
        lang: 'vi-vn'
      }
    ]
  },
  customLocaleList: [
    // {
    //   code: 'zh-tw',
    //   text: '繁體中文',
    //   src: 'tw.svg'
    // },
    { code: 'en', text: 'English', src: 'en.svg' },
    //{ code: 'zh-cn', text: '简体中文', src: 'cn.svg' }
    { code: 'vi-vn', text: 'Tiếng Việt', src: 'vietnam.svg' }
  ],
  i18n: {
    strategy: 'no_prefix',
    vueI18n: {
      messages: {
        en: require('../../locales/en'),
        'vi-vn': require('../../locales/vi-vn')
        //'zh-cn': require('../../locales/zh-cn')
      },
      fallbackLocale: {
        default: ['en']
      }
    },
    locales: [
      { code: 'en', iso: 'en-US', file: 'en.js', isCatchallLocale: true },
      { code: 'vi-vn', iso: 'VN', file: 'vi-vn.js' }
      //{ code: 'zh-cn', iso: 'zh-CN', file: 'zh-cn.js' }
    ],
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'locale',
      redirectOn: 'root'
    },
    vuex: {
      syncLocale: true
    },
    lazy: true,
    langDir: 'locales/',
    seo: false,
    defaultLocale: 'en',
    baseUrl: process.env.BASE_URL
  }
}
