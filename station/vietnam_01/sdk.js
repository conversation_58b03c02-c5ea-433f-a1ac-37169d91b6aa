//apple login
const hostname = window.location.hostname

//google login
function handleResponse(response) {
  sdkObj.login.google.authData.idToken = response.credential
}

let sdkObj = {
  analysis: {
    facebook: {
      appId: '103923812981440',
      cookie: true,
      xfbml: true,
      version: 'v10.0'
    },
    google: {
      config: { id: 'GTM-T6TXS9' }, // gtm 或著ga4 ID 都可以直接使用，GOOGLE 服務會自己判斷要送到GA4還是GTM
      appName: '星城Online WEB館',
      analyticsPluginName: 'gtm' //看是使用ga 還是 gtm
    }
  },
  login: {
    facebook: {
      appId: '103923812981440',
      cookie: true,
      xfbml: true,
      version: 'v10.0'
    },
    google: {
      client_id: '817131079341-4p081vu0rs7k2r3csa913aht0lk2n6dc.apps.googleusercontent.com',
      callback: handleResponse,
      context: 'signin',
      authData: {
        idToken: undefined
      }
    },
    apple: {
      clientId: 'game.xin.hd.free.service',
      scope: 'name email',
      redirectURI: 'https://' + hostname + '/',
      usePopup: true
    },
    line: {
      clientId: '1653851402',
      liffId: '1653851402-wpQ0oNyd',
      loginUrl:
        'https://access.line.me/oauth2/v2.1/authorize?response_type=code&client_id={0}&redirect_uri={1}&state={2}&scope={3}&bot_prompt=aggressive&disable_auto_login=true&disable_ios_auto_login=true',
      scope: 'profile%20openid',
      redirectURI: '{0}line/callback'
    }
  }
}

export default sdkObj
