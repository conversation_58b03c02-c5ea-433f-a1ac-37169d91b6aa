const STATION = process.env.STATION
export default {
  /**
   * 正式環境下的 Cache 策略
   * 靜態檔案將採用 CacheFirst 策略
   * Ajex Request 將採用 NetWorkFirst 策略
   *
   * 參照：https://developers.google.com/web/tools/workbox/modules/workbox-strategies
   */
  pwa: {
    name: '星城Online Web館',
    icon: {
      source: `@/static/${STATION}/android-chrome-512x512.png`,
      fileName: `${STATION}/android-chrome-512x512.png`,
      sizes: [64, 120, 144, 152, 192, 384, 512],
      iosSizes: [],
      purpose: 'maskable'
    },
    meta: {
      author: 'WANIN',
      title: '星城Online Web館',
      ogHost: process.env.BASE_URL || 'https://h5.xin-stars.com',
      nativeUI: true
    },
    manifest: {
      id: '/?source=pwa',
      start_url: '/?source=pwa',
      name: '星城Online WEB館',
      short_name: '星城Online WEB館',
      scope: '/',
      description: '星城Online Web館:::全球華人的線上遊戲娛樂城:::帶您體驗來自世界各國的遊戲',
      useWebmanifestExtension: 'json',
      lang: 'zh-Hant-TW',
      icons: [
        {
          src: `/${STATION}/favicon-16x16.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '16x16',
          type: 'image/png'
        },
        {
          src: `/${STATION}/favicon-32x32.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '32x32',
          type: 'image/png'
        },
        {
          src: `/${STATION}/apple-touch-icon.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '180x180',
          type: 'image/png'
        },
        {
          src: `/${STATION}/android-chrome-192x192.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '192x192',
          type: 'image/png'
        },
        {
          src: `/${STATION}/android-chrome-256x256.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '256x256',
          type: 'image/png'
        },
        {
          src: `/${STATION}/android-chrome-512x512.png?v=${Math.floor(Math.random() * 100)}`,
          sizes: '512x512',
          type: 'image/png'
        }
      ],
      theme_color: '#440106',
      background_color: '#ac2335',
      display_override: ['window-control-overlay', 'minimal-ui'],
      display: 'standalone',
      prefer_related_applications: true,
      shortcuts: [
        {
          name: '星城Online Web館',
          short_name: '星城Online WEB館',
          description: '星城Online Web館:::全球華人的線上遊戲娛樂城:::帶您體驗來自世界各國的遊戲',
          url: '/?standalone=true',
          icons: [
            {
              src: `/${STATION}/android-chrome-192x192.png?v=${Math.floor(Math.random() * 100)}`,
              sizes: '192x192'
            }
          ]
        }
      ],
      screenshots: []
    },
    workbox: {
      enabled: false,
      // autoRegister: true,
      // cachingExtensions: '@/plugins/workbox/extensions.js'
      runtimeCaching: [
        {
          urlPattern: '/*',
          handler: 'networkFirst', // 快取策略
          method: 'GET'
        }
      ]
    }
  }
}
