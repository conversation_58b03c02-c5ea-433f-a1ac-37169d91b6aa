const STATION = process.env.STATION
export default {
  css: [`@/assets/scss/${STATION}/main.scss`],

  styleResources: {
    scss: [`@/assets/scss/${STATION}/variables.scss`]
  },
  // Vuetify module configuration (https://go.nuxtjs.dev/config-vuetify)
  vuetify: {
    treeShake: true,
    theme: {
      dark: true,
      themes: {
        light: {
          primary: '#4176FA',
          secondary: '#FFB235',
          warning: '#FF7917',
          error: '#E71A5B',
          white: '#ffffff',
          success: '#4CAF50',
          'primary-variant-1': '#6CA7FF',
          'primary-variant-2': '#F7B675',
          'primary-variant-3': '#C8E8FF',
          'grey-0': '#252C33',
          'grey-1': '#3A4550',
          'grey-2': '#4E5774',
          'grey-25': '#8A94B1',
          'grey-3': '#8A94B1',
          'grey-4': '#CBD1E7',
          'grey-5': '#F0F6FF',
          'grey-6': '#FAFBFD'
        },
        dark: {
          primary: '#E9B950',
          secondary: '#AC2335',
          warning: '#FF9F21',
          error: '#F62C2C',
          white: '#ffffff',
          black: '#000000',
          success: '#4CAF50',
          info: '#4176FA',
          'primary-variant-1': '#FEA600',
          'primary-variant-2': '#F7B675',
          'primary-variant-3': '#FFE7BD',
          'secondary-variant-1': '#440106',
          'grey-1': '#E6E1E1',
          'grey-2': '#B2AAAA',
          'grey-3': '#A38989',
          'grey-4': '#4C3535',
          'grey-5': '#402222',
          'grey-6': '#291515',
          'default-content': '#FBFBFB',
          'button-content': '#1A0508',
          'dialog-fill': '#552F31',
          'dialog-fill-2': '#472426',
          divider: '#ffffff',
          'footer-fill': '#000000',
          'iframe-bar': '#1A0508',
          'footer-item': '#4D4545',
          'app-bar-item': '#440106',
          'button-icon': '#292222',
          'game-hover': '#4D4545',
          'card-gradient-2': '#440106',
          'card-fill': '#440106',
          'pagination-fill': '#AC2335',
          'scroll-fill': '#331C1C',
          scroll: '#886D6D',
          'letter-info': '#8FAFFF',
          'footer-button': '#B2AAAA',
          'btn-disable': '#FFFFFF',
          offline: '#B2AAAA',
          private: '#FFFFFF',
          'name-private': '#36BF36'
        }
      }
    },
    breakpoint: {
      //與預設不同，md & lg 需考慮到 scrollbar 寬度
      thresholds: {
        xs: 600,
        sm: 960,
        md: 1270,
        lg: 1910
      },
      scrollBarWidth: 6
    }
  }
}
