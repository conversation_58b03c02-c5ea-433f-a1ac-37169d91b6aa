const STATION = process.env.STATION

export default {
  router: {
    middleware: 'route_authentication',
    extendRoutes(routes, resolve) {
      routes.splice(0, routes.length)
      routes.push(
        // 404
        {
          name: 'error',
          path: '*',
          component: resolve(__dirname, '~/layouts/error.vue'),
          props: { error: { statusCode: 404 } }
        },
        {
          name: `index`,
          path: '/',
          component: resolve(__dirname, `~/pages_station/${STATION}/index.vue`)
        },
        {
          name: 'game',
          path: '/:qcStation/game',
          component: resolve(__dirname, `~/pages_station/${STATION}/game/index.vue`),
          props: true
        },
        {
          name: 'playgame',
          path: '/:qcStation/:mode/:gameId',
          component: resolve(__dirname, `~/pages_station/${STATION}/game/playgame.vue`),
          props: true
        }
      )
    }
  }
}
