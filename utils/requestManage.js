// 請求管理器類別
class RequestManager {
  constructor() {
    // 儲存進行中的請求
    this.pendingRequests = new Map()
    // 儲存可取消API組
    this.apiGroup = new Map()
  }

  generateRequestKey(url, params) {
    const sorted = JSON.stringify(params)
    const full = `${url}?${sorted}`

    let hash = 0
    for (let i = 0; i < full.length; i++) {
      const chr = full.charCodeAt(i)
      hash = (hash << 5) - hash + chr
      hash |= 0 // 32-bit
    }

    return Math.abs(hash).toString(36).slice(0, 8)
  }

  // 添加請求到管理器
  addRequest(key, cancelToken) {
    // 如果存在相同key的請求，先取消它
    if (this.pendingRequests.has(key)) {
      this.pendingRequests.get(key).cancel()
    }
    this.pendingRequests.set(key, cancelToken)
  }

  // 添加可取消請求到管理器
  addAbortableRequest(key, cancelToken) {
    this.apiGroup.set(key, cancelToken)
  }

  // 從管理器中移除請求
  removePendingRequest(key) {
    this.pendingRequests.delete(key)
  }

  // 從管理器中移除可取消請求
  removeAbortableRequest(key) {
    this.apiGroup.delete(key)
  }

  // 手動取消特定請求
  cancelRequest(key) {
    if (this.apiGroup.has(key)) {
      const cancelToken = this.apiGroup.get(key)
      cancelToken.cancel()
      this.removeAbortableRequest(key)
    }
  }
}

export default RequestManager

//  API 方法說明：gameRTPList
// 請求 RTP 資料列表，可透過參數控制是否要取消重複請求或中途取消請求。

/**
 * @param {String} [cancelDuplicateId=null] - 是否取消重複的請求
 * @param {String} [abortableRequestId=null] - 是否允許中途取消請求
 *
 * @example
 * // 生成key
  const cancelDuplicateKey = this.$clientApi.requestManager.generateRequestKey('/api/client/game/rtp_list', gameIds)
  const abortableRequestKey = this.$clientApi.requestManager.generateRequestKey('/api/client/game/rtp_list', gameIds)

* // function設定

  gameRTPList(gameIds, cancelDuplicateKey = null, abortableRequestKey = null) {
    const body = { gameIds }

    return this.axios.$get('/api/client/game/rtp_list', {
      params: body,
      cancelDuplicateKey,
      abortableRequestKey
    })
  }

* // 呼叫
 this.$clientApi.game.gameRTPList(gameIds, cancelDuplicateKey, abortableRequestKey)

*/
