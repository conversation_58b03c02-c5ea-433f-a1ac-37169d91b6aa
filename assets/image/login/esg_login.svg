<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28">
  <defs>
    <clipPath id="a">
      <path d="M.22.18H145v140.25H.22z" class="cls-1" style="fill:none"/>
    </clipPath>
    <clipPath id="b">
      <path d="M.26 70.28v70.1H145V.18H.26v70.1Zm106.37-50.55a38.89 38.89 0 0 1 5.19.88 22.26 22.26 0 0 1 8.89 4.32 24 24 0 0 1 2.14 2.14 9.48 9.48 0 0 1 2 3.93c.3 1.19.32 1.46.32 5.7v3.83h-12v-2a7.85 7.85 0 0 0-.13-2.29c-.62-1.9-2.63-3.35-5.7-4.13a14.28 14.28 0 0 0-3.79-.44 13 13 0 0 0-6 1.26 9.11 9.11 0 0 0-2.7 1.93A6.15 6.15 0 0 0 93.22 38a6.43 6.43 0 0 0 1.16 5.27c1.11 1.43 4 3.36 7.43 5 1.52.72 4.23 1.92 6.29 2.81a60.47 60.47 0 0 1 6 2.77 46.55 46.55 0 0 1 4.63 2.94 23.43 23.43 0 0 1 5.47 5.77 17.13 17.13 0 0 1 2.77 8.6 32.52 32.52 0 0 1-.08 4.21 20.43 20.43 0 0 1-11.51 16.12 26 26 0 0 1-11.71 2.66A29.48 29.48 0 0 1 91.41 92a33.69 33.69 0 0 1-10.53-6.38c-.63-.57-2.34-2.29-2.52-2.54l-.1-.14 7.49-9.33 1 1a27.1 27.1 0 0 0 4.43 3.57 24.43 24.43 0 0 0 3 1.75 18.6 18.6 0 0 0 9 2.21 12.54 12.54 0 0 0 3-.29 11 11 0 0 0 5.37-2.74 7 7 0 0 0 1.96-4.11 11.68 11.68 0 0 0 0-2.51 6.73 6.73 0 0 0-2.25-3.92 28.44 28.44 0 0 0-5.42-3.57c-1.33-.7-5.32-2.61-8.47-4a51.15 51.15 0 0 1-7.77-4.24 24.4 24.4 0 0 1-6.07-5.42 15.86 15.86 0 0 1-3.33-6.56 19.41 19.41 0 0 1-.51-4.76 18 18 0 0 1 .76-5.45A20.17 20.17 0 0 1 88.29 24a25.9 25.9 0 0 1 6.18-3 29.17 29.17 0 0 1 7.24-1.37 42.8 42.8 0 0 1 4.92.1Zm-42.3 1.11c3.07.27 4.9 1.68 5.59 4.32.3 1.13.3 1.12.3 7.6v5.91H58.13v-2.3c0-1.39 0-2.46-.05-2.64a1.85 1.85 0 0 0-.92-1.39c-.53-.27-.14-.26-9.18-.26h-8.33V51H63v11.27H39.65v17.88l.1.27a1.76 1.76 0 0 0 1.12 1.12l.28.1h8.89c7.86 0 8.93 0 9.21-.06a1.82 1.82 0 0 0 1.47-1.69V75h12.13v12.27c-.36 3.6-2.13 5.3-5.84 5.58H49.18c-18.65 0-17.4 0-18.56-.27-2.62-.58-3.97-2.23-4.24-5.36 0-.39-.05-9.48-.05-27.86V32.08h-6.62V20.79h22c15.26 0 22.19 0 22.6.05ZM46 101.68a11.07 11.07 0 0 1 4.34 1.43 5.53 5.53 0 0 1 1.21.93 3.92 3.92 0 0 1-.3.42l-.36.47c-.06.08-.08.07-.29-.11a10.27 10.27 0 0 0-4.1-1.85 7.49 7.49 0 0 0-1.78-.11 6.63 6.63 0 0 0-1.82.13 8.78 8.78 0 0 0-6.85 8 15 15 0 0 0 .08 2.85 8.65 8.65 0 0 0 5.75 7 7.87 7.87 0 0 0 2.67.41 7.29 7.29 0 0 0 3.82-.91 10 10 0 0 0 2.73-2l.4-.41v-1.53a5.88 5.88 0 0 0-.08-1.73c-.13-.26-.29-.3-1.1-.3h-.7v-1.16h.82a3.09 3.09 0 0 1 1.69.26.82.82 0 0 1 .4.44l.15.29v7.94h-1.21v-2.68l-.4.39A7.27 7.27 0 0 1 49.7 121a9.63 9.63 0 0 1-3.42 1.38 9.82 9.82 0 0 1-7.77-2 12 12 0 0 1-1.78-1.86 11.15 11.15 0 0 1 .07-13 9.8 9.8 0 0 1 6.64-3.83 17.8 17.8 0 0 1 2.53 0Zm20 4.65c.89 2.39 2.45 6.56 3.47 9.27s1.88 5 1.93 5a1.19 1.19 0 0 0 .88.33h.38v1.14h-.26a4.83 4.83 0 0 1-1.39-.2 1.75 1.75 0 0 1-.83-1c-.12-.25-.66-1.66-1.22-3.14l-1-2.69h-8.6l-1 2.69c-.55 1.48-1.09 2.89-1.21 3.14a1.76 1.76 0 0 1-.84 1 4.83 4.83 0 0 1-1.39.2h-.26V121h.39a1.18 1.18 0 0 0 .87-.33c.05-.07 1-2.4 2-5.17l3.43-9.14c.84-2.25 1.56-4.16 1.59-4.23L63 102h1.36l1.64 4.33Zm14.53.75c3.53 7.61 3.74 8.06 4.13 9a5.14 5.14 0 0 0 .4.82 6.57 6.57 0 0 0 .29-.66c.25-.61.84-1.9 2.51-5.5.43-.94 1-2.13 1.23-2.66s.67-1.44.94-2l.75-1.61.69-1.5.44-.94h.6c.6 0 .61 0 .63.09s.15 1.68.3 3.59.31 4 .36 4.63c.17 2.07.52 6.57.67 8.4.08 1 .17 1.86.2 1.94.1.28.23.33 1 .34h.72v1.13h-.89a4.41 4.41 0 0 1-1.17-.08 1.24 1.24 0 0 1-.86-.93c0-.18-.12-1-.41-4.58s-.51-6.41-.61-7.6c0-.71-.12-1.52-.14-1.81s-.07-1.09-.08-1.78a11.17 11.17 0 0 0-.07-1.24 3.66 3.66 0 0 0-.25.62c-.39 1.06-.77 2-1.64 3.8-.47 1-1.19 2.52-1.62 3.42l-2.7 5.73-.26.57h-1.34l-.18-.39-.56-1.2-1.83-3.91-1.78-3.9c-1.06-2.26-1.35-2.92-1.7-3.86l-.38-1-.06 1.3c0 1.11-.25 3.82-.69 9.19 0 .65-.15 1.87-.21 2.72-.29 3.58-.31 3.76-.42 4a1.3 1.3 0 0 1-.57.71 4.17 4.17 0 0 1-1.62.18h-.67v-1.14h.62c.74 0 1 0 1.07-.25s.1-.14.27-2.26c.06-.83.16-2 .21-2.7s.26-3.23.46-5.74.41-5.1.46-5.75.11-1.35.12-1.57 0-.46.06-.56v-.19h1.22l2.35 5.09Zm29.94-5a1.21 1.21 0 0 1 .66.69 3.56 3.56 0 0 1 .13 1.31v1.05H110v-.73c0-.88 0-1.07-.26-1.2s-.27-.1-4.24-.1h-4.09v8.26h8v1.16h-8v4c0 3.9 0 4 .09 4.18a.47.47 0 0 0 .28.2h4.37c4.56 0 4.42 0 4.61-.27.07-.11.09-.26.1-1v-.84h1.17v1c0 1.06 0 1.33-.26 1.68a1.39 1.39 0 0 1-1 .51c-.46.07-8.91.06-9.36 0a1.43 1.43 0 0 1-1-.48c-.31-.43-.3 0-.3-9.67v-8.82h-1.8v-1.13h11.9l.24.11Z" class="cls-2" style="clip-path:url(#a);clip-rule:evenodd;fill:none"/>
    </clipPath>
    <clipPath id="d" transform="translate(-.02 .06)">
      <path d="M.22.19H145v140.24H.22z" class="cls-3" style="clip-path:url(#b);fill:none"/>
    </clipPath>
    <clipPath id="e" transform="translate(-.02 .06)">
      <path d="M63.53 103.62c-.23.86-.61 2-1.21 3.61-.26.67-.87 2.31-1.36 3.64l-1 2.72a1.74 1.74 0 0 0-.12.35s1.34 0 3.84 0c3.64 0 3.84 0 3.81-.08l-.39-1c-.19-.52-.57-1.52-.84-2.24-2-5.23-2.2-5.84-2.49-6.89a1 1 0 0 0-.13-.33s-.06.1-.09.21" class="cls-2" style="clip-path:url(#a);clip-rule:evenodd;fill:none"/>
    </clipPath>
    <mask id="c">
      <path d="M28 14a14 14 0 0 1-14 14A14 14 0 0 1 0 14 14 14 0 0 1 14 0a14 14 0 0 1 14 14Z" style="paint-order:fill;fill:#fff"/>
    </mask>
  </defs>
  <ellipse style="stroke-width: 1; fill: rgb(255, 255, 255);" cx="14" cy="14" rx="13.5" ry="13.5"/>
  <g style="mask:url(#c)" transform="matrix(1, 0, 0, 1, -0.000009, 0.0005)">
    <g data-name="Layer 1">
      <g class="cls-4" style="clip-path:url(#d)" transform="matrix(.18641 0 0 .18641 .47 .888)">
        <path d="M0 0h145.17v140.68H0z" class="cls-5" style="fill:#f2981b"/>
      </g>
      <g class="cls-6" style="clip-path:url(#e)" transform="matrix(.18641 0 0 .18641 .47 .888)">
        <path d="M59.55 103.23h8.14v11.05h-8.14z" class="cls-5" style="fill:#f2981b"/>
      </g>
    </g>
    <path d="M25.562-.023H28v28.045h-2.438z" style="vector-effect:non-scaling-stroke;fill:#f1981b;paint-order:fill"/>
    <path d="M0-.023h2.438v28.045H0z" style="vector-effect:non-scaling-stroke;fill:#f1981b;paint-order:fill;stroke-width:1"/>
    <path d="M0 26.504h28V28H0zM0 0h28v1.496H0z" style="vector-effect:non-scaling-stroke;fill:#f1981b;paint-order:fill;stroke-width:1"/>
  </g>
</svg>