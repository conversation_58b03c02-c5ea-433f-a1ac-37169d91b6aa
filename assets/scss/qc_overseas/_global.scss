$primary-color: map-get($colors, primary);
$secondary-color: map-get($colors, secondary);
$white-color: map-get($colors, white);
$grey-1-color: map-get($colors, grey-1);
$grey-5-color: map-get($colors, grey-5);
$grey-6-color: map-get($colors, grey-6);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$scroll-fill-color: map-get($colors, scroll-fill);
$scroll-color: map-get($colors, scroll);
$button-content-color: map-get($colors, button-content);
$dialog-fill-color: map-get($colors, dialog-fill);
$app-bar-item-color: map-get($colors, app-bar-item);
$card-fill-color: map-get($colors, card-fill);
$divider-color: map-get($colors, divider);
$btn-disable-color: map-get($colors, btn-disable);

* {
  font-family: "Noto Sans TC", sans-serif;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background-color: $scroll-fill-color;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $scroll-color;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}

.scrollbar-thin::-webkit-scrollbar,
.scrollbar-thin *::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

body,
html {
  font-family: "Noto Sans TC", sans-serif;
  position: relative;
  height: auto;
}

a,
.link {
  color: $primary-color !important;
  cursor: pointer;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-default {
  cursor: default;
}

.scrollable {
  overflow-y: auto;
  height: 100%;
}
.noscroll {
  overflow: hidden !important;
}

.word-break-all {
  word-break: break-all;
}

//深色主題 客製化
body .theme--dark.v-application {
  background: linear-gradient(180deg, #290a92 0%, #270a62 51.04%, #8f2094 100%);
}
.v-application {
  .v-dialog {
    background: $dialog-fill-color;
  }
  .theme--dark.v-list {
    background: $dialog-fill-color;
  }
  .theme--dark.v-tabs-items {
    background: $grey-6-color;
  }
  // 分頁元件
  .theme--dark.v-pagination {
    .v-pagination__item,
    .v-pagination__navigation {
      background-color: $secondary-color;
    }
    .v-pagination__item--active {
      color: $button-content-color;
    }
  }

  .theme--dark.v-divider {
    border-color: $divider-color;
  }
  //v-textarea 為了避免內容跟title連在一起 將margin-top調高
  .v-textarea.v-text-field--enclosed textarea {
    margin-top: 30px;
  }

  .player-menu-mobilebar {
    width: 250px;
  }

  .player-menu-playerinfo {
    width: 300px;
  }

  .w-100 {
    width: 100%;
  }

  .h-100-percent {
    height: 100% !important;
  }

  //vuetify config無法設opacity 故用此方式
  .btn-disable--text {
    color: $btn-disable-color !important;
    caret-color: $btn-disable-color !important;
  }
}
//data table第一筆資料不要有bofder-radius, data-footer不要有margin
.v-data-table {
  & > .v-data-table__wrapper tbody tr:first-child:hover {
    td:last-child {
      border-top-right-radius: 0 !important;
    }
    td:first-child {
      border-top-left-radius: 0 !important;
    }
  }
  .v-data-footer {
    margin-right: 0 !important;
  }
}
.input-height {
  // 修復 v-text-field 字體被切掉的問題
  &.v-text-field--dense .v-input__control {
    min-height: 40px !important;
  }
  &.v-text-field--outlined .v-input__control {
    min-height: 40px !important;
  }
  &.v-text-field--dense .v-text-field__details {
    min-height: 16px !important;
  }
  &.v-text-field input {
    line-height: 1.5 !important;
    padding: 8px 0 !important;
  }
  &.v-text-field--outlined.v-text-field--dense .v-label {
    top: 6px !important;
  }
  &.v-text-field--outlined.v-text-field--dense.v-text-field--placeholder .v-label {
    top: 6px !important;
  }
}
