<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="gift-pack-frame gift-pack-card-bg" :class="specialUI">
    <div class="gift-pack-card custom-text-noto" :style="cardBackgroundStyle" ref="giftPackCard">
      <v-card
        height="100%"
        color="transparent"
        class="px-3 px-sm-4 py-3 d-flex flex-column align-center"
        elevation="0"
      >
        <div v-if="canBuyItem || !countDownDisplay" class="limit-buy"></div>
        <!--VIP Icon-->
        <v-img eager :src="vipIcon" @error="errorStickerHandler()" class="gift-pack-vipIcon" />
        <!--回饋-->
        <div v-if="providerInfo.adTitle !== ''" class="rebate-card gift-pack-reward-card-fill">
          <span class="elevation-text">{{ providerInfo.adTitle }}</span>
        </div>
        <!--禮包標題-->
        <div class="gift-pack-title">
          <span class="gift-pack-title-text custom-text-noto" v-if="providerInfo.title">
            {{ providerInfo.title }}
          </span>
          <!--購買次數限制-->
          <span
            class="gift-max-count custom-text-noto"
            v-if="providerInfo.isBigCard && providerInfo.buyLimitText !== ''"
          >
            {{ formatBuyLimitText }}
          </span>
        </div>
        <!--禮包視覺-->
        <v-img
          :src="getImage(providerInfo.mainContent.image)"
          width="126"
          @error="errorStickerHandler()"
          class="mt-1"
          eager
        />
        <!--折扣-->
        <v-img
          :src="getImage(providerInfo.mainContent.discountImage)"
          @error="errorStickerHandler()"
          class="gift-pack-discountIcon"
        />
        <!--禮包內容-->
        <div class="mt-1 gift-pack-content" :class="{ 'has-countdown': needCountdown }">
          <div class="main-content d-flex justify-center align-center">
            <v-img
              :src="getImage(providerInfo.mainContent.icon)"
              max-width="16"
              max-height="16"
              class="mr-1"
            />
            <span>{{ providerInfo.mainContent.text }}</span>
          </div>
          <!--加贈-->
          <div v-if="providerInfo.subContents.length > 0">
            <div class="bonus-title d-flex justify-center align-center">
              <span class="bonus-line-left"></span>
              <span class="bonus-square mx-1"></span>
              <span>{{ $t('mall_bonus') }}</span>
              <span class="bonus-square mx-1"></span>
              <span class="bonus-line-left bonus-line-right"></span>
            </div>
            <div class="sub-content d-flex justify-center align-center flex-column">
              <div
                class="d-flex justify-center align-center"
                v-for="(item, index) in providerInfo.subContents"
                :key="index"
              >
                <v-img :src="getImage(item.icon)" max-width="16" max-height="16" class="mr-1" />
                <span>{{ item.text }}</span>
              </div>
            </div>
          </div>
        </div>
        <!--價位-->
        <v-btn
          :disabled="
            providerInfo.isBigCard &&
            ((providerInfo.userBuyCount == providerInfo.maxBuyLimit &&
              providerInfo.maxBuyLimit > 0) ||
              !duration)
          "
          class="gift-pack-price py-0 mt-4"
          @click="showPaymentMethod(providerInfo)"
        >
          <div
            v-if="
              !(
                providerInfo.isBigCard &&
                ((providerInfo.userBuyCount == providerInfo.maxBuyLimit &&
                  providerInfo.maxBuyLimit > 0) ||
                  !duration)
              )
            "
            class="d-flex justify-center"
          >
            <span class="mr-1"> {{ this.$UIConfig.giftPackCurrency }} </span>
            <span>{{ providerInfo.amount }}</span>
          </div>
          <div v-else class="d-flex justify-center">
            <span> {{ $t('purchase_limit_reached') }} </span>
          </div>
        </v-btn>
        <!--附註-->
        <div v-if="providerInfo.extraInfoIcon?.show" class="right-top-remark">
          <v-menu open-on-hover offset-x left :nudge-left="10" :nudge-top="4">
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="remark-icon" v-bind="attrs" v-on="on">
                {{ providerInfo.extraInfoIcon.icon }}
              </v-btn>
            </template>
            <v-card class="right-top-remark-text px-2 py-1">{{
              providerInfo.extraInfoIcon.text
            }}</v-card>
          </v-menu>
        </div>
      </v-card>
      <yoeConfirmDialog v-model="showConfirmDialog" :action="handleConfirmWithExpiredCheck">
        <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
        <div class="d-flex flex-wrap">
          <span class="default-content--text mb-2">{{
            $t('yoeGameConfirmNoty').split('\n')[0]
          }}</span>
          <span class="primary--text">
            {{ $t('yoeGameConfirmNoty').split('\n')[1] }}
          </span>
          <span class="primary--text">
            {{ $t('yoeGameConfirmNoty').split('\n')[2] }}
          </span>
        </div>
      </yoeConfirmDialog>
    </div>
  </div>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import debounce from 'lodash/debounce'

  export default {
    name: 'yoeProviderCard',
    mixins: [analytics],
    components: {
      yoeConfirmDialog: () => import('~/components/payment/yoeConfirmDialog')
    },
    props: {
      providerInfo: {
        type: Object,
        default: () => ({
          itemContent: []
        })
      }
    },
    data() {
      return {
        showConfirmDialog: false,
        imageLoadingError: false,
        showCancelDialog: false,
        showSuccessDialog: false,
        showPurchaseCancelDialog: false,
        timer: null,
        duration: Math.floor(
          this.$moment.duration(this.$moment(this.providerInfo.date).diff(this.$moment()))
        ),
        cardAngle: 0, // 動態計算背景角度
        debounceCalculateCardAngle: null //防抖機制
      }
    },
    computed: {
      specialUI() {
        return this.providerInfo.specialUI === 'VIP-style'
          ? 'gift-pack-frame-vip gift-pack-card-vip-bg'
          : 'gift-pack-frame gift-pack-card-bg'
      },
      canBuyItem() {
        const isbuyMax =
          this.providerInfo.maxBuyLimit !== 0 &&
          this.providerInfo.userBuyCount >= this.providerInfo.maxBuyLimit
        return isbuyMax
      },
      vipIcon() {
        return this.getImage(this.providerInfo.vipIcon)
      },
      remainingBalance({ $store }) {
        return $store.getters['yoeShop/remainingBalance']
      },
      imageUrlPath({ $store }) {
        return $store.getters['image/shopImageUrlPath']
      },
      formatBuyLimitText() {
        if (this.isLevelPack) {
          // 如果是 VIP 禮包，使用含次數格式
          return (
            this.$t(this.providerInfo.buyLimitText) +
            ' ' +
            this.providerInfo.maxBuyLimit +
            ' ' +
            this.$t('vip_limit_purchase').slice(-1)
          )
        } else {
          // 如果非VIP禮包，使用文字+回傳次數格式
          return (
            this.$t(this.providerInfo.buyLimitText) +
            ' ' +
            '(' +
            this.providerInfo.userBuyCount +
            '/' +
            this.providerInfo.maxBuyLimit +
            ')'
          )
        }
      },
      shopImage() {
        return (url) => {
          const imageName = this.imageLoadingError ? url.slice(0, -5) + '.png' : url
          const imagePath = this.imageUrlPath + '/' + imageName
          return imagePath
        }
      },
      maxBuyCountdown() {
        if (!this.needCountdown) return ''

        if (this.duration <= 0) {
          return this.$t('expired')
        }

        // 計算小時和分鐘
        const hours = Math.floor(this.duration / 3600)
        const minutes = Math.floor((this.duration % 3600) / 60)
        const seconds = this.duration % 60
        // 最後59秒，顯示秒數
        if (hours === 0 && minutes === 0 && seconds > 0) {
          return `${seconds}s`
        }
        // 其他情況顯示小時:分鐘
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
      },
      cardBackgroundStyle() {
        // 因IOS裝置無法正常顯示整個漸層background，故需拆分為BGC和BGI
        const backgroundConfig = this.$UIConfig.swiperBox.giftPackGradientBG
        const cardBGBase =
          this.providerInfo.specialUI === 'VIP-style'
            ? backgroundConfig.vipBackground
            : backgroundConfig.background
        // background-color部分
        const baseColor = cardBGBase.split(',').pop().trim()
        // background-image部分
        const gradients = cardBGBase
          .substring(0, cardBGBase.lastIndexOf(',')) // 獲取除了bgc值外的所有漸層
          .format(this.cardAngle) // 計算角度參數

        return {
          backgroundColor: baseColor,
          backgroundImage: gradients
        }
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      yoeShopArray() {
        return this.$store.getters['yoeShop/yoeShopArray']
      },
      countDownDisplay() {
        const hasCountdown = this.needCountdown
        // 如果沒有倒數文字，就顯示
        if (!hasCountdown) {
          return true
        }

        // 如果有倒數且時間大於 0，就顯示
        if (this.duration > 0) {
          return true
        }

        // 如果有倒數但時間已到，則不顯示
        if (hasCountdown && this.duration <= 0) {
          return false
        }
      },
      isDailyPack() {
        return this.providerInfo.id.includes('dly')
      },
      isWeeklyPack() {
        return this.providerInfo.id.includes('wly')
      },
      isMonthlyPack() {
        return this.providerInfo.id.includes('mly')
      },
      needCountdown() {
        return this.isDailyPack || this.isWeeklyPack || this.isMonthlyPack
      }
    },
    watch: {
      yoeShopArray: {
        deep: true,
        handler(newVal) {
          if (newVal) {
            // store 資料更新後，強制元件重新渲染
            this.$forceUpdate()
          }
        }
      },
      // 監聽傳入的資料變化，重新計算角度
      providerInfo: {
        async handler() {
          await this.$nextTick()
          this.debounceCalculateCardAngle()
        },
        deep: true
      }
    },
    mounted() {
      this.debounceCalculateCardAngle = debounce(this.calculateCardAngle, 100)
      this.startCountdown()
      this.debounceCalculateCardAngle()
      // 添加 resize 監聽器
      window.addEventListener('resize', this.debounceCalculateCardAngle)
    },
    beforeDestroy() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      // 清理 resize 監聽器
      window.removeEventListener('resize', this.debounceCalculateCardAngle)
    },
    methods: {
      errorStickerHandler() {
        this.imageLoadingError = true
      },
      async showPaymentMethod(providerInfo) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        if (providerInfo.maintained) return
        if (this.providerInfo.showConfirm) this.showConfirmDialog = true
        else this.handleConfirmWithExpiredCheck()
      },
      handleConfirmWithExpiredCheck() {
        if (this.needCountdown && this.duration <= 0) {
          this.showConfirmDialog = false
          this.$notify.error(this.$t('salesTimeEnded'))
          return
        }
        this.handleConfirm()
      },
      calculateCardAngle() {
        this.$nextTick(() => {
          setTimeout(() => {
            const card = this.$refs.giftPackCard
            if (card) {
              const width = card.offsetWidth
              const height = card.offsetHeight
              // 計算對角線角度（負值是因為我們要從右下角開始）
              this.cardAngle = -90 + Math.round(Math.atan2(height, width) * (180 / Math.PI))
            }
          }, 500)
        })
      },
      handleConfirm() {
        this.$store.commit('yoeShop/SET_PRODUCT_INFO', this.providerInfo)
        this.$nuxt.$emit('root:showYoeDialogStatus', {
          show: true,
          shopItem: this.providerInfo,
          cancel: false,
          purChaseCancel: false,
          onClose: async () => {
            // 對話框關閉時重新獲取數據
            await this.$store.dispatch('yoeShop/fetchYoeGame')
          }
        })
      },
      getImage(pictureName) {
        if (pictureName) return this.shopImage(pictureName)
        return ''
      },
      computedCountDown() {
        const endTime = this.$moment(this.providerInfo.date)
        const diffDuration = this.$moment.duration(endTime.diff(this.$moment()))

        this.duration = Math.floor(diffDuration.asSeconds())
        // 當時間到了就清除計時器
        if (this.duration <= 0) {
          if (this.timer) {
            clearInterval(this.timer)
          }
        }
      },
      startCountdown() {
        // 只有當是 pack 類型時才啟動計時器
        if (!this.needCountdown) return

        this.computedCountDown()
        this.timer = setInterval(() => {
          this.computedCountDown()
        }, 1000)
      }
    }
  }
</script>
<style lang="scss" scoped>
  //color
  $primary: map-get($colors, 'primary');
  $default-content: map-get($colors, 'default-content');
  $button-content: map-get($colors, 'button-content');

  .gift-pack-card-bg {
    position: relative;
    padding: 2px;
    border-radius: 10px;
    .gift-pack-card {
      border-radius: 10px;
      box-sizing: border-box;
      border: 2px solid #0933b7;
      background-blend-mode: screen, screen, multiply, normal;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0.4px;
      .gift-pack-title-text {
        color: var(--Primary-primary, #ffed30);
        text-align: center;
        font-family: Poppins;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 22px; /* 157.143% */
        letter-spacing: 0.1px;
      }
      .gift-pack-title {
        height: 42px;
        color: $default-content;
        display: inline-grid;
        align-items: center;
      }
      .gift-pack-sub-title {
        color: $default-content;
      }
      .gift-pack-vipIcon {
        position: absolute;
        top: -18px;
        z-index: 2;
      }
      .rebate-card {
        width: 70%;
        position: absolute;
        top: 0;
        transform: translateY(-50%);
        z-index: 2;
      }
      .gift-pack-discountIcon {
        width: 30%;
        position: absolute;
        top: 20%;
        left: -10px;
        transform: rotate(-20deg);
      }
      .gift-pack-content {
        color: $default-content;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0.1px;
        .main-content {
          span {
            color: #ffed30;
            font-weight: 700;
          }
        }
        .bonus-title {
          .bonus-line-left {
            width: 30px;
            border: 1px solid;
            border-image-source: linear-gradient(270deg, rgba(247, 182, 117, 0) 0%, #ffed30 100%);
            border-image-slice: 1;
            transform: rotate(180deg);
          }
          .bonus-line-right {
            transform: rotate(0deg);
          }
          .bonus-square {
            width: 4px;
            height: 4px;
            background-color: #facb94;
            transform: rotate(45deg);
          }
        }
        .sub-content {
          height: 40px;
          font-weight: 400;
          font-size: 12px;
        }
      }
      .time-count-down {
        height: 20px;
        background: linear-gradient(
          270deg,
          rgba(80, 51, 37, 0) 0%,
          rgba(80, 51, 37, 0.6) 50%,
          rgba(80, 51, 37, 0) 100%
        );
        background-blend-mode: multiply;
      }
      .gift-pack-price {
        width: 100%;
        margin: 0 auto;
        border-radius: 20px;
        background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
        color: $button-content;
        font-style: normal;
        font-weight: 500;
        line-height: 120%;
        letter-spacing: 1.071px;
        &:disabled {
          background: #ffffff1f;
          color: #ffffff4d;
        }
      }
      .right-top-remark {
        position: absolute;
        top: 8px;
        right: 8px;
        .remark-icon {
          padding: 2px;
          width: 20px;
          height: 20px;
          min-width: unset;
          border-radius: 100px;
          background: linear-gradient(180deg, rgba(153, 144, 144, 0.5) 0%, #211616 100%);
        }
      }
    }
  }
  .gift-max-count {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.4px;
  }
  .right-top-remark-text {
    font-size: 12px;
    border-radius: 4px;
    background-color: rgba(97, 97, 97, 0.9);
  }
  .gift-pack-card-vip-bg {
    .gift-pack-card {
      border: 2px solid #0a126b;
    }
  }
  .limit-buy {
    display: block;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.42353);
    position: absolute;
    z-index: 1;
    top: 0;
  }
  .elevation-text {
    color: var(--content-default-content, #fff);
    text-align: center;
    /* elevation/elevation-1 */
    text-shadow: 0px 2px 1px rgba(0, 0, 0, 0.2), 0px 1px 1px rgba(0, 0, 0, 0.14),
      0px 1px 3px rgba(0, 0, 0, 0.12);
    font-family: 'Noto Sans TC';
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 166.667% */
  }
  @media screen and (max-width: 375px) {
    .gift-pack-card-bg {
      .gift-pack-card {
        .gift-pack-content {
          font-size: 12px;
          .bonus-title {
            .bonus-line-left {
              width: 25px;
            }
          }
        }
      }
    }
  }
</style>
