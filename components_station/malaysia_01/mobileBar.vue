<template>
  <v-app-bar
    app
    elevate-on-scroll
    clipped-left
    color="app-bar-gradient"
    class="mobile-bar"
    :height="$vuetify.breakpoint.smAndDown ? '68px' : undefined"
    style="border-bottom: 2px solid #ffed30"
    ref="headerRef"
    :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
  >
    <v-row no-gutters justify="center" class="px-lg-8 px-md-6 px-4 py-0">
      <!-- logo & menu -->
      <v-col>
        <v-row no-gutters align="center">
          <v-app-bar-nav-icon
            v-if="$vuetify.breakpoint.mdAndUp"
            style="width: auto; height: auto"
            class="mr-lg-6 mr-sm-4 mr-2"
            @click="switchDrawer"
          >
            <!-- 必須使用 google font icon 才能正常顯示 app-bar-button 的漸層色 -->
            <!-- 但 google font icon 的邊緣會因漸層色糊掉，故直接寫 svg -->
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path :d="menuIconPath" fill="url(#menuGradient)" />
              <defs>
                <linearGradient
                  id="menuGradient"
                  x1="12"
                  y1="6"
                  x2="12"
                  y2="18"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop class="menu-gradient-start" />
                  <stop offset="1" class="menu-gradient-end" />
                </linearGradient>
              </defs>
            </svg>
          </v-app-bar-nav-icon>

          <v-toolbar-title class="pa-0">
            <v-row no-gutters align="center" justify="center">
              <!-- logo -->
              <div class="pr-xl-2 pr-lg-2 pr-md-2 pr-0">
                <v-img
                  contain
                  :src="logoImg"
                  width="130px"
                  class="cursor-pointer"
                  @click="goHome()"
                  @error="imgError()"
                />
              </div>
            </v-row>
          </v-toolbar-title>

          <v-spacer />

          <!-- action btn group -->
          <!-- 必須使用google font icon 才能正常顯示app-bar-button的漸層色 -->
          <div v-if="$vuetify.breakpoint.mdAndUp" class="pr-4">
            <!-- payment -->
            <v-btn
              text
              color="transparent"
              :class="[
                'mobileBar-flexcol px-1 v-btn-no-transform',
                { 'mobileBar-disable-button': maintainSystem[0].maintaining }
              ]"
              :disabled="paymentDialogDisable"
              @click="goPayment()"
            >
              <span class="material-symbols-outlined app-bar-button"> paid </span>
              <span class="app-bar-button">
                {{ $t('stored_mall') }}
              </span>
            </v-btn>
            <!-- mail -->
            <v-btn
              text
              color="transparent"
              :class="[
                'mobileBar-flexcol px-1 v-btn-no-transform',
                { 'mobileBar-disable-button': maintainSystem[0].maintaining }
              ]"
              :disabled="maintainSystem[0].maintaining"
              @click="checkMaintenanceForBtn('root:mailDialogStatus')"
            >
              <v-badge
                v-if="getNotyCount.enable"
                overlap
                color="error"
                :content="getNotyCount.content"
              >
                <span class="material-symbols-outlined app-bar-button"> mail </span>
              </v-badge>
              <span v-else class="material-symbols-outlined app-bar-button"> mail </span>
              <span class="app-bar-button">
                {{ $t('mail') }}
              </span>
            </v-btn>
            <!-- chat -->
            <v-btn
              text
              color="transparent"
              :class="[
                'mobileBar-flexcol px-1 v-btn-no-transform',
                { 'mobileBar-disable-button': maintainSystem[0].maintaining }
              ]"
              :disabled="maintainSystem[0].maintaining"
              @click="checkMaintenanceForBtn('root:showChatRoomDialogStatus')"
            >
              <v-badge
                v-if="getChatNotyCount.enable"
                overlap
                color="error"
                :content="getChatNotyCount.content"
              >
                <span class="material-symbols-outlined app-bar-button"> sms </span>
              </v-badge>
              <template v-else>
                <span class="material-symbols-outlined app-bar-button"> sms </span>
              </template>
              <span class="app-bar-button">
                {{ $t('chat_session') }}
              </span>
            </v-btn>
          </div>
          <div>
            <v-row no-gutters v-if="!isLogin">
              <v-col cols="12" class="pa-0 d-flex justify-end">
                <v-btn
                  depressed
                  rounded
                  color="gradient-button"
                  :disabled="maintainSystem[0].maintaining"
                  :class="[
                    { 'text-subtitle-2': $vuetify.breakpoint.width < 340 },
                    'btn-animation',
                    'button-content--text'
                  ]"
                  @click="checkMaintenanceForBtn('root:showLoginDialogStatus')"
                >
                  {{ $t('login') }}
                </v-btn>
              </v-col>
            </v-row>
            <v-row v-else no-gutters class="justify-end align-center">
              <!-- coin & role info -->
              <div>
                <div
                  :class="[
                    'd-flex',
                    $vuetify.breakpoint.mdAndUp ? 'flex-row' : 'flex-column-reverse',
                    $vuetify.breakpoint.mdAndUp ? 'align-center' : 'align-end',
                    'justify-end'
                  ]"
                >
                  <v-row no-gutters align="center">
                    <!-- coin -->
                    <v-chip class="px-0 mt-md-0 mt-1" color="app-bar-item">
                      <v-row no-gutters align="center">
                        <!-- coin img -->
                        <v-img
                          :src="getImage('<EMAIL>')"
                          :srcset="getSrcset('coin')"
                          width="24"
                          height="24"
                          class="d-inline-block vertical-middle ml-2"
                        />
                        <!-- coin num -->
                        <count-animate
                          class="px-2 text-sm-body-2 default-content--text"
                          :style="{
                            'font-size': $vuetify.breakpoint.mdAndUp
                              ? '14px !important'
                              : '12px !important'
                          }"
                          :start-val="startBalance"
                          :end-val="selfBalance"
                          :duration="1000"
                        />
                      </v-row>
                    </v-chip>
                  </v-row>
                  <!-- role info -->
                  <div
                    :class="[
                      'd-flex',
                      $vuetify.breakpoint.mdAndUp ? 'flex-column' : 'flex-row-reverse',
                      $vuetify.breakpoint.mdAndUp ? 'align-end' : 'align-center',
                      'justify-end',
                      'pl-2'
                    ]"
                  >
                    <!-- user name -->
                    <v-row
                      no-gutters
                      justify="end"
                      class="font-weight-regular custom-text-noto text-body-2"
                      style="font-size: 14px !important"
                    >
                      <div class="d-flex align-center flex-nowrap">
                        <vipLevelIcon
                          class="mr-1"
                          width="24"
                          height="24"
                          :vip-level="profile.vipLevel"
                        ></vipLevelIcon>
                        <span
                          class="default-content--text custom-text-noto text-body-2"
                          style="font-size: 14px"
                        >
                          {{ profile.userName }}
                        </span>
                      </div>
                    </v-row>
                  </div>
                </div>
              </div>
              <!-- role img -->
              <div class="pa-0 d-flex justify-center align-center">
                <roleProfile />
              </div>
            </v-row>
          </div>
        </v-row>
      </v-col>
    </v-row>
  </v-app-bar>
</template>

<script>
  import scroll from '~/mixins/scroll'
  import chat from '~/mixins/chatroom/chat'
  import images from '~/mixins/images'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import utilWhiteList from '@/utils/whiteList.js'
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'Mobilebar',
    mixins: [scroll, chat, images, preLoginAction, orientation],
    components: {
      roleProfile: () => import('~/components/roleProfile'),
      vipLevelIcon: () => import('~/components/player_info/vipLevelIcon')
    },

    data() {
      return {
        bg: 'transparent',
        elevation: 0,
        startBalance: Number(this.$store.getters['role/balance']),
        showLogoutDialogStatus: false,
        getBackBtnStatus: false,
        windowTop: 0,
        logoImg: `/malaysia_01/logo.webp`,
        drawer: null,
        headerHeight: 0
      }
    },
    computed: {
      paymentDialogDisable({ $store }) {
        if (this.maintainSystem[0].maintaining) return true
        if (!process.client) return false
        const userName = $store.getters['role/userName']
        const level = $store.getters['role/vipLevel']
        const url = window.location.origin
        return utilWhiteList.paymentDialogDisable(userName, level, url)
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      selfBalance({ $store }) {
        return Number($store.getters['role/balance'])
      },
      profile({ $store }) {
        return {
          userName: $store.getters['role/userName'],
          level: $store.getters['role/level'],
          phoneNumber: $store.getters['role/phoneNumber'],
          vipLevel: $store.getters['role/vipLevel']
        }
      },
      getNotyCount({ $store }) {
        const getNotyCount = $store.getters['mail/getNotyCount']
        const result = {}
        if (getNotyCount === 0) {
          result.enable = false
          result.content = 0
        } else if (getNotyCount > 99) {
          result.enable = true
          result.content = '99+'
        } else {
          result.enable = true
          result.content = getNotyCount
        }

        return result
      },
      payVip({ $store }) {
        return $store.getters[`malaysia_01/payment/vipState`]
      },
      selfLimit({ $store }) {
        return $store.getters['role/limit']
      },
      isInErrorPage({ $store }) {
        return $store.getters['isInErrorPage']
      },
      menuIconPath() {
        return this.drawer
          ? 'M4 18C3.71667 18 3.47917 17.9042 3.2875 17.7125C3.09583 17.5208 3 17.2833 3 17C3 16.7167 3.09583 16.4792 3.2875 16.2875C3.47917 16.0958 3.71667 16 4 16H15C15.2833 16 15.5208 16.0958 15.7125 16.2875C15.9042 16.4792 16 16.7167 16 17C16 17.2833 15.9042 17.5208 15.7125 17.7125C15.5208 17.9042 15.2833 18 15 18H4ZM18.9 16.3L15.3 12.7C15.1 12.5 15 12.2667 15 12C15 11.7333 15.1 11.5 15.3 11.3L18.9 7.7C19.0833 7.51667 19.3167 7.425 19.6 7.425C19.8833 7.425 20.1167 7.51667 20.3 7.7C20.4833 7.88333 20.575 8.11667 20.575 8.4C20.575 8.68333 20.4833 8.91667 20.3 9.1L17.4 12L20.3 14.9C20.4833 15.0833 20.575 15.3167 20.575 15.6C20.575 15.8833 20.4833 16.1167 20.3 16.3C20.1167 16.4833 19.8833 16.575 19.6 16.575C19.3167 16.575 19.0833 16.4833 18.9 16.3ZM4 13C3.71667 13 3.47917 12.9042 3.2875 12.7125C3.09583 12.5208 3 12.2833 3 12C3 11.7167 3.09583 11.4792 3.2875 11.2875C3.47917 11.0958 3.71667 11 4 11H12C12.2833 11 12.5208 11.0958 12.7125 11.2875C12.9042 11.4792 13 11.7167 13 12C13 12.2833 12.9042 12.5208 12.7125 12.7125C12.5208 12.9042 12.2833 13 12 13H4ZM4 8C3.71667 8 3.47917 7.90417 3.2875 7.7125C3.09583 7.52083 3 7.28333 3 7C3 6.71667 3.09583 6.47917 3.2875 6.2875C3.47917 6.09583 3.71667 6 4 6H15C15.2833 6 15.5208 6.09583 15.7125 6.2875C15.9042 6.47917 16 6.71667 16 7C16 7.28333 15.9042 7.52083 15.7125 7.7125C15.5208 7.90417 15.2833 8 15 8H4Z'
          : 'M4 18C3.71667 18 3.47917 17.9042 3.2875 17.7125C3.09583 17.5208 3 17.2833 3 17C3 16.7167 3.09583 16.4792 3.2875 16.2875C3.47917 16.0958 3.71667 16 4 16H20C20.2833 16 20.5208 16.0958 20.7125 16.2875C20.9042 16.4792 21 16.7167 21 17C21 17.2833 20.9042 17.5208 20.7125 17.7125C20.5208 17.9042 20.2833 18 20 18H4ZM4 13C3.71667 13 3.47917 12.9042 3.2875 12.7125C3.09583 12.5208 3 12.2833 3 12C3 11.7167 3.09583 11.4792 3.2875 11.2875C3.47917 11.0958 3.71667 11 4 11H20C20.2833 11 20.5208 11.0958 20.7125 11.2875C20.9042 11.4792 21 11.7167 21 12C21 12.2833 20.9042 12.5208 20.7125 12.7125C20.5208 12.9042 20.2833 13 20 13H4ZM4 8C3.71667 8 3.47917 7.90417 3.2875 7.7125C3.09583 7.52083 3 7.28333 3 7C3 6.71667 3.09583 6.47917 3.2875 6.2875C3.47917 6.09583 3.71667 6 4 6H20C20.2833 6 20.5208 6.09583 20.7125 6.2875C20.9042 6.47917 21 6.71667 21 7C21 7.28333 20.9042 7.52083 20.7125 7.7125C20.5208 7.90417 20.2833 8 20 8H4Z'
      }
    },
    watch: {
      windowTop: {
        handler(val) {
          window.onscroll = () => {
            if (this.$vuetify.breakpoint.smOnly) {
              if (val > 415) {
                this.bg = 'white'
              } else {
                this.bg = 'transparent'
              }
            } else if (val > 10) {
              this.bg = 'white'
            } else {
              this.bg = 'transparent'
            }
          }
        }
      }
    },
    created() {
      if (this.$device.isIos || this.$device.isMacOS) {
        this.logoImg = `/malaysia_01/iosLogo.webp`
      }
      this.$nuxt.$on('drawer:acceptDrawer', this.acceptDrawer)
    },
    mounted() {
      window.addEventListener('scroll', this.onScroll)
      this.$nextTick(this.setHeaderHeight)
    },
    beforeDestroy() {
      window.removeEventListener('scroll', this.onScroll)
      this.$nuxt.$off('drawer:acceptDrawer', this.acceptDrawer)
    },
    methods: {
      async checkMaintenanceForBtn(rootEmitName) {
        await this.$store.dispatch('maintain/fetch', true) // 傳入true，不受5秒限制
        if (this.maintainSystem[0].maintaining) return

        if (
          rootEmitName !== 'root:showVipPaymentDialogStatus' ||
          rootEmitName !== 'root:showPaymentDialogStatus'
        ) {
          // 離開Web遊戲館服務
          this.$wsClient.send(
            this.$wsPacketFactory.exitService(this.$xinConfig.WEB_GAME_SERVICE.ID)
          )
          this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
            serviceId: this.$xinConfig.WEB_GAME_SERVICE.ID,
            enable: false,
            connected: false,
            sendId: 0,
            receiveId: 0
          })
        }

        if (!this.isLogin) {
          // 依據『rootEmitName』決定要存入哪些『登入前動作紀錄』
          switch (rootEmitName) {
            case 'root:showVipPaymentDialogStatus':
              if (this.$UIConfig.paymentIndex.showVipTab) {
                this.setPreLoginAction('goPayment', this.goPayment)
              }
              break
            case 'root:showPaymentDialogStatus':
              this.setPreLoginAction('goPayment', this.goPayment)
              break
            case 'root:mailDialogStatus':
              this.setPreLoginAction('mail', this.checkMaintenanceForBtn, 'root:mailDialogStatus')
              break
            case 'root:showChatRoomDialogStatus':
              this.setPreLoginAction(
                'chatRoom',
                this.checkMaintenanceForBtn,
                'root:showChatRoomDialogStatus'
              )
              break
            default: {
              break
            }
          }

          rootEmitName = 'root:showLoginDialogStatus'
        }

        if (rootEmitName === 'root:mailDialogStatus') {
          this.$store.commit('mail/SET_SHOW_NOTY_STATUS', true)
          this.$store.commit('mail/SET_SCROLL_TOP_STATUS', true)
          this.$nuxt.$emit('root:mailDialogStatus', { show: true, name: '' })
        } else if (rootEmitName === 'root:showLoginDialogStatus') {
          this.$nuxt.$emit(rootEmitName, { show: true })
        } else {
          this.$nuxt.$emit(rootEmitName, true)
        }
      },
      showLogoutDiaLog() {
        this.showLogoutDialogStatus = true
      },
      onScroll() {
        this.windowTop = window.top.scrollY
      },
      formatPrice(value) {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      },
      isJson(str) {
        try {
          JSON.parse(str)
        } catch (e) {
          return false
        }
        return true
      },
      async goPayment() {
        if (this.isLogin) {
          await this.$store.dispatch('role/updateUserDetail')
        }
        // 使用 UIConfig 來判斷是否要顯示 VIP 優惠窗
        if (this.payVip && this.selfLimit !== 0 && this.$UIConfig.paymentIndex.showVipTab) {
          this.checkMaintenanceForBtn('root:showVipPaymentDialogStatus')
        } else {
          this.checkMaintenanceForBtn('root:showPaymentDialogStatus')
        }
      },
      async imgError() {
        this.logoImg = `/malaysia_01/logo.png`
        const title = this.$t('reminder')
        await this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: this.$t('lowerThanRequiredVersion')
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      async goHome() {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        this.$nuxt.$loading.start()
        if (this.$route.fullPath === '/') {
          this.$nuxt.error(null)
          this.$router.push(this.localePath('/'))
        } else {
          this.$router.replace(this.localePath('/'))
        }
        this.$nuxt.$loading.finish()
        this.scollToTop()
      },
      acceptDrawer(drawer) {
        this.drawer = drawer
      },
      switchDrawer() {
        this.drawer = !this.drawer
        this.$nuxt.$emit('drawer:switchDrawer', this.drawer, false)
      },
      setHeaderHeight() {
        if (this.$refs.headerRef) {
          this.headerHeight = this.$refs.headerRef.$el.offsetHeight
          this.$emit('update-header-height', this.headerHeight)
        }
      }
    }
  }
</script>
<style lang="scss">
  $primary-variant-2: map-get($colors, 'primary-variant-2');
  $primary-variant-3: map-get($colors, 'primary-variant-3');

  .mobile-bar {
    .v-toolbar__content {
      padding-left: 0px !important;
      padding-right: 0px !important;
    }
  }
  .mobileBar-flexcol .v-btn__content {
    flex-direction: column;
  }
  .btn-animation {
    animation: pulse 1s ease-in-out infinite alternate;
  }
  .mobileBar-disable-button {
    .app-bar-button {
      background: #ffffffb0;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
  .menu-gradient-start {
    stop-color: $primary-variant-3;
  }
  .menu-gradient-end {
    stop-color: $primary-variant-2;
  }
  .v-btn-no-transform {
    text-transform: none !important;
  }

  @keyframes pulse {
    from {
      transform: scale(1);
    }
    to {
      transform: scale(1.1);
    }
  }
</style>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
