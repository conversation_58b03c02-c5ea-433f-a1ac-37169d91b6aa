<template>
  <v-dialog
    fullscreen
    max-width="620px"
    persistent
    v-model="showLoginIframeStatus.show"
    content-class="yoe-dialog-background"
  >
    <v-card tile color="transparent" class="rounded-t-lg">
      <v-card-text class="d-flex justify-center align-center pa-0 yoe-dialog-content">
        <iframe
          title="yoeframe"
          ref="yoeGameFrame"
          class="iframe-height-normal"
          :src="showLoginIframeStatus.url"
          id="yoeIframe"
          loading="lazy"
          scrolling="no"
          frameborder="0"
          style="display: block; border: 0px; width: 100%"
          allowfullscreen
          @load="handleIframeLoad"
        >
        </iframe>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
  const STATION = process.env.STATION
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'
  import orientation from '~/mixins/orientation'
  import images from '~/mixins/images'
  const esgAuth = require(`~/mixins_station/${STATION}/esgAuth`).default
  export default {
    name: 'loginDialog',
    mixins: [hiddenScrollHtml, orientation, images, analytics, esgAuth],
    props: {
      showLoginIframeStatus: {
        type: Object,
        required: true,
        default: () => ({ show: false, url: '' })
      }
    },
    data() {
      return {
        showLoginIframeStatusTmp: false,
        isInit: false,
        alreadyOwneItemArray: [],
        currentIframeUrl: '',
        urlChangeInterval: null
      }
    },
    watch: {
      showLoginIframeStatus: {
        async handler(value) {
          console.log('showLoginIframeStatus', value)
          this.showLoginIframeStatusTmp = value.show
          if (value.show) {
            this.startUrlMonitoring()
            this.setupIframe()
          } else {
            this.stopUrlMonitoring()
          }
        }
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      }
    },
    mounted() {
      console.log('showLoginIframeStatus', this.showLoginIframeStatus.url)
      this.setupIframe()
      this.startUrlMonitoring()
    },
    beforeDestroy() {
      this.stopUrlMonitoring()
    },
    methods: {
      setupIframe() {
        this.getIframeElement()
          .then((iframe) => {
            iframe.src = this.showLoginIframeStatus.url
          })
          .catch((error) => {
            console.error('Failed to get iframe:', error)
          })
      },
      getIframeElement() {
        return new Promise((resolve, reject) => {
          const maxAttempts = 10
          let attempts = 0
          const tryGetIframe = () => {
            const iframe = this.$refs.yoeGameFrame || document.getElementById('yoeIframe')
            if (iframe) {
              resolve(iframe)
              return
            }
            attempts++
            if (attempts >= maxAttempts) {
              reject(new Error('Max attempts reached'))
              return
            }
            setTimeout(tryGetIframe, 100)
          }
          tryGetIframe()
        })
      },
      handleIframeLoad() {
        this.checkIframeUrl()
      },
      startUrlMonitoring() {
        // 每500ms檢查一次iframe的URL
        this.urlChangeInterval = setInterval(() => {
          this.checkIframeUrl()
        }, 500)
      },
      stopUrlMonitoring() {
        if (this.urlChangeInterval) {
          clearInterval(this.urlChangeInterval)
          this.urlChangeInterval = null
        }
      },
      checkIframeUrl() {
        const iframe = this.$refs.yoeGameFrame
        if (iframe) {
          try {
            const newUrl = iframe.contentWindow.location.href
            if (newUrl !== this.currentIframeUrl) {
              this.currentIframeUrl = newUrl
              this.handleUrlChange(newUrl)
            }
          } catch (error) {
            // 跨域錯誤處理
            console.warn('無法訪問iframe URL:', error)
          }
        }
      },
      handleUrlChange(newUrl) {
        console.log('iframe URL changed:', newUrl)
        // 檢查URL是否包含ESGame的認證參數
        const open_id = this.getQueryParam('open_id')
        const timestamp = this.getQueryParam('timestamp')
        const ip = this.getQueryParam('ip')
        const sign = this.getQueryParam('sign')

        if (open_id && timestamp && ip && sign) {
          const partnerSign = this.md5(open_id + timestamp + ip + this.secretKey)

          if (sign === partnerSign) {
            this.handleLoginSuccess(open_id)
          } else {
            this.handleLoginFail()
          }
        }
      },
      handleLoginSuccess(open_id) {
        console.log('Login successful, ESGID:', open_id)
        this.sendAnalytics()
        this.closeDialog()
      },
      handleLoginFail() {
        console.log('Login failed')
        this.closeDialog()
      },
      closeDialog() {
        const dialogStatus = {
          show: false,
          shopItem: {},
          cancel: true
        }
        this.$emit('update:showLoginIframeStatus', dialogStatus)
        this.$nuxt.$emit('yoe:dialogStatusChange', dialogStatus)
      },
      sendAnalytics() {
        const analyticsObj = {
          username: this.userName,
          type: 7, // 登入成功
          point: 0
        }
        this.paymentAnalyticsToPlatform(analyticsObj)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .rounded-t-lg {
    position: relative;
    .yoe-dialog-title {
      z-index: 1;
    }
    iframe {
      position: absolute;
      bottom: 0;
    }
  }
  .iframe-height-normal {
    height: 100vh;
  }
  ::v-deep {
    .yoe-dialog-background {
      background: #1a0b0bcc !important;
    }
  }
</style>
