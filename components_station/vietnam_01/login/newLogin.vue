<template>
  <div>
    <v-dialog
      v-model="showLoginDialogStatusTmp.show"
      max-width="600"
      :fullscreen="breakpoint.xsOnly"
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-container class="pa-0 rounded-3">
        <customDialogTitle :title="$t('login').toUpperCase()" @closeDialog="onCancel" />
        <div id="login-card" :class="breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm'">
          <v-card-text class="my-6 pa-0">
            <div v-if="$liff.isInClient() || !$ua['_ua'].includes('Line')">
              <v-row
                v-if="$liff.isInClient()"
                no-gutters
                align="center"
                justify="center"
                class="px-4 px-sm-6 d-flex flex-nowrap"
              >
                <v-col cols="12" class="mt-3 mb-6" align="center" justify="center">
                  <v-btn
                    :disabled="maintainSystem[0].maintaining"
                    large
                    :color="$UIConfig.defaultBtnColor"
                    depressed
                    @click="firstOpenQPPCheck(1)"
                    class="pl-3 pr-4 d-flex justify-space-between"
                    :width="breakpoint.smAndUp ? '320' : '100%'"
                  >
                    <span class="custom-material-symbols-outlined-h5 default-content--text pr-2">
                      phone_iphone
                    </span>
                    <span
                      class="text-body-2 font-weight-bold default-content--text d-flex justify-center w-100"
                      >{{ $t('phone_login_btn_liff') }}</span
                    >
                  </v-btn>
                </v-col>
              </v-row>
            </div>
            <div
              v-if="$liff.isInClient() || $ua['_ua'].includes('Line')"
              class="d-flex justify-center rounded-b-0 mx-4 mx-sm-6 mb-4"
              :class="{ 'pt-3': !$liff.isInClient() && $ua['_ua'].includes('Line') }"
            >
              <v-row no-gutters align="center" justify="center" class="d-flex flex-nowrap">
                <v-col cols="12" align="center" justify="center">
                  <v-btn
                    :disabled="maintainSystem[0].maintaining || !lineItem.status"
                    :ripple="false"
                    large
                    depressed
                    @click="firstOpenQPPCheck(lineItem.id)"
                    class="pl-0 pr-9 d-flex justify-space-between line-btn"
                    :width="breakpoint.smAndUp ? '320' : '100%'"
                  >
                    <v-img :width="44" :src="lineItem.logoFile" class="ma-0 line-btn-logo" />
                    <span
                      class="text-body-2 font-weight-bold default-content--text d-flex justify-center w-100"
                      >{{ $t('line_login_btn') }}</span
                    >
                  </v-btn>
                </v-col>
              </v-row>
            </div>
            <div v-else class="d-flex justify-center rounded-b-0">
              <div
                v-for="item in loginMethod"
                :key="item.id"
                elevation="0"
                :class="item.id === 1 && breakpoint.xsOnly ? 'pa-0' : 'pa-2 pb-6'"
              >
                <v-hover v-slot="{ hover }">
                  <div class="d-inline">
                    <v-btn
                      v-if="!(item.id === 1 && breakpoint.xsOnly)"
                      :disabled="maintainSystem[0].maintaining || !item.status"
                      :class="[
                        'translate-y-btn',
                        'shadow-none',
                        {
                          maintenance: maintainSystem[0].maintaining,
                          'ripple-animation-btn': item.id === 1,
                          'grey-5': item.id !== 1
                        }
                      ]"
                      fab
                      large
                      @click="firstOpenQPPCheck(item.id)"
                    >
                      <v-icon v-if="hover && maintainSystem[0].maintaining"> mdi-wrench </v-icon>
                      <v-img
                        v-if="
                          (item.iconFile && !hover && maintainSystem[0].maintaining) ||
                          (item.iconFile && !maintainSystem[0].maintaining)
                        "
                        :max-width="item.maxWidth"
                        :src="item.iconFile"
                        class="ma-5"
                      />
                      <span
                        v-if="
                          (item.icon && !hover && maintainSystem[0].maintaining) ||
                          (item.icon && !maintainSystem[0].maintaining)
                        "
                        class="custom-material-symbols-outlined-h5"
                        :class="[loginConfig.phoneIconColor]"
                      >
                        {{ item.icon }}
                      </span>
                    </v-btn>
                  </div>
                </v-hover>
              </div>
            </div>
            <div
              v-if="$liff.isInClient() || $ua['_ua'].includes('Line')"
              class="d-flex justify-center mx-4 mx-sm-6 mb-6"
            >
              <v-alert
                text
                dense
                icon="mdi-information-outline"
                color="primary"
                border="left"
                class="w-100 ma-0"
                :width="breakpoint.smAndUp ? '320' : ''"
              >
                <span class="text-body-2">{{ $t('third_party_not_support') }}</span>
              </v-alert>
            </div>
            <v-row
              no-gutters
              class="px-4 px-sm-6 pb-sm-6"
              :class="!$liff.isInClient() && $ua['_ua'].includes('Line') ? 'pb-6' : 'pb-0'"
            >
              <v-divider
                v-if="breakpoint.smAndUp || (!$liff.isInClient() && $ua['_ua'].includes('Line'))"
            /></v-row>
            <v-row no-gutters>
              <v-col
                cols="12"
                class="default-content--text custom-text-noto px-4 px-sm-12 pt-0 pb-1 text-left text-body-2"
              >
                {{ $t('registration_terms') }}
              </v-col>
            </v-row>
            <v-row no-gutters class="px-4 px-sm-12 terms-btn">
              <v-col
                v-for="(info, index) in companyInfoList"
                :key="index"
                cols="auto"
                :class="[
                  'custom-text-noto text-left pt-1',
                  breakpoint.xsOnly ? 'text-caption' : 'text-sm-body-2'
                ]"
              >
                <a
                  :href="
                    loginConfig.companyInfoList.useHerf &&
                    info.name !== 'intellectual_property_policy'
                      ? info.url
                      : null
                  "
                  elevation="0"
                  :small="breakpoint.xsOnly ? true : false"
                  class="px-0"
                  color="primary"
                  :style="loginConfig.companyInfoList.style"
                  @click.prevent="handleClick(info)"
                >
                  {{ $t(info.name) }}
                </a>

                <span v-show="index !== companyInfoList.length - 1" class="grey-2--text px-1">
                  |
                </span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col
                cols="12"
                :class="[
                  'px-4 px-sm-14 pt-2 text-left pt-2 default-content--text',
                  breakpoint.xsOnly ? 'text-caption' : 'text-sm-body-2'
                ]"
              >
                <ul>
                  <li v-for="(noty, index) in loginConfig.loginNotyArray" :key="index">
                    {{ $t(noty) }}
                  </li>
                </ul>
              </v-col>
            </v-row></v-card-text
          >
        </div>
      </v-container>
    </v-dialog>
    <loginDialog v-if="showIframeDialog.show" :show-login-iframe-status="showIframeDialog" />
  </div>
</template>

<script>
  const STATION = process.env.STATION
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'
  import uploadPhoto from '@/mixins/uploadPhoto.js'
  import facebook from '@/mixins/facebook.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import lineMgr from '@/mixins/lineMgr.js'
  import esgAuth from '@/mixins/esgAuth'
  export default {
    name: 'NewLogin',
    mixins: [hiddenScrollHtml, analytics, uploadPhoto, facebook, preLoginAction, lineMgr, esgAuth],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),

      loginDialog: () => import(`~/components_station/${STATION}/login/loginDialog`)
    },
    props: {
      showLoginDialogStatus: {
        type: Object,
        default: () => ({ show: false, onCancelNotify: () => {} })
      }
    },
    data() {
      return {
        loginMethod: [],
        showLoginDialogStatusTmp: this.showLoginDialogStatus,
        showIframeDialog: { show: false, url: '' }
      }
    },
    computed: {
      loginConfig() {
        return this.$UIConfig.newLogin
      },
      lineItem() {
        return this.loginMethod.find((method) => method.title === 'LINE')
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      companyInfoList({ $store }) {
        return $store.getters[`${STATION}/companyInfo/list`]
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      isBindPhone({ $store }) {
        return $store.getters['role/phoneNumber'].length > 0
      },
      vipLevel({ $store }) {
        return $store.getters['role/vipLevel']
      },
      loginRecord({ $store }) {
        return $store.getters['role/loginRecord']
      },
      facebookId() {
        return this.$store.getters['role/facebookId']
      },
      userName() {
        return this.$store.getters['role/userName']
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showLoginDialogStatus: {
        handler(val) {
          console.log('showLoginDialogStatus', val)
          this.showLoginDialogStatusTmp = val
        }
      }
    },
    created() {
      this.loginMethod = this.$UIConfig.newLogin.loginMethod
    },
    methods: {
      handleClick(info) {
        if (info.name === 'intellectual_property_policy') {
          // 對於 intellectual_property_policy，使用 Vue Router 導航
          this.$router.push({ path: this.localePath(info.url) }, () => {
            this.closeDialog()
          })
        } else if (this.loginConfig.companyInfoList.click) {
          // 如果 click 為 true，調用 openCompayDialog
          this.openCompayDialog(info)
        } else if (this.loginConfig.companyInfoList.useHerf) {
          // 如果 useHerf 為 true，在新標籤頁打開鏈接
          window.open(info.url, '_blank')
        }
        // 如果 click 和 useHerf 都為 false，不執行任何操作
      },
      openCompayDialog(info) {
        this.$store.commit(`${STATION}/companyInfo/SET_COMPANY_POLICY_TYPE`, info.id)
        this.$nuxt.$emit('root:showCompanyDialogStatus', true)
      },
      async firstOpenQPPCheck(id) {
        this.selectLoginMethod(id)
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },

      async selectLoginMethod(loginId) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) {
          return
        }
        // 封鎖彈窗提示透過localstorage控制
        let localStorageAcceptPopupWindowData =
          this.$localStorage.get('accept_popup_window').expired
        if (
          !localStorageAcceptPopupWindowData ||
          this.$moment(localStorageAcceptPopupWindowData).isBefore(this.$moment())
        ) {
          //30天後失效
          let maturityDate = this.$moment().add(30, 'days')
          this.$localStorage.set('accept_popup_window', { expired: maturityDate })
          alert(this.$t('popup_windows_noty'))
        }

        switch (loginId) {
          case 1:
            this.showIframeDialog = true
            break
          case 2:
            break
          case 3:
            {
              const gameCategory = this.$route.query.gameCategory
              const gameSortType = this.$route.query.gameSortType
              const page = this.$route.query.page
              const searchWord = this.$route.query.searchWord
              const gamePage = window.location.pathname.includes('game')
              const loginType = 3
              const stringValue = {
                loginType,
                gamePage,
                gameCategory,
                gameSortType,
                page,
                searchWord
              }
              this.$localStorage.set('esgSetting', {
                setting: JSON.stringify(stringValue)
              })
              window.location.href = this.facebookLoginUrl
            }
            break
          case 4:
            {
              const gameCategory = this.$route.query.gameCategory
              const gameSortType = this.$route.query.gameSortType
              const page = this.$route.query.page
              const searchWord = this.$route.query.searchWord
              const gamePage = window.location.pathname.includes('game')
              const loginType = 4
              const stringValue = {
                loginType,
                gamePage,
                gameCategory,
                gameSortType,
                page,
                searchWord
              }
              this.$localStorage.set('esgSetting', {
                setting: JSON.stringify(stringValue)
              })
              window.location.href = this.googleLoginUrl
            }
            break
          case 5:
            this.openLineLoginPage()
            break
          case 6:
            {
              const gameCategory = this.$route.query.gameCategory
              const gameSortType = this.$route.query.gameSortType
              const page = this.$route.query.page
              const searchWord = this.$route.query.searchWord
              const gamePage = window.location.pathname.includes('game')
              const loginType = 6
              const stringValue = {
                loginType,
                gamePage,
                gameCategory,
                gameSortType,
                page,
                searchWord
              }
              this.$localStorage.set('esgSetting', {
                setting: JSON.stringify(stringValue)
              })
              window.location.href = this.esgLoginUrl
            }
            break
        }
      },
      closeDialog() {
        this.$nuxt.$emit('root:showLoginDialogStatus', { show: false, onCancelNotify: () => {} })
        // 清除導連
        this.$nuxt.$emit('root:clearRedirect')
      },
      onCancel() {
        this.showLoginDialogStatusTmp.onCancelNotify?.()
        this.closeDialog()
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import url('https://fonts.googleapis.com/css?family=Raleway');

  .glowing-btn {
    width: 64px;
    height: 64px;
    color: #e9b950;
    cursor: pointer;
    padding: 0.35em 1em;
    border-radius: 50%;
    background: none;
    perspective: 2em;
    font-family: 'Raleway', sans-serif;

    -webkit-box-shadow: inset 0px 0px 0.5em 0px #e9b950, 0px 0px 0.5em 0px #e9b950;
    -moz-box-shadow: inset 0px 0px 0.5em 0px #e9b950, 0px 0px 0.5em 0px #e9b950;
    box-shadow: inset 0px 0px 0.5em 0px #e9b950, 0px 0px 0.5em 0px #e9b950;
    animation: border-flicker 6s linear infinite;
  }

  .glowing-txt {
    float: left;
    -webkit-text-shadow: 0 0 0.125em hsl(0 0% 100% / 0.3), 0 0 0.45em #e9b950;
    -moz-text-shadow: 0 0 0.125em hsl(0 0% 100% / 0.3), 0 0 0.45em #e9b950;
    text-shadow: 0 0 0.125em hsl(0 0% 100% / 0.3), 0 0 0.45em #e9b950;
  }

  .glowing-btn::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0.7;
    filter: blur(1em);
    transform: translateY(120%) rotateX(95deg) scale(1, 0.35);
    background: #e9b950;
    pointer-events: none;
  }

  .glowing-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    z-index: -1;
    background-color: #e9b950;
    box-shadow: 0 0 2em 0.2em #e9b950;
    transition: opacity 100ms linear;
    border-radius: 50%;
  }

  .glowing-btn:not(:hover) {
    border: 0.15em solid #e9b950;
  }
  .glowing-btn:hover {
    color: rgba(0, 0, 0, 0.8);
    text-shadow: none;
    animation: none;
  }

  .glowing-btn:hover .glowing-txt {
    animation: none;
  }

  .glowing-btn:hover:before {
    filter: blur(1em);
    opacity: 1;
  }

  .glowing-btn:hover:after {
    opacity: 1;
  }
  .gradient-phone-button {
    background: linear-gradient(90deg, #e9b950 0%, #fea600 99.91%);
  }
  .gradient-border-left {
    width: 100%;
    border: 1px solid;
    border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    -webkit-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    -moz-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    -ms-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    border-image-slice: 1;
    -webkit-border-image-slice: 1;
    -moz-border-image-slice: 1;
    -ms-border-image-slice: 1;
    transform: rotate(180deg);
  }
  .gradient-border-right {
    width: 100%;
    border: 1px solid;
    border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    -webkit-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    -moz-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    -ms-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    border-image-slice: 1;
    -webkit-border-image-slice: 1;
    -moz-border-image-slice: 1;
    -ms-border-image-slice: 1;
    transform: rotate(180deg);
  }
  .terms-btn {
    a {
      text-decoration: underline;
    }
  }

  @keyframes border-flicker {
    0% {
      opacity: 0.1;
    }
    2% {
      opacity: 1;
    }
    4% {
      opacity: 0.1;
    }

    8% {
      opacity: 1;
    }
    70% {
      opacity: 0.7;
    }
    100% {
      opacity: 1;
    }
  }
  #login-card {
    &.scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    &.scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      &.scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
  $line-green: #06c755;
  $line-border-8: #00000014; /* 000000 8% */
  $line-black-10: #0000001a; /* 000000 10% */
  $line-black-30: #0000004d; /* 000000 30% */
  $line-grey-60: #e5e5e599; /* E5E5E5 60% */
  $line-grey-20: #1e1e1e33; /* #1E1E1E 20%  */
  .v-btn {
    &.line-btn {
      background-color: $line-green !important;
      span {
        color: #ffffff;
      }
      .line-btn-logo {
        border-right: $line-border-8 solid 1px;
        margin-right: 30px !important;
      }
      &:hover {
        background: linear-gradient($line-black-10, $line-black-10), $line-green !important;
      }
      &:active {
        background: linear-gradient($line-black-30, $line-black-30), $line-green !important;
      }
      &:disabled {
        border: solid 1px $line-grey-60;
        background-color: #ffffff !important;
        span {
          color: $line-grey-20 !important;
        }
        .line-btn-logo {
          filter: brightness(0) saturate(100%) invert(13%) sepia(0%) saturate(0%) hue-rotate(0deg)
            brightness(96%) contrast(80%) opacity(20%);
          border-right: solid 1px $line-grey-60 !important;
        }
      }
    }
  }
  .v-btn {
    &.shadow-none {
      box-shadow: none !important;
    }
  }
</style>
