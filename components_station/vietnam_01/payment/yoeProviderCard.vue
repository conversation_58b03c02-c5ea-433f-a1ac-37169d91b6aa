<!-- eslint-disable vue/no-v-html -->
<template>
  <div
    :class="['gift-pack-card-bg', { 'gift-pack-card-vip-bg': providerInfo.vipIcon }]"
    :style="outerBackgroundStyle"
  >
    <div class="gift-pack-card custom-text-noto" :style="cardBackgroundStyle" ref="giftPackCard">
      <!--VIP Icon-->
      <v-img eager :src="vipIcon" @error="errorStickerHandler()" class="gift-pack-vipIcon" />
      <v-card
        height="100%"
        color="transparent"
        class="px-3 px-sm-4 py-3 gift-pack-card-layout"
        elevation="0"
      >
        <div v-if="canBuyItem || !countDownDisplay" class="limit-buy"></div>

        <!--回饋-->
        <div v-if="providerInfo.feedback !== ''" class="rebate-card" :style="rebateCardStyle">
          <span>{{ providerInfo.feedback }}</span>
        </div>

        <div class="gift-pack-main-img-block">
          <!--禮包標題-->
          <div class="gift-pack-title-block">
            <div class="gift-pack-title" :class="isLevelPack ? 'text-subtitle-1' : ''">
              <span v-if="providerInfo.title"> {{ providerInfo.title }}</span>
            </div>
            <template v-if="isLevelPack && !isLevelPackXs">
              <div
                class="levelPack-price d-flex justify-center align-center"
                v-for="(item, index) in providerInfo.mainContent"
                :key="index"
              >
                <v-img :src="getImage(item.mainIcon)" max-width="16" max-height="16" class="mr-1" />
                <span class="main-item-title">
                  {{ item.mainText.slice(0, -2) }}
                  <span class="text-subtitle-2 font-weight-bold">{{
                    item.mainText.slice(-2)
                  }}</span>
                </span>
              </div>
            </template>
          </div>
          <div class="limit-and-img">
            <!--購買次數限制-->
            <span
              class="gift-limit-count"
              :class="isLevelPack ? 'gift-pack-reward-card-fill-vip' : ''"
              v-if="providerInfo.isBigCard && buyLimitText !== ''"
            >
              {{ buyLimitText }}
            </span>
            <!--禮包視覺-->
            <v-img
              :src="getImage(providerInfo.mainImage)"
              width="126"
              @error="errorStickerHandler()"
              class="mt-1 main-img"
              eager
            />
          </div>
        </div>

        <!--折扣-->
        <v-img
          :src="getImage(providerInfo.discountIcon)"
          @error="errorStickerHandler()"
          class="gift-pack-discountIcon"
        />

        <!--禮包內容-->
        <div class="levelPack-right-layout">
          <div
            class="mt-1 gift-pack-content"
            :class="{ 'has-countdown': providerInfo.timeCountdownText }"
          >
            <template v-if="!isLevelPack || isLevelPackXs">
              <div
                class="main-content d-flex justify-center align-center"
                v-for="(item, index) in providerInfo.mainContent"
                :key="index"
              >
                <v-img :src="getImage(item.mainIcon)" max-width="16" max-height="16" class="mr-1" />
                <span class="main-item-title">
                  {{ item.mainText.slice(0, -2) }}
                  <span class="text-subtitle-2 font-weight-bold">{{
                    item.mainText.slice(-2)
                  }}</span>
                </span>
              </div>
            </template>
            <!--加贈-->
            <div class="bonus-block" v-if="providerInfo.subContent[0].subText01">
              <div class="bonus-title d-flex justify-center align-center">
                <span class="bonus-line-left"></span>
                <span class="bonus-square mx-1"></span>
                <span>{{ $t('mall_bonus') }}</span>
                <span class="bonus-square mx-1"></span>
                <span class="bonus-line-left bonus-line-right"></span>
              </div>
              <div
                class="sub-content d-flex align-center flex-column"
                :class="isLevelPack && isLevelPackXs ? 'justify-start' : 'justify-center'"
                v-for="(item, index) in providerInfo.subContent"
                :key="index"
              >
                <div class="d-flex justify-center align-center">
                  <v-img
                    :src="getImage(item.subIcon01)"
                    max-width="16"
                    max-height="16"
                    class="mr-1"
                  />
                  <span>{{ item.subText01 }}</span>
                </div>
                <div class="d-flex justify-center align-center">
                  <v-img
                    :src="getImage(item.subIcon02)"
                    max-width="16"
                    max-height="16"
                    class="mr-1"
                  />
                  <span>{{ item.subText02 }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="countDown-and-price">
            <!--倒數-->
            <div
              v-if="providerInfo.isBigCard && providerInfo.timeCountdownText"
              class="time-count-down mt-1"
            >
              <div
                v-if="providerInfo.timeCountdownText !== ''"
                class="d-flex justify-center align-center"
              >
                <v-icon small class="mr-1">{{ providerInfo.countdownIcon }}</v-icon>
                <span> {{ maxBuyCountdown }}</span>
              </div>
            </div>
            <!--價位區域-->
            <!--有開啟導流-->
            <div v-if="showRedirectStyle" class="price-area has-redirect gift-pack-price-fill mt-1">
              <!--導流價位區塊-->
              <div class="d-flex justify-center align-center">
                <span class="mdi mdi-menu-down mr-1"></span>
                <div class="d-flex justify-center align-center">
                  <span class="mr-1">{{ providerInfo.currencyText }}</span>
                  <span>{{ providerInfo.amount }}</span>
                </div>
              </div>
              <!--價格按鈕-->
              <v-btn
                :disabled="isBigCardDisabled"
                class="gift-pack-price-btn"
                @click="showPaymentMethod(providerInfo)"
                small
                depressed
              >
                <div v-if="purchasable">
                  <span>{{ $t('redirect_purchase') }}</span>
                </div>
                <div v-else>
                  <span>{{ $t('purchase_limit_reached') }}</span>
                </div>
              </v-btn>
            </div>
            <!--沒開啟導流-->
            <div v-else class="price-area">
              <v-btn
                :disabled="isBigCardDisabled"
                class="gift-pack-price-btn"
                :class="{
                  'mt-6': providerInfo.isBigCard && providerInfo.redirect,
                  'mt-4': !providerInfo.isBigCard
                }"
                @click="showPaymentMethod(providerInfo)"
                small
                depressed
              >
                <div v-if="providerInfo.isBigCard">
                  <div v-if="purchasable" class="d-flex justify-center">
                    <span class="mr-1">{{ providerInfo.currencyText }}</span>
                    <span>{{ providerInfo.amount }}</span>
                  </div>
                  <div v-else class="d-flex justify-center">
                    <span>{{ $t('purchase_limit_reached') }}</span>
                  </div>
                </div>
                <div v-else class="d-flex justify-center">
                  <span class="mr-1">{{ providerInfo.currencyText }}</span>
                  <span>{{ providerInfo.amount }}</span>
                </div>
              </v-btn>
            </div>
          </div>
        </div>
        <!--附註-->
        <div v-if="providerInfo.hasCopperCoin" class="copper-coin-remark">
          <v-menu open-on-hover offset-x left :nudge-left="10" :nudge-top="4">
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="remark-icon" v-bind="attrs" v-on="on"> ? </v-btn>
            </template>

            <v-card class="copper-coin-remark-text px-2 py-1">{{
              $t('copperCoin_transfer')
            }}</v-card>
          </v-menu>
        </div>
      </v-card>
      <!--導流窗-->
      <yoeConfirmDialog v-model="showRedirectDialog" :action="handleConfirmWithRedirect">
        <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
        <div v-if="checkAndroidBrowser()" class="d-flex flex-wrap default-content--text">
          <span>
            {{ $t('yoeShopRedirectNoty').split('\n')[0] }}
          </span>
          <span>
            {{ $t('yoeShopRedirectNoty').split('\n')[1] }}
          </span>
        </div>
        <div v-else class="d-flex flex-wrap default-content--text">
          <span>
            {{
              $t('yoeShopRedirectNoty').split('\n')[0].slice(0, 6) +
              $t('download') +
              $t('yoeShopRedirectNoty').split('\n')[0].slice(6)
            }}
          </span>
        </div>
      </yoeConfirmDialog>
      <!--確認購買窗-->
      <yoeConfirmDialog v-model="showConfirmDialog" :action="handleConfirmWithExpiredCheck">
        <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
        <div class="d-flex flex-wrap">
          <span class="default-content--text mb-2">{{ $t('yoeGameConfirmNoty_head') }}</span>
          <i18n path="yoeGameConfirmNoty_content" tag="span" class="primary--text">
            <template v-slot:content>{{
              isCashBackReceive
                ? $t('yoeGameConfirmNoty_content_rebate')
                : $t('yoeGameConfirmNoty_content_app')
            }}</template>
          </i18n>
          <span v-if="isAppReceive || isCashBackReceive" class="primary--text">
            {{ $t('yoeGameConfirmNoty_appReceive') }}
          </span>
        </div>
      </yoeConfirmDialog>
    </div>
  </div>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import debounce from 'lodash/debounce'

  export default {
    name: 'yoeProviderCard',
    mixins: [analytics],
    components: {
      yoeConfirmDialog: () => import('~/components/payment/yoeConfirmDialog')
    },
    props: {
      providerInfo: {
        type: Object,
        default: () => ({
          itemContent: []
        })
      }
    },
    data() {
      return {
        showConfirmDialog: false,
        imageLoadingError: false,
        showRedirectDialog: false,
        timer: null,
        duration: Math.floor(
          this.$moment.duration(this.$moment(this.providerInfo.date).diff(this.$moment()))
        ),
        cardAngle: 0, // 動態計算背景角度
        debounceCalculateCardAngle: null //防抖機制
      }
    },
    computed: {
      canBuyItem() {
        const isbuyMax =
          this.providerInfo.maxBuyLimit !== 0 &&
          this.providerInfo.userBuyCount >= this.providerInfo.maxBuyLimit
        return isbuyMax
      },
      vipIcon() {
        return this.getImage(this.providerInfo.vipIcon)
      },
      remainingBalance({ $store }) {
        return $store.getters['yoeShop/remainingBalance']
      },
      imageUrlPath({ $store }) {
        return $store.getters['image/shopImageUrlPath']
      },
      buyLimitText() {
        const isStringEmpty = (str) => {
          return str === '' || str === undefined
        }
        if (isStringEmpty(this.providerInfo.buyLimitText)) return ''
        const matches = this.providerInfo.buyLimitText.match(/\{(\d+)\}/g)
        if (matches.length === 1)
          return this.providerInfo.buyLimitText.format(this.providerInfo.maxBuyLimit)
        return this.providerInfo.buyLimitText.format(
          this.providerInfo.userBuyCount,
          this.providerInfo.maxBuyLimit
        )
      },
      shopImage() {
        return (url) => {
          const imageName = this.imageLoadingError ? url.slice(0, -5) + '.png' : url
          const imagePath = this.imageUrlPath + '/' + imageName
          return imagePath
        }
      },
      maxBuyCountdown() {
        if (this.duration <= 0) {
          return this.providerInfo.timeCountdownText.format(this.$t('expired'))
        }

        // 計算小時和分鐘
        const hours = Math.floor(this.duration / 3600)
        const minutes = Math.floor((this.duration % 3600) / 60)
        const seconds = this.duration % 60
        // 最後59秒，顯示秒數
        if (hours === 0 && minutes === 0 && seconds > 0) {
          return this.providerInfo.timeCountdownText.format(`${seconds}s`)
        }
        // 其他情況顯示小時:分鐘
        const countDownText = `${hours.toString().padStart(2, '0')}:${minutes
          .toString()
          .padStart(2, '0')}`

        return this.providerInfo.timeCountdownText.format(countDownText)
      },
      outerBackgroundStyle() {
        return {
          background: this.providerInfo.outsideBGColorBase
        }
      },
      cardBackgroundStyle() {
        // 因IOS裝置無法正常顯示整個漸層background，故需拆分為BGC和BGI
        const cardBGBase = this.providerInfo.backgroundColorBase
        // background-color部分
        const baseColor = cardBGBase.split(',').pop().trim()
        // background-image部分
        const gradients = cardBGBase
          .substring(0, cardBGBase.lastIndexOf(',')) // 獲取除了bgc值外的所有漸層
          .format(this.cardAngle) // 計算角度參數

        return {
          backgroundColor: baseColor,
          backgroundImage: gradients
        }
      },
      rebateCardStyle() {
        return {
          background: this.providerInfo.rebateCardBg
        }
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      yoeShopArray() {
        return this.$store.getters['yoeShop/yoeShopArray']
      },
      countDownDisplay() {
        const hasCountdown = this.providerInfo.timeCountdownText
        // 如果沒有倒數文字，就顯示
        if (!hasCountdown) {
          return true
        }

        // 如果有倒數且時間大於 0，就顯示
        if (this.duration > 0) {
          return true
        }

        // 如果有倒數但時間已到，則不顯示
        if (hasCountdown && this.duration <= 0) {
          return false
        }
      },
      showRedirectStyle() {
        return (
          this.providerInfo.isBigCard &&
          this.providerInfo.redirect &&
          !this.$device.isIos &&
          !this.$device.isMacOS
        )
      },
      // 是長包且已購買次數已達上限或已逾時
      isBigCardDisabled() {
        return (
          (this.providerInfo.isBigCard &&
            this.providerInfo.userBuyCount === this.providerInfo.maxBuyLimit) ||
          (this.providerInfo.isBigCard &&
            this.providerInfo.userBuyCount !== this.providerInfo.maxBuyLimit &&
            this.duration <= 0)
        )
      },
      // 是否尚能購買
      purchasable() {
        return this.providerInfo.userBuyCount !== this.providerInfo.maxBuyLimit && this.duration > 0
      },
      // 內含僅可APP使用物品確認窗
      isAppUsage() {
        return this.providerInfo.showConfirm === 'appUsage'
      },
      // 內含僅可APP領取物品確認窗
      isCashBackReceive() {
        return this.providerInfo.showConfirm === 'cashBackReceive'
      },
      isAppReceive() {
        return this.providerInfo.showConfirm === 'appReceive'
      },
      isLevelPack() {
        return this.providerInfo.id.includes('gpvip')
      },
      isLevelPackXs() {
        return this.$vuetify.breakpoint.width < 551
      }
    },
    watch: {
      yoeShopArray: {
        deep: true,
        handler(newVal) {
          if (newVal) {
            // store 資料更新後，強制元件重新渲染
            this.$forceUpdate()
          }
        }
      },
      // 監聽傳入的資料變化，重新計算角度
      providerInfo: {
        async handler() {
          await this.$nextTick()
          this.debounceCalculateCardAngle()
        },
        deep: true
      }
    },
    mounted() {
      this.debounceCalculateCardAngle = debounce(this.calculateCardAngle, 100)
      this.startCountdown()
      this.debounceCalculateCardAngle()
      // 添加 resize 監聽器
      window.addEventListener('resize', this.debounceCalculateCardAngle)
    },
    beforeDestroy() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      // 清理 resize 監聽器
      window.removeEventListener('resize', this.debounceCalculateCardAngle)
    },
    methods: {
      errorStickerHandler() {
        this.imageLoadingError = true
      },
      async showPaymentMethod(providerInfo) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) {
          return
        }
        if (providerInfo.maintained) return

        // 開啟導流提示且不是IOS裝置也不是MAC裝置
        if (providerInfo.redirect && !this.$device.isIos && !this.$device.isMacOS) {
          this.showRedirectDialog = true
        } else {
          const validConfirmTypes = ['appUsage', 'cashBackReceive', 'appReceive']
          if (validConfirmTypes.includes(this.providerInfo.showConfirm)) {
            this.showConfirmDialog = true
          } else {
            this.handleConfirmWithExpiredCheck()
          }
        }
      },
      checkAndroidBrowser() {
        // 因三星平版使用三星瀏覽器不被檢測為Android瀏覽器，故需另外判斷
        const userAgent = navigator.userAgent.toLowerCase()
        const isAndroidBrowser =
          userAgent.includes('android') || userAgent.includes('samsungbrowser')

        return this.$device.isAndroid || isAndroidBrowser
      },
      handleConfirmWithRedirect() {
        const shouldUseAndroidLink = this.checkAndroidBrowser()

        let redirectUrl = shouldUseAndroidLink
          ? 'https://www.xin-stars.com/QPPandroid?action=apk'
          : 'https://www.xin-stars.com/Downloads'

        this.$lineOpenWindow.open(redirectUrl, '_blank')
        this.showRedirectDialog = false
      },
      handleConfirmWithExpiredCheck() {
        if (this.providerInfo.timeCountdownText && this.duration <= 0) {
          this.showConfirmDialog = false
          this.$notify.error(this.$t('salesTimeEnded'))
          return
        }
        this.handleConfirm()
      },
      calculateCardAngle() {
        this.$nextTick(() => {
          setTimeout(() => {
            const card = this.$refs.giftPackCard
            if (card) {
              const width = card.offsetWidth
              const height = card.offsetHeight
              // 計算對角線角度（負值是因為我們要從右下角開始）
              this.cardAngle = -90 + Math.round(Math.atan2(height, width) * (180 / Math.PI))
            }
          }, 500)
        })
      },
      handleConfirm() {
        this.$store.commit('yoeShop/SET_PRODUCT_INFO', this.providerInfo)
        this.$nuxt.$emit('root:showYoeDialogStatus', {
          show: true,
          shopItem: this.providerInfo,
          cancel: false,
          purChaseCancel: false,
          onClose: async () => {
            // 對話框關閉時重新獲取數據
            await this.$store.dispatch('yoeShop/fetchYoeGame')
          }
        })
      },
      getImage(pictureName) {
        if (pictureName) return this.shopImage(pictureName)
        return ''
      },
      computedCountDown() {
        const endTime = this.$moment(this.providerInfo.date)
        const diffDuration = this.$moment.duration(endTime.diff(this.$moment()))

        this.duration = Math.floor(diffDuration.asSeconds())
        // 當時間到了就清除計時器
        if (this.duration <= 0) {
          if (this.timer) {
            clearInterval(this.timer)
          }
        }
      },
      startCountdown() {
        // 只有當有倒數文字時才啟動計時器
        const isStringEmpty = (str) => {
          return str === '' || str === undefined
        }
        if (isStringEmpty(this.providerInfo.timeCountdownText)) return
        this.computedCountDown()
        this.timer = setInterval(() => {
          this.computedCountDown()
        }, 1000)
      }
    }
  }
</script>
<style lang="scss" scoped>
  //color
  $primary: map-get($colors, 'primary');
  $default-content: map-get($colors, 'default-content');
  $button-content: map-get($colors, 'button-content');

  .gift-pack-card-bg {
    position: relative;
    padding: 2px;
    border-radius: 10px;
    .gift-pack-card {
      border-radius: 10px;
      box-sizing: border-box;
      border: 2px solid #2a0909;
      background-blend-mode: screen, screen, multiply, normal;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0.4px;
      .gift-pack-card-layout {
        display: flex;
        flex-direction: column;
        align-items: center;
        .levelPack-right-layout {
          width: 100%;
        }
      }
      .gift-pack-title {
        height: 28px;
        color: $default-content;
        text-align: center;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 28px;
        letter-spacing: 0.15px;
      }
      .gift-pack-sub-title {
        color: $default-content;
      }
      .gift-pack-vipIcon {
        position: absolute;
        top: -18px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
      }
      .rebate-card {
        width: 70%;
        position: absolute;
        top: 0;
        transform: translateY(-50%);
        z-index: 2;
      }
      .gift-pack-discountIcon {
        width: 30%;
        position: absolute;
        top: 20%;
        left: -10px;
        transform: rotate(-20deg);
      }
      .gift-pack-content {
        color: $default-content;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0.1px;
        .main-content {
          span {
            color: #e9b950;
            font-weight: 700;
          }
        }
        .bonus-title {
          .bonus-line-left {
            width: 30px;
            border: 1px solid;
            border-image-source: linear-gradient(270deg, rgba(247, 182, 117, 0) 0%, #f7b675 100%);
            border-image-slice: 1;
            transform: rotate(180deg);
          }
          .bonus-line-right {
            transform: rotate(0deg);
          }
          .bonus-square {
            width: 4px;
            height: 4px;
            background-color: #facb94;
            transform: rotate(45deg);
          }
        }
        .sub-content {
          height: 40px;
          font-weight: 400;
          font-size: 12px;
        }
      }
      .time-count-down {
        height: 20px;
        background: linear-gradient(
          270deg,
          rgba(80, 51, 37, 0) 0%,
          rgba(80, 51, 37, 0.6) 50%,
          rgba(80, 51, 37, 0) 100%
        );
        background-blend-mode: multiply;
      }
      .price-area {
        width: 100%;
        position: relative;
      }
      .has-redirect {
        height: 48px;
        border-radius: 14px;
        .gift-pack-price-btn {
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
      .gift-pack-price-btn {
        width: 100%;
        height: 28px;
        margin: 0 auto;
        border-radius: 20px;
        background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
        color: $button-content;
        font-style: normal;
        font-weight: 500;
        line-height: 120%;
        letter-spacing: 1.071px;
        padding: 7px 0;
        box-sizing: border-box;
        &:disabled {
          background: #ffffff1f;
          color: #ffffff4d;
        }
      }
      .copper-coin-remark {
        position: absolute;
        top: 8px;
        right: 8px;
        .remark-icon {
          padding: 2px;
          width: 20px;
          height: 20px;
          min-width: unset;
          border-radius: 100px;
          background: linear-gradient(180deg, rgba(153, 144, 144, 0.5) 0%, #211616 100%);
        }
      }
      .main-item-title {
        white-space: nowrap;
      }
    }
  }
  .copper-coin-remark-text {
    font-size: 12px;
    border-radius: 4px;
    background-color: rgba(97, 97, 97, 0.9);
  }
  .gift-pack-card-vip-bg {
    .gift-pack-card {
      .gift-pack-title {
        background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .limit-buy {
    display: block;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.42353);
    position: absolute;
    z-index: 1;
    top: 0;
  }
  @media screen and (max-width: 375px) {
    .gift-pack-card-bg {
      .gift-pack-card {
        .gift-pack-content {
          font-size: 12px;
          .bonus-title {
            .bonus-line-left {
              width: 25px;
            }
          }
        }
      }
    }
  }
</style>
