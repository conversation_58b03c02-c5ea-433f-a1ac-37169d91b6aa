<template>
  <div>
    <v-dialog
      v-model="showLoginDialogStatusTmp.show"
      max-width="600"
      :fullscreen="breakpoint.xsOnly"
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? '' : 'rounded-lg'"
    >
      <v-container class="pa-0 rounded-3">
        <customDialogTitle :title="$t('login').toUpperCase()" @closeDialog="onCancel" />
        <div id="login-card" :class="breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm'">
          <v-card-text class="my-6 pa-0">
            <div class="d-flex justify-center rounded-b-0">
              <div
                v-for="item in loginMethod"
                :key="item.id"
                elevation="0"
                :class="item.id === 1 && breakpoint.xsOnly ? 'pa-0' : 'pa-2 pb-6'"
              >
                <v-hover v-slot="{ hover }">
                  <div class="d-inline">
                    <v-btn
                      v-if="!(item.id === 1 && breakpoint.xsOnly)"
                      :disabled="maintainSystem[0].maintaining || !item.status"
                      :class="[
                        'translate-y-btn',
                        'shadow-none',
                        {
                          maintenance: maintainSystem[0].maintaining,
                          'ripple-animation-btn': item.id === 1,
                          'grey-5': item.id !== 1
                        }
                      ]"
                      fab
                      large
                      @click="firstOpenQPPCheck(item.id)"
                    >
                      <v-icon v-if="hover && maintainSystem[0].maintaining"> mdi-wrench </v-icon>
                      <v-img
                        v-if="
                          (item.iconFile && !hover && maintainSystem[0].maintaining) ||
                          (item.iconFile && !maintainSystem[0].maintaining)
                        "
                        :max-width="item.maxWidth"
                        :src="item.iconFile"
                        class="ma-5"
                      />
                      <span
                        v-if="
                          (item.icon && !hover && maintainSystem[0].maintaining) ||
                          (item.icon && !maintainSystem[0].maintaining)
                        "
                        class="custom-material-symbols-outlined-h5"
                        :class="[loginConfig.phoneIconColor]"
                      >
                        {{ item.icon }}
                      </span>
                    </v-btn>
                  </div>
                </v-hover>
              </div>
            </div>
            <v-row no-gutters class="px-4 px-sm-6 pb-sm-6 pb-0"> <v-divider /></v-row>
            <v-row no-gutters>
              <v-col
                cols="12"
                class="default-content--text custom-text-noto px-4 px-sm-12 pt-0 pb-1 text-left text-body-2"
              >
                {{ $t('registration_terms') }}
              </v-col>
            </v-row>
            <v-row no-gutters class="px-4 px-sm-12 terms-btn">
              <v-col
                v-for="(info, index) in companyInfoList"
                :key="index"
                cols="auto"
                :class="[
                  'custom-text-noto text-left pt-1',
                  breakpoint.xsOnly ? 'text-caption' : 'text-sm-body-2'
                ]"
              >
                <a
                  :href="
                    loginConfig.companyInfoList.useHerf &&
                    info.name !== 'intellectual_property_policy'
                      ? info.url
                      : null
                  "
                  elevation="0"
                  :small="breakpoint.xsOnly ? true : false"
                  class="px-0"
                  color="primary"
                  :style="loginConfig.companyInfoList.style"
                  @click.prevent="handleClick(info)"
                >
                  {{ $t(info.name) }}
                </a>

                <span v-show="index !== companyInfoList.length - 1" class="grey-2--text px-1">
                  |
                </span>
              </v-col>
            </v-row>
            <v-row no-gutters>
              <v-col
                cols="12"
                :class="[
                  'px-4 px-sm-14 pt-2 text-left pt-2 default-content--text',
                  breakpoint.xsOnly ? 'text-caption' : 'text-sm-body-2'
                ]"
              >
                <ul>
                  <li v-for="(noty, index) in loginConfig.loginNotyArray" :key="index">
                    {{ $t(noty) }}
                  </li>
                </ul>
              </v-col>
            </v-row></v-card-text
          >
        </div>
      </v-container>
    </v-dialog>
    <phoneNumber
      v-if="showPhoneNumberDialog"
      :show-phone-number-dialog.sync="showPhoneNumberDialog"
    />
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import analytics from '@/mixins/analytics.js'
  import uploadPhoto from '@/mixins/uploadPhoto.js'
  import facebook from '@/mixins/facebook.js'
  import preLoginAction from '@/mixins/preLoginAction.js'
  const STATION = process.env.STATION

  export default {
    name: 'NewLogin',
    mixins: [hiddenScrollHtml, analytics, uploadPhoto, facebook, preLoginAction],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      phoneNumber: () => import(`~/components_station/${STATION}/login/phoneNumber`)
    },
    props: {
      showLoginDialogStatus: {
        type: Object,
        default: () => ({ show: false, onCancelNotify: () => {} })
      }
    },
    data() {
      return {
        loginMethod: [],
        showLoginDialogStatusTmp: this.showLoginDialogStatus,
        showPhoneNumberDialog: false
      }
    },
    created() {
      this.loginMethod = this.$UIConfig.newLogin.loginMethod
    },
    computed: {
      loginConfig() {
        return this.$UIConfig.newLogin
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      companyInfoList({ $store }) {
        return $store.getters[`${STATION}/companyInfo/list`]
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      facebookId() {
        return this.$store.getters['role/facebookId']
      },
      userName() {
        return this.$store.getters['role/userName']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showLoginDialogStatus: {
        handler(val) {
          this.showLoginDialogStatusTmp = val
        }
      }
    },
    methods: {
      openCompayDialog(info) {
        this.$store.commit(`${STATION}/companyInfo/SET_COMPANY_POLICY_TYPE`, info.id)
        this.$nuxt.$emit('root:showCompanyDialogStatus', true)
      },
      handleClick(info) {
        this.openCompayDialog(info)
      },
      async firstOpenQPPCheck(id) {
        // 否则，执行其他处理
        if (!this.$wsClient.isConnected) {
          await this.wsInit()
        }

        this.selectLoginMethod(id)
      },
      debug(message) {
        console.log(
          `%c XinWeb %c ${message} %c`,
          'background:#181834 ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
          'background:#3affff ; padding: 1px; border-radius: 0 3px 3px 0;  color: #333',
          'background:transparent'
        )
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      async wsInit() {
        this.$nuxt.$loading.start()
        if (!this.$wsClient.isConnected) {
          do {
            let endPoint = this.$wsClient.endPoint.current
            this.debug('連線到:' + endPoint)
            try {
              // 1000 正常關閉
              await this.$wsClient.disconnect(1000)
              await this.$wsClient.connect(endPoint)
            } catch (err) {
              console.log('CATCH ERR', err)
              this.$wsClient.endPoint.next()
            }
            await this.$xinUtility.delay(1000)
          } while (!this.$wsClient.isConnected)

          this.$wsClient.send(this.$wsPacketFactory.connectionPacket())
          await this.$xinUtility
            .waitEvent(
              this.$wsClient.receivedListeners,
              (data) => data.protocolId === this.$xinConfig.PROTOCOL_ID.CONNECT
            )
            .then((data) => {
              this.$store.dispatch('role/saveToken', data)
              // 初始化動作-進入登入服務器
              this.$wsClient.send(
                this.$wsPacketFactory.getServiceJoin(this.$xinConfig.LOGIN_SERVICE.ID)
              )
              this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                serviceId: this.$xinConfig.LOGIN_SERVICE.ID,
                enable: true,
                connected: true
              })
            })
        }
        this.$nuxt.$loading.finish()
      },
      async selectLoginMethod(loginId) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) {
          return
        }
        // 封鎖彈窗提示透過localstorage控制
        let localStorageAcceptPopupWindowData =
          this.$localStorage.get('accept_popup_window').expired
        if (
          !localStorageAcceptPopupWindowData ||
          this.$moment(localStorageAcceptPopupWindowData).isBefore(this.$moment())
        ) {
          //30天後失效
          let maturityDate = this.$moment().add(30, 'days')
          this.$localStorage.set('accept_popup_window', { expired: maturityDate })
          alert(this.$t('popup_windows_noty'))
        }

        const title = this.$t('reminder')
        switch (loginId) {
          case 1:
            this.showPhoneNumberDialog = true
            break
          case 2:
            this.$appleapi.login({
              successful: async (idToken) => {
                this.appleIdLoginHandle(idToken)
              },
              failed: () => {
                this.showNotyDialog(title, this.$t('appleLoginFailNoty1'))
              }
            })
            break
          case 3:
            {
              const onLogin = (loginResponse) => {
                if (loginResponse.status === 'connected') {
                  const authResponse = loginResponse.authResponse
                  this.facebookLoginHandle(authResponse)
                } else {
                  this.showNotyDialog(title, this.$t('fbLoginFailNoty1'))
                }
              }
              this.FBLogin(onLogin)
            }
            break
          case 4:
            this.$gapi.login({
              successful: async (idToken) => {
                this.googleLoginHandle(idToken)
              },
              failed: (message) => {
                if (message === 'opt_out_or_no_session') {
                  this.showNotyDialog(title, this.$t('googleLoginFailNoty1'))
                } else {
                  this.showNotyDialog(title, this.$t('googleLoginFailNoty2'))
                }
              }
            })
            break
        }
      },
      closeDialog() {
        this.$nuxt.$emit('root:showLoginDialogStatus', { show: false, onCancelNotify: () => {} })
      },
      async googleLoginHandle(idToken) {
        const xAgent = this.$cookies.get('xAgent')
        const packet = this.$wsPacketFactory.loginWithGoogle({
          idToken,
          device: xAgent,
          clientVersion: this.$xinServerConfig.clientVersion,
          serverVersion: this.$xinServerConfig.serverVersion,
          promote: this.$store.getters['social/promote']
        })
        this.$wsClient.send(packet)
        this.loginHandler('Google')
      },
      async loginHandler(type) {
        try {
          const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
            return data.isFeature(this.$xinConfig.FEATURE.LOGIN.ID)
          })
          const title = this.$t('reminder')
          let message = ''
          let label
          switch (type) {
            case 'Apple':
              label = 'Webapple'
              break
            case 'Facebook':
              label = 'Webfacebook'
              break
            case 'Google':
              label = 'Webgoogle'
              break
            default:
              break
          }
          this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
            serviceId: this.$xinConfig.LOGIN_SERVICE.ID,
            enable: false,
            connected: false
          })

          // 避免剛登入時連續跳出 noty info 的設計，先透過該值關閉通知
          this.$store.commit('mail/SET_FIRST_RECIVE', true)

          if (
            res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.NEW_LOGIN.COMMAND.SHOW_CREATE_CHAR
          ) {
            const onLogin = (loginResponse) => {
              if (loginResponse.status === 'connected') {
                window.FB.api('/me', async (response) => {
                  this.$store.commit('role/SET_FBNAME', response.name)
                  this.$nuxt.$emit('root:showCreateRoleDialogStatus', true)
                })
              }
            }
            if (type === 'Facebook') this.FBLogin(onLogin)
            else this.$nuxt.$emit('root:showCreateRoleDialogStatus', true)
          }
          //因流程可能會未登入成功(角色在線、裝置驗證、裝置驗證失敗)
          else if (
            res.type === 0 &&
            res.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.PLAYER_INFO.ID
          ) {
            // 先發送前三個服務的加入請求
            const initialServices = [
              this.$xinConfig.LOTTERY_SERVICE.ID, // 進入摸彩服務
              this.$xinConfig.GAME_SERVICE.ID, // 進入遊戲服務
              this.$xinConfig.SOCIAL_SERVICE.ID // 進入社群服務
            ]

            initialServices.forEach((serviceId) => {
              this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId))
              this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
                serviceId,
                enable: true,
                connected: true
              })
            })

            // 等待 SOCIAL_SERVICE 確認
            await this.$xinUtility
              .waitEvent(
                this.$wsClient.receivedListeners,
                (data) =>
                  data.protocolId === this.$xinConfig.PROTOCOL_ID.SERVICE &&
                  data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
              )
              .then(() => {
                this.$wsClient.send(this.$wsPacketFactory.initMail())
                // 延遲五秒後開啟新信件通知
                setTimeout(() => {
                  this.$store.commit('mail/SET_FIRST_RECIVE', false)
                }, 5000)
              })
              .catch((err) => {
                console.log('SOCIAL_SERVICE ERROR:', err)
              })
            // 初始化角色資料
            await this.$store.dispatch('role/profileInit', res)
            // 再加入公會服務
            this.$wsClient.send(
              this.$wsPacketFactory.getServiceJoin(this.$xinConfig.GUILD_SERVICE.ID)
            )
            this.$store.commit('xinProtocol/SET_SERVICE_IDS', {
              serviceId: this.$xinConfig.GUILD_SERVICE.ID,
              enable: true,
              connected: true
            })

            //取得與伺服器時間差
            await this.$store.dispatch('xinProtocol/getServerLocalTimeDiff')
            //每次臉書登入都上傳照片
            if (type === 'Facebook') {
              //上傳fb照片
              const avatar = await this.getUserAvatar()
              this.uploadImgRes(this.userName, avatar)
            }

            setTimeout(() => {
              this.closeDialog()
              // 成功登入，執行使用者登入前動作
              const hasAction = this.callPreLoginAction()
              if (!hasAction && this.$vuetify.breakpoint.xsOnly) {
                this.$nuxt.$emit('root:showChatRoomDialogStatus', true)
              }
            }, 500)

            this.$nuxt.$emit('root:showWelcomeStatus', true)

            //第三方登入 登入成功後 送出登入事件至google analytics 與 facebook analytics
            this.loginAnalytics(label)
            if (
              Object.prototype.hasOwnProperty.call(res, 'lastLogoutTime') &&
              res.lastLogoutTime === '1900/01/01 00:00:00'
            ) {
              this.createRoleAnalytics()
            }
          } else if (res.commandId === 136) {
            this.$notify.info(res.message)
            this.$nuxt.$emit('root:showDeviceWhiteDialogStatus', true)
          } else if (res.commandId === 135) {
            message = res.message
            this.showNotyDialog(title, message)
          }
        } catch (error) {
          console.log('google login', error)
        }
      },
      onCancel() {
        this.showLoginDialogStatusTmp.onCancelNotify?.()
        this.closeDialog()
      }
    }
  }
</script>

<style lang="scss" scoped>
  #login-card {
    &.scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    &.scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      &.scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
  .v-btn {
    &.shadow-none {
      box-shadow: none !important;
    }
  }
</style>
