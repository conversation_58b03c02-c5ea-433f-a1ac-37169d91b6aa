<template>
  <v-container class="pa-4 pa-sm-6" id="guildInfo">
    <div>
      <div class="d-flex justify start flex-nowrap mb-4 mb-sm-6">
        <div>
          <avatar frame :guild-id="guildId" />
        </div>
        <div class="ml-4 guild-flex-grow">
          <v-row no-gutters>
            <v-col>
              <span class="text-start custom-text-noto text-h6 primary--text">
                {{ guildName }}
              </span>
            </v-col>
          </v-row>

          <v-row class="mt-4" no-gutters>
            <v-col class="my-1" cols="12" sm="5" md="5" lg="5" xl="5">
              <div class="d-flex justify-start align-center">
                <span class="text-start custom-text-noto text-caption text-no-wrap grey-2--text">
                  {{ $t('online_member_count') + '/' + $t('guild_member_count') }}
                </span>
                <v-spacer v-if="breakpoint.xsOnly" />
                <span
                  class="text-start custom-text-noto text-subtitle-1 text-no-wrap default-content--text ml-2"
                >
                  {{ guildOnlineMembers + '/' + guildMembers }}
                </span>
              </div>
            </v-col>
          </v-row>
          <v-row class="d-flex" v-if="!breakpoint.xsOnly" no-gutters>
            <v-col v-if="showOperation(guildRank)" cols="12" sm="4" md="4" lg="4" xl="4">
              <div class="py-2 pr-2">
                <v-btn
                  v-if="guildAcceptList.length === 0"
                  block
                  class="button-content--text"
                  color="primary"
                  depressed
                  @click="onAcceptClick()"
                >
                  {{ $t('accept_list') }}
                </v-btn>
                <v-btn
                  v-else
                  block
                  class="button-content--text"
                  color="primary"
                  depressed
                  @click="onAcceptClick()"
                  data-after-text="!"
                  data-after-type="badge top right red"
                >
                  {{ $t('accept_list') }}
                </v-btn>
              </div>
            </v-col>

            <v-col cols="6" :sm="showOperation(guildRank) ? 4 : 6">
              <div
                :class="{
                  'pa-2': showOperation(guildRank),
                  'py-2 pr-2': !showOperation(guildRank)
                }"
                width="100%"
              >
                <v-btn
                  v-if="guildChat === 0"
                  block
                  :class="{ 'button-content--text': !showOperation(guildRank) }"
                  :outlined="showOperation(guildRank)"
                  color="primary"
                  @click="onChatroomClick()"
                >
                  {{ $t('guild_chat_button') }}
                </v-btn>
                <v-btn
                  v-else
                  block
                  :class="{ 'button-content--text': !showOperation(guildRank) }"
                  :outlined="showOperation(guildRank)"
                  color="primary"
                  @click="onChatroomClick()"
                  :data-after-text="guildChat > 99 ? '99+' : guildChat"
                  data-after-type="badge top right red"
                >
                  {{ $t('guild_chat_button') }}
                </v-btn>
              </div>
            </v-col>

            <v-col cols="6" :sm="showOperation(guildRank) ? 4 : 6">
              <div class="py-2 pl-2">
                <v-btn
                  block
                  outlined
                  color="error"
                  :disabled="isGuildPresident()"
                  @click="onLeaveGuildClick()"
                >
                  {{ $t('exit_guild') }}
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </div>
      </div>
      <v-row v-if="breakpoint.xsOnly" no-gutters>
        <v-col v-if="showOperation(guildRank)" cols="12" sm="4" md="4" lg="4" xl="4">
          <div class="py-2">
            <v-btn
              v-if="guildAcceptList.length === 0"
              block
              class="button-content--text"
              color="primary"
              depressed
              @click="onAcceptClick()"
            >
              {{ $t('accept_list') }}
            </v-btn>
            <v-btn
              v-else
              block
              class="button-content--text"
              color="primary"
              depressed
              @click="onAcceptClick()"
              data-after-text="!"
              data-after-type="badge top right red"
            >
              {{ $t('accept_list') }}
            </v-btn>
          </div>
        </v-col>
        <v-col cols="6" sm="4" md="4" lg="4" xl="4">
          <div class="pr-2 py-2">
            <v-btn
              block
              outlined
              color="error"
              :disabled="isGuildPresident()"
              @click="onLeaveGuildClick()"
            >
              {{ $t('exit_guild') }}
            </v-btn>
          </div>
        </v-col>
        <v-col cols="6" sm="4" md="4" lg="4" xl="4">
          <div class="pl-2 py-2">
            <v-btn
              v-if="guildChat === 0"
              block
              :class="{ 'button-content--text': !showOperation(guildRank) }"
              :outlined="showOperation(guildRank)"
              color="primary"
              @click="onChatroomClick()"
            >
              {{ $t('guild_chat_button') }}
            </v-btn>
            <v-btn
              v-else
              block
              :class="{ 'button-content--text': !showOperation(guildRank) }"
              :outlined="showOperation(guildRank)"
              color="primary"
              @click="onChatroomClick()"
              :data-after-text="guildChat > 99 ? '99+' : guildChat"
              data-after-type="badge top right red"
            >
              {{ $t('guild_chat_button') }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
      <acceptGuildMemberDialog
        v-if="showGuildAcceptDialogStatus"
        :show-guild-accept-dialog-status.sync="showGuildAcceptDialogStatus"
      />
      <v-card class="guild-scrollable-content mt-2" width="100%" flat :height="guildCaptionHeight">
        <v-card-text class="py-2 px-3">
          <span
            class="custom-text-noto text-body-2 guild-content-text"
            :class="{
              'grey-3--text': guildCaption === '',
              'default-content--text': !(guildCaption === '')
            }"
            >{{ guildCaption === '' ? $t('not_have_caption') : guildCaption }}</span
          >
        </v-card-text>
        <guildConfirmDialog
          v-if="guildConfirmStatus"
          :confirm-obj="confirmObj"
          :show-guild-confirm-dialog-status.sync="guildConfirmStatus"
        />
      </v-card>
    </div>
  </v-container>
</template>

<script>
  import chat from '~/mixins/chatroom/chat.js'
  import images from '~/mixins/images'
  import guildMgr from '~/mixins/guildMgr'
  import scssLoader from '~/mixins/scssLoader.js'
  export default {
    name: 'guildLobbyInfo',
    mixins: [images, scssLoader, chat, guildMgr],
    components: {
      avatar: () => import('~/components/guild/guildAvatar'),
      acceptGuildMemberDialog: () => import('~/components/guild/acceptGuildMemberDialog'),
      guildConfirmDialog: () => import('~/components/guild/guildConfirmDialog')
    },
    props: {
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showGuildAcceptDialogStatus: false,
        showGuilSettingDialogStatus: false,
        guildConfirmStatus: false,
        confirmObj: {}
      }
    },

    computed: {
      orientation({ $store }) {
        return $store.getters['deviceManagement/getOrientation']
      },
      facebookId() {
        return this.$store.getters['role/facebookId']
      },
      //是否橫屏
      isStanding() {
        return this.orientation === 0
      },
      //是否手機
      isMobile() {
        return this.$device.isMobile
      },
      selfGuildName({ $store }) {
        return $store.getters['guild/guildName']
      },
      guildAcceptList({ $store }) {
        return $store.getters['guild/guildAcceptList']
      },
      guildChat({ $store }) {
        const chatList = $store.getters['chat/chats']
        const guildChat = chatList.find((x) => x.title === 'guild_chat')
        return guildChat === undefined ? 0 : guildChat.noty
      },
      guildCaptionHeight() {
        if (this.$vuetify.breakpoint.xsOnly) return 104
        const maxCount = 10
        const guildCaptionList = this.guildCaption !== '' ? this.guildCaption.split('\n') : []
        const ShowCount = guildCaptionList.length > maxCount ? maxCount : guildCaptionList.length
        const captionHeight = ShowCount * 22 + 16
        return captionHeight > 95 ? captionHeight : 95
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      guildRank: {
        handler(val) {
          if (val === 1) this.showGuildAcceptDialogStatus = false
        }
      }
    },
    async mounted() {
      this.setOrientation() //偵測手機轉向是否改變
      window.addEventListener('orientationchange', this.setOrientation)
    },
    beforeDestroy() {
      window.removeEventListener('orientationchange', this.setOrientation)
    },

    methods: {
      setOrientation() {
        this.$store.dispatch('deviceManagement/setOrientation')
      },
      openUploadMethodDialog() {
        if (!this.facebookId && this.isInfoPage) {
          this.$nuxt.$emit('root:showUploadPhotoMethodDialogStatus', true)
        }
      },
      async onLeaveGuildClick() {
        const leave = () => {
          this.$store.commit('guild/SET_LEAVE_GUILD', true)
          setTimeout(() => {
            this.$wsClient.send(this.$wsPacketFactory.leaveGuild())
          }, 800)
        }
        this.confirmObj = {
          confirm: 'sure',
          confirmInfo: this.$t('guild_leave_noty'),
          cancel: 'guild_think',
          onConfirmNotify: leave
        }
        this.guildConfirmStatus = true
      },
      async sendAddGuildMember(guildId) {
        this.$wsClient.send(this.$wsPacketFactory.addGuild(guildId))
      },
      onAcceptClick() {
        this.showGuildAcceptDialogStatus = true
      },
      onChatroomClick() {
        this.openGuildChat()
      },
      isGuildPresident() {
        return this.guildRank === 3
      },
      showOperation(rank) {
        return rank > 1
      }
    }
  }
</script>
<!-- 此處加scoped會使部分Class無作用，故在最外層包覆一層class以及將class命名複雜化避免汙染 -->
<style lang="scss">
  $dialog-fill: map-get($colors, 'dialog-fill');
  $black-with-opacity-20-color: map-get($colors, black-with-opacity-20);
  #guildInfo {
    .guild-scrollable-content {
      overflow-y: auto; /* 自动显示滚动条 */
      background-color: $black-with-opacity-20-color !important;
    }
    .guild-content-text {
      word-break: break-all;
      white-space: pre-wrap;
    }
    .guild-flex-grow {
      flex-grow: 1;
    }
    .fill-guild-width {
      width: 100%;
    }
    [data-after-text],
    [data-before-text] {
      --badge-offset-x: calc(0px - var(--badge-size) / 3);
      --badge-offset-y: calc(0px - var(--badge-size) / 3);
      --badge-size: 1.3rem;

      --b: initial;
      --bgc: hsl(195, 100%, 30%);
      --bdrs: 0;
      --c: hsl(195, 100%, 99%);
      --d: inline-flex;
      --fz: 0.625rem;
      --fw: 700;
      --h: auto;
      --l: initial;
      --m: 0.4rem;
      --p: 0;
      --pos: static;
      --r: initial;
      --t: initial;
      --tt: uppercase;
      --w: initial;

      position: relative;
    }

    [data-after-text]:not([data-after-text=''])::after {
      content: attr(data-after-text);
    }
    [data-before-text]:not([data-before-text=''])::before {
      content: attr(data-before-text);
    }

    [data-after-text]:not([data-after-text=''])::after,
    [data-before-text]:not([data-before-text=''])::before {
      align-items: center;
      background: var(--bgc);
      border-radius: var(--bdrs);
      bottom: var(--b);
      box-shadow: var(--bxsh);
      box-sizing: border-box;
      color: var(--c);
      display: var(--d);
      font-size: var(--fz);
      font-weight: var(--fw);
      height: var(--h);
      justify-content: center;
      left: var(--l);
      padding: var(--p);
      position: var(--pos);
      right: var(--r);
      text-decoration: none;
      text-transform: var(--tt);
      top: var(--t);
      width: var(--w);
    }

    /* MODIFIERS */
    [data-after-type*='badge']::after,
    [data-before-type*='badge']::before {
      --bdrs: var(--badge-size);
      // --bxsh: 0 0 0 2px rgba(255, 255, 255, 0.5);
      --h: var(--badge-size);
      --p: 0;
      --pos: absolute;
      --w: var(--badge-size);
    }

    /* COLORS */

    [data-after-type*='red']::after,
    [data-before-type*='red']::before {
      --bgc: #f62c2c;
    }

    /* POSITION */
    [data-after-type*='badge'][data-after-type*='top']::after,
    [data-before-type*='badge'][data-before-type*='top']::before {
      --m: 0;
      --t: var(--badge-offset-y);
    }
    [data-after-type*='badge'][data-after-type*='right']::after,
    [data-before-type*='badge'][data-before-type*='right']::before {
      --m: 0;
      --r: var(--badge-offset-x);
    }
    [data-after-type*='badge'][data-after-type*='bottom']::after,
    [data-before-type*='badge'][data-before-type*='bottom']::before {
      --b: var(--badge-offset-y);
      --m: 0;
    }
    [data-after-type*='badge'][data-after-type*='left']::after,
    [data-before-type*='badge'][data-before-type*='left']::before {
      --l: var(--badge-offset-x);
      --m: 0;
    }
  }
</style>
