<!-- eslint-disable vue/no-v-html -->
<template>
  <v-row no-gutters justify="center" ref="companyDialog" class="company-dialog">
    <v-dialog
      v-model="showCompanyDialogStatus"
      :fullscreen="$vuetify.breakpoint.smAndDown"
      class="dialog-fill"
      scrollable
      max-width="800"
      :attach="$refs.companyDialog"
    >
      <v-card color="transparent">
        <customDialogTitle
          :title="$t(nowArticleObj.name)"
          @closeDialog="closeDialog"
          :class="['company-dialog-title', { 'notch-left': hasLeftNotch }]"
        />
        <v-card-text
          style="height: 800px"
          :class="[
            'px-10 pb-6 pt-4 company-dialog-card-text',
            { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
          ]"
        >
          <v-row no-gutters>
            <v-col>
              <nuxt-content :document="article" />
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-row>
</template>
<script>
  import orientation from '@/mixins/orientation.js'
  const STATION = process.env.STATION
  export default {
    name: 'companyDialog',
    mixins: [orientation],
    props: {
      showCompanyDialogStatus: { type: Boolean, default: false }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    data() {
      return {
        article: {},
        nowArticleObj: {}
      }
    },
    watch: {
      showCompanyDialogStatus(val) {
        if (val === false) {
          this.closeDialog()
        }
      }
    },
    computed: {
      companyPolicyType({ $store }) {
        return $store.getters[`${STATION}/companyInfo/companyPolicyType`]
      }
    },

    async mounted() {
      try {
        this.nowArticleObj = this.switchArticle(this.companyPolicyType)
        this.article = await this.$content(
          `articles/${this.$i18n.locale}/${this.nowArticleObj.pathName}`
        ).fetch()
      } catch (loadErr) {
        console.log(loadErr)
      }
    },
    beforeDestroy() {},
    methods: {
      switchArticle(articleType) {
        let article = {}
        switch (articleType) {
          case 1:
            article.name = 'services_policy'
            article.pathName = 'malaysia_terms_of_service'
            break
          case 2:
            article.name = 'privacy_policy'
            article.pathName = 'malaysia_privacy_policy'
            break
          default:
            break
        }

        return article
      },
      closeDialog() {
        this.$nuxt.$emit('root:showCompanyDialogStatus', false)
      }
    }
  }
</script>
<style>
  .opacity07 {
    opacity: 0.7;
  }
  .custom-default-content-1 {
    color: rgba(255, 255, 255, 0.5);
  }
  .default-content-line {
    text-decoration: none;
    opacity: 1 !important;
  }
  .company-dialog {
    position: relative;
    z-index: 1001;
  }
</style>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .company-dialog-title {
      &.notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      &.notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
    .company-dialog-card-text {
      &.notch-left {
        padding-left: calc(40px + env(safe-area-inset-left)) !important;
      }
      &.notch-right {
        padding-right: calc(40px + env(safe-area-inset-right)) !important;
      }
    }
  }
</style>
