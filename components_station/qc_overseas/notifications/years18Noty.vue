<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showYears18NotyStatusTmp.show"
    persistent
    max-width="400"
    content-class="rounded-lg"
  >
    <v-card class="transparent">
      <v-card-title
        class="custom-text-noto text-subtitle-1 button-content--text gradient-primary-left justify-center"
      >
        {{ $t('reminder').toUpperCase() }}
      </v-card-title>
      <v-card-text class="custom-text-noto default-content--text px-4 pt-6 pb-2 px-sm-6">
        <span class="text-md-body-1 custom-text-noto">{{ $t('year18_noty1') }}</span>
        <v-checkbox v-model="checkbox" :label="`${$t('today_dont_display')}`"></v-checkbox>
      </v-card-text>
      <v-card-actions class="pt-0 px-4 pb-4 px-sm-6 pb-sm-6">
        <v-btn
          depressed
          block
          color="primary"
          :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
          @click="closeDialog"
        >
          {{ $t('sure').toUpperCase() }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  export default {
    name: 'Years18Noty',
    mixins: [hiddenScrollHtml],
    props: {
      showYears18NotyStatus: {
        type: Object,
        default: () => ({ show: false, onConfirmNotify: () => {} })
      }
    },
    data() {
      return {
        showYears18NotyStatusTmp: this.showYears18NotyStatus,
        checkbox: false
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showYears18NotyStatus: {
        handler(status) {
          this.showYears18NotyStatusTmp = status
        }
      },
      checkbox: {
        handler(status) {
          this.$localStorage.set('years18Noty', { enable: Number(status) })
        }
      }
    },
    mounted() {},
    methods: {
      closeDialog() {
        this.showYears18NotyStatusTmp.onConfirmNotify?.()
        this.$emit('update:showYears18NotyStatus', { show: false, onConfirmNotify: () => {} })
      }
    }
  }
</script>
