<template>
  <div>
    <v-dialog
      v-model="mailDialogStatusTmp.show"
      :fullscreen="breakpoint.xsOnly"
      width="800px"
      scrollable
      persistent
      transition="dialog-transition"
      :content-class="breakpoint.xsOnly ? 'mail-dialog' : 'mail-dialog rounded-lg'"
    >
      <v-card class="dialog-fill" height="560px" max-height="90vh">
        <customDialogTitle :title="$t('mail').toUpperCase()" @closeDialog="closeDialog" />

        <!-- action bar for not mobile -->
        <v-card
          v-if="!breakpoint.xsOnly"
          class="d-flex rounded-b-0 dialog-fill-2"
          flat
          tile
          elevation="0"
          color="grey-6"
        >
          <v-card class="pa-4 rounded-0" color="transparent" outlined tile>
            <v-btn class="mx-2" outlined color="primary" @click="sendNewMail">
              <span>
                {{ $t('send_mail').toUpperCase() }}
              </span>
            </v-btn>
            <v-btn
              :disabled="delReadBtnStatus === false"
              class="mx-2"
              outlined
              color="primary"
              @click="confirmDeletionAllStatus = true"
            >
              <span>
                {{ $t('delete_read').toUpperCase() }}
              </span>
            </v-btn>
            <v-btn
              :disabled="allReceiveBtnStatus === false"
              class="mx-2"
              outlined
              color="primary"
              @click="confirmReadAllStatus = true"
            >
              <span>
                {{ $t('receive_all').toUpperCase() }}
              </span>
            </v-btn>
          </v-card>
        </v-card>
        <!-- action bar for mobile -->
        <v-card
          v-else-if="!mobile.showMailContentstatus"
          class="d-flex rounded-b-0 dialog-fill-2"
          flat
          tile
          elevation="0"
        >
          <v-card class="pa-4 rounded-0" color="transparent" outlined tile>
            <v-btn
              :disabled="delReadBtnStatus === false"
              class="mx-2"
              outlined
              color="primary"
              @click="confirmDeletionAllStatus = true"
            >
              <span>
                {{ $t('delete_read').toUpperCase() }}
              </span>
            </v-btn>
            <v-btn
              :disabled="allReceiveBtnStatus === false"
              class="mx-2"
              outlined
              color="primary"
              @click="confirmReadAllStatus = true"
            >
              <span>
                {{ $t('receive_all').toUpperCase() }}
              </span>
            </v-btn>
          </v-card>
        </v-card>
        <!-- list & content -->
        <v-container
          fill-height
          class="pa-0"
          :style="{
            overflow: 'hidden',
            height: breakpoint.xsOnly ? '100%' : ''
          }"
        >
          <v-row
            id="mail-list-mobile"
            no-gutters
            class="fill-height w-100"
            :style="{
              'flex-wrap': breakpoint.xsOnly ? 'wrap' : 'nowrap',
              overflow: breakpoint.xsOnly ? 'auto' : ''
            }"
          >
            <!-- mail list -->
            <!-- v-if 改 v-show 是為了在任何狀態下 -->
            <!-- showMailDataArrTmp 持續更新，進而讓 getSingleMail 維持最新資料 -->
            <v-col
              v-show="!mobile.showMailContentstatus"
              sm="5"
              md="5"
              lg="5"
              id="mail-list-container"
              class="dialog-fill-2"
              :style="{
                'min-width': breakpoint.xsOnly ? ' 100px' : 'unset',
                'max-height': breakpoint.xsOnly ? '100%' : 'calc(min(560px, 90vh) - 178px)'
              }"
            >
              <v-select
                v-model="mailCategoryType"
                class="px-6 py-2"
                :label="$t('mail_category')"
                rounded
                outlined
                dense
                height="40px"
                :items="mailCategoryList"
                item-value="categoryType"
                item-text="name"
                hide-details
                @change="selectedMailCategoryEvent"
              >
                <template #item="{ item }">
                  <div class="d-flex justify-space-between w-100">
                    <span>{{ item.name }}</span>
                    <span v-show="item.count !== 0">{{ item.count }}</span>
                  </div>
                </template>
                <template #selection="{ item }">
                  <div class="d-flex justify-space-between w-100">
                    <span>{{ item.name }}</span>
                  </div>
                </template>
              </v-select>
              <v-list
                v-if="(showMailDataArrTmp && showMailDataArrTmp.length != 0) || !breakpoint.xsOnly"
                id="mail-list"
                ref="mailList"
                two-line
                shaped
                color="dialog-fill-2"
                :class="['pa-0', breakpoint.smAndUp ? 'scrollable-sm' : 'scrollable-xs']"
                :style="{
                  height: '100%',
                  'overflow-y': 'auto'
                }"
              >
                <!-- 避免在mandatory的屬性導致在未選擇信件的情況下硬是設定一筆，
                  mandatory屬性是為了不讓使用者點選同一筆第二次會取消active的狀態 -->
                <v-list-item-group
                  v-model="selection"
                  :mandatory="getSingleMail.content !== $t('please_select_letter')"
                  active-class="primary--text"
                >
                  <template v-for="item in showMailDataArrTmp">
                    <v-list-item
                      class="pl-2 pr-4 mr-3"
                      height="82px"
                      :ref="(el) => (mailItemRefs[item.mailId] = el)"
                      :key="item.mailId"
                      :value="item.mailId"
                      @click="doShowMailContent(item.mailId)"
                    >
                      <v-list-item-content>
                        <v-list-item-title class="pl-2">
                          <div class="custom-between">
                            <div class="mr-2">
                              <!-- 左側內容 -->
                              <v-badge
                                v-if="!item.isRead"
                                class="pt-1 pr-1"
                                :color="!item.isRead ? 'error' : 'transparent'"
                                dot
                                left
                              />
                            </div>
                            <div class="right text-left mail-list-icon-title">
                              <!-- 右側內容 -->
                              <span
                                class="custom-text-noto text-caption text-overflow primary-variant-1--text"
                              >
                                {{ checkMailCanOpen(item) ? '' : $t('mail_cant_get_noty1') }}
                              </span>
                              <!--使用clac避免單純使用%數 會導致字被切到 text-overflow出不來-->
                              <span
                                class="text-overflow custom-text-body-1 mail-list-icon-title-text"
                              >
                                {{ convertMessage(item.from) }}
                              </span>
                            </div>
                          </div>
                        </v-list-item-title>
                        <v-list-item-subtitle
                          class="pl-4 custom-text-noto text-caption white--text mail-list-icon-sub-title"
                        >
                          {{ convertMessage(item.title) }}
                        </v-list-item-subtitle>
                      </v-list-item-content>
                      <v-list-item-action>
                        <v-list-item-action-text
                          :class="doTimeFormat(item.expire, item.isRead).style"
                        >
                          {{ doTimeFormat(item.expire, item.isRead).content }}
                        </v-list-item-action-text>
                        <v-list-item-action-text>
                          <span
                            v-if="
                              (item.itemType > 0 && !item.isRead) ||
                              (!checkMailCanOpen(item) && !item.isRead)
                            "
                            class="material-symbols-outlined letter-info--text"
                          >
                            attach_file
                          </span>
                          <span
                            v-else-if="
                              (item.itemType > 0 && item.isRead) ||
                              (!checkMailCanOpen(item) && item.isRead)
                            "
                            class="material-symbols-outlined default-content--text mail-attach-file"
                          >
                            attach_file
                          </span>
                        </v-list-item-action-text>
                      </v-list-item-action>
                    </v-list-item>
                  </template>
                </v-list-item-group>
              </v-list>
              <v-row
                v-else
                no-gutters
                align="center"
                justify="center"
                id="mail-no-content"
                :class="['fill-height dialog-fill-2', breakpoint.xsOnly ? 'scrollable-xs' : '']"
              >
                <span class="grey-3--text custom-text-noto text-body-2 no-mails-font">
                  {{ $t(noMailMessages[mailCategoryType]) }}</span
                >
              </v-row>
            </v-col>
            <!-- mail content -->
            <template v-if="!breakpoint.xsOnly || mobile.showMailContentstatus">
              <!-- mail action bar for mobile -->
              <v-col
                v-if="mobile.showMailContentstatus"
                cols="12"
                class="dialog-fill-2 mail-action-bar"
              >
                <v-row no-gutters align="center" class="mail-action-bar-min-height">
                  <v-col cols="8">
                    <v-row no-gutters>
                      <div class="d-flex align-center pl-3">
                        <span
                          class="material-symbols-outlined cursor-pointer default-content--text mail-action-bar-arrow"
                          @click="backToMailList()"
                        >
                          chevron_left
                        </span>
                      </div>
                    </v-row>
                  </v-col>
                  <v-col cols="4">
                    <v-row no-gutters justify="end" class="pr-4">
                      <div class="d-flex align-center mail-bar-button">
                        <v-btn
                          color="transparent"
                          small
                          :disabled="isFromOffical ? !hasLink : false"
                          @click="
                            if (isFromOffical) doRedirectPage(getSingleMail.link)
                            if (!isFromOffical) replyNewMail(getSingleMail.from)
                          "
                        >
                          <span class="material-symbols-outlined mr-3 mail-action-button-icon">
                            {{ isFromOffical ? 'open_in_new' : 'reply' }}
                          </span>
                          <span class="px-0 mx-0">{{
                            this.$t(isFromOffical ? 'goto' : 'write_in_reply')
                          }}</span>
                        </v-btn>
                        <v-btn
                          small
                          color="transparent"
                          class="d-flex align-center mx-2"
                          :disabled="!getSingleMail.isRead"
                          @click="
                            () => {
                              if (
                                getSingleMail.isRead ||
                                $moment()
                                  .subtract(serverTimestamp, 'milliseconds')
                                  .isAfter(getSingleMail.expire)
                              )
                                confirmDeletionStatus = true
                            }
                          "
                        >
                          <span class="material-symbols-outlined mr-3 mail-button-icon-size">
                            delete
                          </span>
                          <span class="px-0 mx-0">{{ this.$t('delete') }}</span>
                        </v-btn>
                      </div>
                    </v-row>
                  </v-col>
                </v-row>
              </v-col>
              <!-- mail content -->
              <!-- no selected -->
              <v-col
                v-if="selection === null || showMailDataArrTmp.length === 0"
                :style="{
                  height: breakpoint.xsOnly ? '100%' : '436px'
                }"
                cols="12"
                sm="7"
                md="7"
                lg="7"
                class="d-flex pa-2 justify-center align-center"
              >
                <span class="grey-3--text custom-text-noto text-body-2 no-select-font">
                  {{
                    showMailDataArrTmp.length === 0
                      ? $t(noMailMessages[mailCategoryType])
                      : $t('please_select_letter')
                  }}
                </span>
              </v-col>
              <!-- selected -->
              <v-col
                v-else
                cols="12"
                sm="7"
                md="7"
                lg="7"
                class="px-6 py-4 px-sm-4 pb-sm-4 pt-sm-3 d-flex flex-column"
                :style="{
                  height: breakpoint.xsOnly ? 'calc(100% - 56px)' : '436px'
                }"
                id="mail-dialog-content"
              >
                <!-- mail action btn for not mobile-->
                <template v-if="!mobile.showMailContentstatus">
                  <div class="d-flex align-center pb-2 mail-bar-button">
                    <v-btn
                      color="transparent"
                      small
                      :disabled="isFromOffical ? !hasLink : false"
                      @click="
                        if (isFromOffical) doRedirectPage(getSingleMail.link)
                        if (!isFromOffical) replyNewMail(getSingleMail.from)
                      "
                    >
                      <span class="material-symbols-outlined mr-3 mail-action-button-icon">
                        {{ isFromOffical ? 'open_in_new' : 'reply' }}
                      </span>
                      <span class="px-0 mx-0">{{
                        this.$t(isFromOffical ? 'goto' : 'write_in_reply')
                      }}</span>
                    </v-btn>
                    <v-btn
                      small
                      color="transparent"
                      class="d-flex align-center mx-2"
                      :disabled="!getSingleMail.isRead"
                      @click="
                        () => {
                          if (
                            getSingleMail.isRead ||
                            $moment()
                              .subtract(serverTimestamp, 'milliseconds')
                              .isAfter(getSingleMail.expire)
                          )
                            confirmDeletionStatus = true
                        }
                      "
                    >
                      <span class="material-symbols-outlined mr-3 mail-button-icon-size">
                        delete
                      </span>
                      <span class="px-0 mx-0">{{ this.$t('delete') }}</span>
                    </v-btn>
                  </div>
                </template>
                <div
                  ref="mailContent"
                  :class="breakpoint.smAndUp ? 'scrollable-sm' : 'scrollable-xs'"
                >
                  <!-- mail title && action bar-->
                  <v-card class="d-flex py-2 mail-title" color="transparent" flat tile>
                    <!-- mail title -->
                    <div
                      class="flex-column mr-auto"
                      :style="{
                        width: mobile.showMailContentstatus ? '100%' : 'unset'
                      }"
                    >
                      <div class="pa-0 my-1">
                        <span class="text--subtitle-1 primary--text">
                          {{ convertMessage(getSingleMail.title) }}
                        </span>
                      </div>
                      <div class="pa-0 my-1">
                        <span class="text-body-2 grey-3--text custom-text-noto">
                          {{ $t('sender') + ': ' + convertMessage(getSingleMail.from) }}
                        </span>
                      </div>
                      <div v-if="!isHiddenSendMailTime" class="pa-0 my-1">
                        <span class="warning--text caption">
                          {{ getCreateText(getSingleMail.creatDate) }}
                        </span>
                      </div>
                    </div>
                  </v-card>
                  <v-divider class="pa-0" />
                  <!-- mail content -->
                  <v-row class="py-2 mail-text mail-content" no-gutters>
                    <pre
                      class="default-content--text custom-text-noto text-body-2 mail-content-text mail-content-font-size"
                      v-text="convertMessage(getSingleMail.content)"
                    />
                  </v-row>
                  <v-divider
                    class="pb-2"
                    v-if="
                      getSingleMail.itemType > 0 ||
                      (getSingleMail.itemType === 0 && !checkMailCanOpen(getSingleMail))
                    "
                  />
                  <!-- gift notice -->
                  <div
                    v-if="checkMailCanOpen(getSingleMail) && getSingleMail.itemType !== 0"
                    class="d-flex pb-1 mail-file"
                  >
                    <span class="text-caption grey-3--text custom-text-noto">
                      {{ $t('remarks_on_sending_gifts').toUpperCase() }}
                    </span>
                  </div>
                  <!-- mail attachment -->
                  <v-card
                    v-if="
                      getSingleMail.itemType > 0 ||
                      (getSingleMail.itemType === 0 && !checkMailCanOpen(getSingleMail))
                    "
                    :height="
                      checkMailCanOpen(getSingleMail)
                        ? hasScrollbar
                          ? '148px'
                          : '136px'
                        : breakpoint.xsOnly
                        ? '76px'
                        : '56px'
                    "
                    class="info-card mail-content-card-rounded"
                    flat
                  >
                    <v-card-text class="pa-2 d-flex align-center fill-height">
                      <template v-if="checkMailCanOpen(getSingleMail)">
                        <div class="swiper-area">
                          <swiper :options="swiperOptions">
                            <swiper-slide
                              v-for="item in receiveableItems"
                              :key="item.uniqueKey"
                              class="mail-receive-item-block mail-attachment-gradient-bg default-content--text text-body-2"
                            >
                              <v-tooltip top min-width="75px" content-class="mail-item-tooltip">
                                <template v-slot:activator="{ on, attrs }">
                                  <div class="mail-receive-item" v-bind="attrs" v-on="on">
                                    <v-img
                                      class="mb-1"
                                      :src="item.mail.imgSrc"
                                      width="40"
                                      height="48"
                                      contain
                                      @error="errorImgHandler(item)"
                                    />
                                    <span
                                      class="mb-1 d-flex align-center text-caption receive-name"
                                    >
                                      <span>{{ item.mail.name || $t('item_loading') }}</span>
                                    </span>
                                    <span class="primary--text text-caption mail-item-count">
                                      {{ transformPrice(item.count) }}
                                    </span>
                                  </div>
                                </template>
                                <span>{{ formatPrice(item.count) }}</span>
                              </v-tooltip>
                            </swiper-slide>
                          </swiper>
                          <div class="swiper-scrollbar mt-2"></div>
                        </div>
                        <div class="ml-3">
                          <v-btn
                            :disabled="
                              getSingleMail.isRead ||
                              $moment()
                                .subtract(serverTimestamp, 'milliseconds')
                                .isAfter(getSingleMail.expire)
                            "
                            elevation="0"
                            rounded
                            class="mt-1 button-content--text"
                            :class="{
                              'gradient-primary':
                                !(getSingleMail.itemType > 0 && getSingleMail.isRead) &&
                                $moment()
                                  .subtract(serverTimestamp, 'milliseconds')
                                  .isBefore(getSingleMail.expire),
                              'grey-4': getSingleMail.itemType > 0 && getSingleMail.isRead
                            }"
                            @click="doReceive"
                          >
                            <span
                              v-if="
                                checkMailCanOpen(getSingleMail) &&
                                !getSingleMail.isRead &&
                                $moment()
                                  .subtract(serverTimestamp, 'milliseconds')
                                  .isBefore(getSingleMail.expire)
                              "
                              v-text="$t('claim')"
                            />
                            <span
                              v-else-if="
                                $moment()
                                  .subtract(serverTimestamp, 'milliseconds')
                                  .isAfter(getSingleMail.expire)
                              "
                              v-text="$t('expired')"
                            />
                            <span
                              v-else-if="getSingleMail.itemType > 0 && getSingleMail.isRead"
                              v-text="$t('claimed')"
                            />
                          </v-btn>
                        </div>
                      </template>
                      <template v-else>
                        <span
                          class="grey-3--text custom-text-noto text-subtitle-2 mail-content-font-size"
                        >
                          <i18n path="go_download_plz">
                            <template v-slot:mark>＊</template>
                            <template v-slot:download>{{ $t('go_download') }}</template>
                          </i18n>
                        </span>
                      </template>
                    </v-card-text>
                  </v-card>
                </div>
              </v-col>
            </template>
            <!-- send mail fab for mobile -->
            <v-btn
              v-if="breakpoint.xsOnly && !mobile.showMailContentstatus"
              class="mx-2 mail-send-mail-fab-mobile"
              elevation="6"
              fab
              dark
              color="gradient-button"
              @click="sendNewMail"
            >
              <v-icon color="black">mdi-pencil-outline </v-icon>
            </v-btn>
          </v-row>
          <!-- 刪除已讀再次確認區域 -->
          <v-dialog
            v-model="confirmDeletionAllStatus"
            persistent
            max-width="380px"
            content-class="rounded-lg"
          >
            <v-card color="transparent" class="pa-4 pa-sm-6">
              <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
                {{ $t('hint') }}
              </v-card-title>
              <v-card-text
                v-if="mailCategoryType === 0"
                class="default-content--text pb-0 pt-6 px-0"
              >
                {{ $t('confirm_delete_all_read_mail_1') }}
              </v-card-text>
              <v-card-text v-else class="default-content--text pb-0 pt-6 px-0">
                {{ $t('confirm_delete_category_read_mail_1') }}
              </v-card-text>
              <v-card-text class="warning--text pt-0 pb-6 px-0">
                {{ $t('confirm_deletion_warning2') }}
              </v-card-text>
              <v-card-actions class="pa-0">
                <v-row no-gutters justify="end">
                  <v-col cols="6" sm="auto" class="pr-2"
                    ><v-btn
                      :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                      text
                      @click="confirmDeletionAllStatus = false"
                    >
                      {{ $t('cancel').toUpperCase() }}
                    </v-btn></v-col
                  >
                  <v-col cols="6" sm="auto" class="pl-2">
                    <v-btn
                      :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                      color="primary"
                      depressed
                      @click="doDeleteReadAll"
                      :loading="isDeleteReadAllLoading"
                      :disabled="isDeleteReadAllLoading"
                    >
                      {{ $t('sure').toUpperCase() }}
                    </v-btn></v-col
                  >
                </v-row>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <!-- 領取全部並已讀再次確認區域 -->
          <v-dialog
            v-model="confirmReadAllStatus"
            persistent
            max-width="380px"
            content-class="rounded-lg"
          >
            <v-card color="transparent" class="pa-4 pa-sm-6">
              <v-card-title class="custom-text-noto text-h6 justify-center grey-1--text pa-0">
                {{ $t('hint') }}
              </v-card-title>
              <v-card-text v-if="mailCategoryType === 0" class="default-content--text py-6 px-0">
                {{ $t('confirm_read_all_mail') }}
              </v-card-text>
              <v-card-text v-else class="default-content--text py-6 px-0">
                {{ $t('confirm_read_all_category') }}
              </v-card-text>
              <v-card-actions class="pa-0">
                <v-row no-gutters justify="end">
                  <v-col cols="6" sm="auto" class="pr-2"
                    ><v-btn
                      :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                      text
                      @click="confirmReadAllStatus = false"
                    >
                      {{ $t('cancel').toUpperCase() }}
                    </v-btn></v-col
                  >
                  <v-col cols="6" sm="auto" class="pl-2">
                    <v-btn
                      :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
                      color="primary"
                      depressed
                      @click="doReceiveAll"
                      :loading="isReceiveAllLoading"
                      :disabled="isReceiveAllLoading"
                    >
                      {{ $t('sure').toUpperCase() }}
                    </v-btn></v-col
                  >
                </v-row>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-container>
      </v-card>
    </v-dialog>
    <mailSendDialog
      v-if="showMailSendDialogStatus"
      :show-mail-send-dialog-status.sync="showMailSendDialogStatus"
      :mail-dialog-status.sync="mailDialogStatusTmp"
      :recipient="defaultRecipient"
    />
    <mailConfirmDeleteDialog
      v-if="confirmDeletionStatus"
      :confirm-deletion-status.sync="confirmDeletionStatus"
      @doDeleteRead="doDeleteRead"
    />
    <remindBindPhoneDialog
      v-if="showRemindBindPhoneDialogStatus"
      :show-remind-bind-phone-dialog-status.sync="showRemindBindPhoneDialogStatus"
    />
    <remindPhoneOrientationDialog
      v-if="showRemindPhoneOrientationDialogStatus"
      :show-remind-phone-orientation-dialog-status.sync="showRemindPhoneOrientationDialogStatus"
    />
  </div>
</template>
<script>
  const STATION = process.env.STATION
  const mailMixin = require(`~/mixins_station/${STATION}/mail`).default
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import relationship from '@/mixins/relationship.js'
  import translator from '~/mixins/translator'
  import images from '~/mixins/images'
  import converter from '~/mixins/converter'
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
  import 'swiper/css/swiper.css'

  export default {
    name: 'MailIndex',
    mixins: [mailMixin, hiddenScrollHtml, relationship, translator, images, converter],
    components: {
      mailSendDialog: () => import(`~/components_station/${STATION}/mail/mailSendDialog`),
      mailConfirmDeleteDialog: () => import('~/components/mail/mailConfirmDeleteDialog'),
      remindBindPhoneDialog: () => import('~/components/mail/remindBindPhoneDialog.vue'),
      customDialogTitle: () => import('~/components/customDialogTitle'),
      remindPhoneOrientationDialog: () =>
        import('~/components/mail/remindPhoneOrientationDialog.vue'),
      Swiper,
      SwiperSlide
    },
    data() {
      return {
        hasScrollbar: false,
        swiperOptions: {
          direction: 'horizontal',
          slidesPerView: 'auto',
          spaceBetween: 8,
          autoplay: false,
          navigation: false,
          observer: true,
          observeParents: true,
          resizeObserver: true,
          watchOverflow: true,
          scrollbar: {
            el: '.swiper-scrollbar',
            draggable: true,
            hide: false,
            snapOnRelease: true,
            dragSize: 100,
            container: '.swiper-container'
          },
          slidesOffsetAfter: 0,
          roundLengths: true,
          freeMode: {
            enabled: true,
            sticky: false,
            momentumBounce: false,
            momentumRatio: 0.5
          }
        }
      }
    },
    props: {
      mailDialogStatus: { type: Object, default: { show: false, name: '' } }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    beforeDestroy() {
      this.$store.dispatch('maintain/fetch')
    }
  }
</script>

<style lang="scss">
  .custom-between {
    display: flex;
  }

  .left {
    flex: 1;
  }

  .mail-content-card-rounded {
    gap: 10px;
  }

  .mail-content-card-rounded.v-sheet.v-card {
    border-radius: 10px;
  }

  .right {
    display: flex;
    flex-direction: column;
  }
  .mail-content-text {
    word-break: break-all;
    white-space: pre-wrap;
  }
  .mail-item-tooltip {
    text-align: center;
  }
  .mail-dialog {
    .mail-content {
      flex: 1;
    }
    .mail-button-icon-size {
      font-size: 18px;
    }
    .mail-list-icon-title {
      width: 100%;
    }
    .mail-list-icon-title-text {
      line-height: 1.3rem;
      width: calc(100% - 1px);
    }
    .mail-list-icon-sub-title {
      opacity: 0.7;
    }
    .mail-attach-file {
      opacity: 0.3;
    }
    .no-mails-font {
      font-size: 14px !important;
    }
    .mail-action-bar {
      height: 56px;
    }
    .mail-action-bar-min-height {
      min-height: 56px;
    }
    .mail-action-bar-arrow {
      font-size: 25px;
    }
    .mail-action-button-icon {
      font-size: 18px;
    }
    .mail-action-button-text {
      font-size: 15px;
      white-space: nowrap;
    }
    .no-select-font {
      font-size: 14px !important;
    }
    .mail-attachment-text-underline {
      text-decoration: underline;
    }
    .mail-content-font-size {
      font-size: 14px !important;
    }
    .mail-content-link {
      padding: 0 2px;
    }
    .mail-send-mail-fab-mobile {
      position: absolute;
      bottom: 16px;
      right: 16px;
    }
    .mail-bar-button button.theme--dark.v-btn.v-btn--has-bg {
      box-shadow: none;
    }
    .mail-bar-button button.theme--dark.v-btn.v-btn--disabled.v-btn--has-bg {
      background: none !important;
    }
    .swiper-area {
      width: 100%; // 容器寬度
      margin: 0;
      position: relative;
      overflow: hidden;
      .mail-receive-item-block {
        width: 80px;
        padding: 8px 4px;
        box-sizing: border-box;
        border-radius: 4px 4px 0 0;
        &:last-child {
          margin-right: 0;
        }
        .mail-receive-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          width: 100%;
          .receive-name {
            width: 100%;
            height: 32px;
            position: relative;
            span {
              width: 100%;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              line-height: 16px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .mail-item-count {
            line-height: 16px !important;
          }
        }
      }
    }
    .swiper-container {
      width: 100%;
    }
    .swiper-scrollbar {
      width: 100%;
      height: 4px;
      cursor: pointer;
      background: #27235e; // 背景色
      pointer-events: none;
      border-radius: 0px;
      transition: opacity 0.3s;
      z-index: 1;
    }
    .swiper-scrollbar-drag {
      height: 100%;
      background: #3e35a8; // 拖動條顏色
      border-radius: 0px;
      pointer-events: auto;
      transition: background-color 0.3s;
    }
  }
  #mail-dialog-content {
    overflow-y: auto;
    .scrollable-sm {
      max-height: calc(90vh - 190px);
      overflow-y: auto;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .scrollable-xs {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    @supports (height: 90svh) {
      .scrollable-sm {
        max-height: calc(90svh - 190px);
      }
    }
  }
  #mail-list,
  #mail-no-content {
    &.scrollable-xs {
      max-height: calc(100vh - 178px);
      overflow-y: auto;
    }
    &.scrollable-sm {
      max-height: calc(90vh - 178px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-xs {
        max-height: calc(100svh - 178px);
      }
      &.scrollable-sm {
        max-height: calc(90svh - 178px);
      }
    }
  }
</style>
