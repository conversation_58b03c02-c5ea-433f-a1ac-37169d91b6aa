<template>
  <v-app>
    <!--
  有 App 屬性的組件記得使用 if 進入完整生命週期,
  否則 vuetify 會計算其寬高並加入樣式中
-->
    <mobile-bar v-if="!showGameModeStatus" @update-header-height="headerHeight = $event" />
    <navigationDrawer v-if="!showGameModeStatus" />
    <v-main v-scroll="onScroll">
      <!-- Mobile Srceen Top Bar Start -->
      <!-- Mobile Srceen Top Bar End -->

      <v-row no-gutters class="h-100-percent justify-space-between align-end">
        <nuxt />
      </v-row>

      <cookie-notice v-model="acceptCookiePolicy" />

      <login v-if="showLoginDialogStatus.show" :show-login-dialog-status="showLoginDialogStatus" />
      <phoneNumBinding
        v-if="showPhoneNumBindingDialogStatus"
        :show-phone-num-binding-dialog-status="showPhoneNumBindingDialogStatus"
      />
      <notyDialog v-if="showNotyDialogStatus" :show-noty-dialog-status="showNotyDialogStatus" />

      <notySnackbar v-if="$store.getters['easySnackbar/GET_SHOW']" />

      <mail
        v-if="mailDialogStatus.show"
        :mail-dialog-status.sync="mailDialogStatus"
        :show-mail-data-arr.sync="showMailDataArr"
      />
      <payment
        v-if="showPaymentDialogStatus"
        :show-payment-dialog-status.sync="showPaymentDialogStatus"
      />
      <role v-if="showRoleDialogStatus" :show-role-dialog-status.sync="showRoleDialogStatus" />
      <create-role-dialog
        v-if="showCreateRoleDialogStatus"
        :show-create-role-dialog-status.sync="showCreateRoleDialogStatus"
      />
      <deviceWhiteDialog
        v-if="showDeviceWhiteDialogStatus"
        :show-device-white-dialog-status.sync="showDeviceWhiteDialogStatus"
      />
      <gameIframe
        v-if="showGameIframeStatus"
        :show-game-iframe-status.sync="showGameIframeStatus"
      />
      <gameCardConfirmDialog
        v-if="showGameCardConfirmDialogStatus.show"
        :show-game-card-confirm-dialog-status.sync="showGameCardConfirmDialogStatus"
      />
      <div
        v-if="!showGameModeStatus"
        :class="{
          'customer-service-fab-btn-with-top': toTopBottom,
          'customer-service-fab-btn': !toTopBottom,
          'notch-left': hasLeftNotch,
          'notch-right': hasRightNotch
        }"
      ></div>
      <div
        :class="[
          'button-group-layer',
          { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
        ]"
      >
        <v-expand-transition>
          <to-top v-show="toTopBottom" />
        </v-expand-transition>
      </div>
      <years18Noty
        v-if="showYears18NotyStatus.show"
        :show-years18-noty-status.sync="showYears18NotyStatus"
      />
      <notify />
    </v-main>

    <logoutDialog
      v-if="showLogoutDialogStatus"
      :show-logout-dialog-status="showLogoutDialogStatus"
    />
    <foot-bar v-if="$vuetify.breakpoint.smAndDown && !showGameModeStatus" />

    <notyNotBinbPhoneDialog
      v-if="showNotyNotBindPhoneDialogStatus"
      :show-noty-not-bind-phone-dialog-status.sync="showNotyNotBindPhoneDialogStatus"
    />

    <musicPlayer />
    <musicPlayerDialog
      v-if="showMusicPlayerDialogStatus"
      :show-music-player-dialog-status="showMusicPlayerDialogStatus"
    />
    <vipPaymentDialog
      v-if="showVipPaymentDialogStatus"
      :show-vip-payment-dialog-status.sync="showVipPaymentDialogStatus"
    />
    <showVipTopUpSuccessDialog
      v-if="showVipTopUpSuccessDialogStatus"
      :show-vip-top-up-success-dialog-status.sync="showVipTopUpSuccessDialogStatus"
    />
    <showRedeemDialog
      v-if="showRedeemDialogStatus"
      :show-redeem-dialog-status.sync="showRedeemDialogStatus"
    />
    <uploadPhotoMethodDialog
      v-if="showUploadPhotoMethodDialogStatus"
      :show-upload-photo-method-dialog-status.sync="showUploadPhotoMethodDialogStatus"
    />
    <chooseLocalPhotoDialog
      v-if="showChooseLocalPhotoDialogStatus"
      :show-choose-local-photo-dialog-status.sync="showChooseLocalPhotoDialogStatus"
    />
    <editPhotoDialog
      v-if="showEditPhotoDialogStatus"
      :show-edit-photo-dialog-status.sync="showEditPhotoDialogStatus"
    />
    <playerInfoCardDialog
      v-if="showPlayerInfoCardDialogStatus"
      :show-player-info-card-dialog-status.sync="showPlayerInfoCardDialogStatus"
    />
    <guildInfoCardDialog
      v-if="guildDialogStatus.show"
      :show-guild-info-card-dialog-status.sync="guildDialogStatus"
    />
    <confirmDeleteFriendDialog
      v-if="confirmDeleteFriendDialogStatus.show"
      :confirm-delete-friend-dialog-status.sync="confirmDeleteFriendDialogStatus"
    />
    <confirmDeleteBlockDialog
      v-if="confirmDeleteBlockDialogStatus.show"
      :confirm-delete-block-dialog-status.sync="confirmDeleteBlockDialogStatus"
    />
    <confirmAddBlockDialog
      v-if="confirmAddBlockDialogStatus.show"
      :confirm-add-block-dialog-status.sync="confirmAddBlockDialogStatus"
    />
    <confirmAddFriendDialog
      v-if="confirmAddFriendDialogStatus.show"
      :confirm-add-friend-dialog-status.sync="confirmAddFriendDialogStatus"
    />
    <confirmDialog
      v-if="serverDisconnectionDialogStatus.show"
      v-model="serverDisconnectionDialogStatus.show"
      :show-cancel="false"
      :action="serverDisconnectionDialogStatus.onConfirmNotify"
    >
      <p class="mb-0 text-wrap default-content--text">
        {{ serverDisconnectionDialogStatus.text }}
      </p>
    </confirmDialog>
    <template v-if="isLogin">
      <chatRoomDialog
        v-show="showChatRoomDialogStatus"
        :show-chat-room-dialog-status.sync="showChatRoomDialogStatus"
      />
    </template>
    <template v-if="isLogin">
      <customerServiceDialog
        v-show="showCustomerServiceDialogStatus"
        :show-customer-service-dialog-status.sync="showCustomerServiceDialogStatus"
      />
    </template>
    <reportDialog v-if="reportDialogStatus.show" :report-dialog-status.sync="reportDialogStatus" />
    <qcGameSetting
      v-if="showQcGameSettingStatus"
      :show-qc-game-setting-status="showQcGameSettingStatus"
    />
  </v-app>
</template>
<script>
  const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
  const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default
  import ipUtils from '@/utils/ip'

  const STATION = process.env.STATION
  export default {
    name: 'Default',
    mixins: [
      require('~/mixins/orientation').default,
      require('~/mixins/syncServerTime').default,
      require('~/mixins/preLoginAction').default
    ],
    components: {
      cookieNotice: () => import('~/components/notifications/cookieNotice'),
      mobileBar: require(`~/components_station/${STATION}/mobileBar`).default,
      navigationDrawer: require(`~/components_station/${STATION}/navigationDrawer`).default,
      login: require(`~/components_station/${STATION}/login/newLogin`).default,
      notyDialog: () => import('~/components/notifications/notyDialog'),
      notySnackbar: () => import('~/components/notifications/notifySnackbar'),
      toTop: () => import('~/components/toTop'),
      footBar: () => import('~/components/footbar'),
      mail: require(`~/components_station/${STATION}/mail/index`).default,
      payment: () => import('~/components/payment/index'),
      role: () => import('~/components/role/index'),
      createRoleDialog: () => import('~/components/role/createRoleDialog.vue'),
      deviceWhiteDialog: () => import('~/components/login/deviceWhiteDialog'),
      phoneNumBinding: () => import('~/components/login/phoneNumBinding'),
      logoutDialog: () => import('~/components/logoutDialog'),
      gameIframe: () => import('~/components/game/gameIframe'),
      years18Noty: require(`~/components_station/${STATION}/notifications/years18Noty`).default,
      notyNotBinbPhoneDialog: () => import('~/components/phoneBind/notyNotBindPhoneDialog'),
      musicPlayer: () => import('~/components/music/musicPlayer'),
      musicPlayerDialog: () => import('~/components/music/musicPlayerDialog'),
      vipPaymentDialog: () => import('~/components/payment/vipPaymentDialog'),
      showVipTopUpSuccessDialog: () => import('~/components/payment/showVipTopUpSuccessDialog'),
      showRedeemDialog: () => import('~/components/payment/showRedeemDialog'),
      uploadPhotoMethodDialog: () => import('~/components/player_info/uploadPhotoMethodDialog'),
      chooseLocalPhotoDialog: () => import('~/components/player_info/chooseLocalPhotoDialog'),
      editPhotoDialog: () => import('~/components/player_info/editPhotoDialog'),
      playerInfoCardDialog: () => import('~/components/player_info/playerInfoCardDialog'),
      confirmDeleteFriendDialog: () =>
        import('~/components/relationShip/confirmDeleteFriendDialog'),
      gameCardConfirmDialog: () => import('~/components/game/gameCardConfirmDialog'),
      confirmDeleteBlockDialog: () => import('~/components/relationShip/confirmDeleteBlockDialog'),
      confirmAddBlockDialog: () => import('~/components/relationShip/confirmAddBlockDialog'),
      confirmAddFriendDialog: () => import('~/components/relationShip/confirmAddFriendDialog'),
      chatRoomDialog: () => import('~/components/chatroom/index'),
      customerServiceDialog: () => import(`~/components/chatroom/customerServiceDialog`),
      guildInfoCardDialog: () => import('~/components/guild/guildInfoCardDialog'),
      reportDialog: require(`~/components_station/${STATION}/relationShip/reportDialog`).default,
      qcGameSetting: require(`~/components_station/${STATION}/qcGameSetting`).default,
      confirmDialog: () => import('~/components/confirmDialog')

      // dice: () => import('~/components/dice'),
    },
    data() {
      return {
        acceptCookiePolicy: true,
        showLoginDialogStatus: {
          show: false,
          onCancelNotify: () => {}
        },
        toTopBottom: false,
        showNotyDialogStatus: false,
        showNotySnackbarStatus: false,
        showPaymentDialogStatus: false,
        mailDialogStatus: { show: false, name: '' },
        showRoleDialogStatus: false,
        showCreateRoleDialogStatus: false,
        showDeviceWhiteDialogStatus: false,
        showLogoutDialogStatus: false,
        showPhoneNumBindingDialogStatus: false,
        showGameIframeStatus: false,
        showMailDataArr: [],
        showYears18NotyStatus: { show: false, onConfirmNotify: () => {} },
        showNotyNotBindPhoneDialogStatus: false,
        showWelcomeStatus: false,
        showMusicPlayerDialogStatus: false,
        showVipPaymentDialogStatus: false,
        env: process.env.NUXT_ENV,
        //vip購點成功
        showVipTopUpSuccessDialogStatus: false,
        //選擇上傳照片方式
        showUploadPhotoMethodDialogStatus: false,
        //選擇本地照片
        showChooseLocalPhotoDialogStatus: false,
        // 編輯頭貼照片
        showEditPhotoDialogStatus: false,
        showPlayerInfoCardDialogStatus: false,
        confirmDeleteFriendDialogStatus: { show: false, name: '' },
        confirmDeleteBlockDialogStatus: { show: false, name: '' },
        confirmAddBlockDialogStatus: { show: false, name: '' },
        confirmAddFriendDialogStatus: { show: false, name: '' },
        showChatRoomDialogStatus: false,
        showCustomerServiceDialogStatus: false,
        showRedeemDialogStatus: false,
        reportDialogStatus: { show: false, name: '' },
        guildDialogStatus: { show: false, info: '' },
        showGameCardConfirmDialogStatus: {
          show: false,
          hasExp: false,
          hasRobot: false,
          onConfirmNotify: () => {},
          onCancelNotify: () => {}
        },
        showQcGameSettingStatus: false,
        headerHeight: 0,
        serverDisconnectionDialogStatus: {
          show: false,
          text: '',
          onConfirmNotify: () => {}
        }
      }
    },
    head() {
      let dict = this.getRouteBaseName(this.$route)
      if (!dict) {
        dict = `${this.$t('navigation.index')}-NOT FOUND`
      } else if (dict === 'game_list') {
        dict = `${this.$t('navigation.index')}-${this.$t(
          `navigation.game_list.${this.$route.query.category}`
        )}`
      } else if (dict.includes('player_info')) {
        dict = `${this.$t('navigation.index')}-${this.$t(`navigation.${dict.split('-').pop()}`)}`
      } else if (dict.includes('teach')) {
        dict = `${this.$t('navigation.index')}-${this.$t(
          `navigation.teach_${dict.split('-').pop()}`
        )}`
      } else if (dict.includes('news')) {
        dict = `${this.$t('navigation.index')}-${this.$t('navigation.news')}`
      } else if (dict.includes('game')) {
        dict = `${this.$t('navigation.index')}-${this.$t('navigation.game')}`
      } else {
        dict = `${this.$t('navigation.index')}-${this.$route.fullPath}`
      }
      return {
        title: `${dict}`,
        meta: [
          { hid: 'keywords', name: 'keywords', content: this.$t('seo.keyword') },
          {
            hid: 'description',
            name: 'description',
            content: this.$t('seo.description')
          },
          {
            hid: 'og:description',
            name: 'og:description',
            content: this.$t('seo.description')
          }
        ],
        bodyAttrs: {}
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      serverMsg({ $store }) {
        return $store.getters['xinProtocol/serverMsg']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      status404({ $store }) {
        return $store.getters['maintain/status404']
      },
      maintainStatus({ $store }) {
        return $store.getters['maintain/maintainStatus']
      },
      newlyOnlineFriend({ $store }) {
        return $store.getters['social/newlyOnlineFriend']
      },
      friendList({ $store }) {
        return $store.getters['social/friendList']
      },
      hasGuild({ $store }) {
        return $store.getters['guild/hasGuild']
      },
      hasNotyText({ $store }) {
        const stringEmpty = (string) => string === '' || string === null || string === undefined
        const messageEmpty = stringEmpty($store.getters['easyDialog/GET_MESSAGE'])
        const titleEmpty = stringEmpty($store.getters['easyDialog/GET_TITLE'])
        return !(messageEmpty && titleEmpty)
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      }
    },
    watch: {
      maintainStatus: {
        handler() {
          // 平台維護檢查
          if (this.maintainSystem[0].maintaining) {
            this.resetAllDialogStatus()
            this.$nuxt.error({
              statusCode: 503,
              params: {
                maintainBeginAt: this.maintainSystem[0].maintainBeginAt,
                maintainEndAt: this.maintainSystem[0].maintainEndAt
              }
            })
            //登出流程
            if (this.$wsClient?.isConnected) {
              const reqData = this.$wsPacketFactory.logout()
              this.$wsClient.send(reqData)
            }
            this.logoutAction()
          }
        },
        immediate: true
      },
      serverMsg: {
        // 收到伺服器訊息將在這裡被做動，目前將連線邏輯全部放在Store，
        // 方便後續維護，vuex 是獨立的，並辦法沒有使用nuxt 實例
        // 為了避免畫面邏輯跟資料邏輯混在一起，所以這樣拆開
        async handler(msg) {
          if (msg !== '') {
            const title = this.$t('reminder')
            let message = msg
            if (this.$te(message)) {
              message = this.$t(message)
            }
            if (msg === 'new_mail_revice_noty') {
              this.$notify.info(message)
            } else if (msg === 'reconnected') {
              this.$notify.success(message)
            } else if (msg === 'friend_online') {
              if (this.friendList.find((item) => item.username === this.newlyOnlineFriend)) {
                this.$notify.info(
                  this.$t('friend_online_noty', { userName: this.newlyOnlineFriend })
                )
              }
            } else if (msg === 'guild_reward_noty4') {
              this.$notify.warning(this.$t(msg))
            } else if (msg === 'server_kick_noty2') {
              // 被server踢掉，如果是正在遊戲中，按下確認後回到首頁
              let isPlayingGame =
                this.$route.params.mode === 'play' || this.$route.params.mode === 'demo'
              const dialogData = {
                show: true,
                text: this.$t(msg),
                onConfirmNotify: () => {
                  isPlayingGame && this.$router.push(this.localePath('/'))
                }
              }
              this.$nuxt.$emit('root:serverDisconnectionDialogStatus', dialogData)
              this.logoutAction()
              this.resetAllDialogStatus()
            } else {
              // 通知被server 踢掉
              console.log(message)
              await this.$store.dispatch('easyDialog/setDialog', {
                title: title,
                message: message
              })
              this.$nuxt.$emit('root:showNotyDialogStatus', true)
              this.logoutAction()
              this.resetAllDialogStatus()
            }
            this.$store.commit('xinProtocol/SET_SERVER_MSG', '')
          }
        }
      },
      '$i18n.locale': {
        handler() {
          if (this.isLogin) {
            this.$store.dispatch('role/fetch')
          }
        }
      },
      status404: {
        handler(status404) {
          if (status404) {
            this.$nextTick(() => {
              this.$nuxt.error({ statusCode: 404 })
              this.$store.commit('maintain/SET_STATUS404', false)
            })
          }
        },
        immediate: true
      },
      '$nuxt.$route.fullPath': {
        handler(now, old) {
          const infoPage = '/guild/info'
          if (!old.includes(infoPage) && now.includes(infoPage) && !this.hasGuild) {
            this.$notify.warning(this.$t('guild_reward_noty5'))
            this.$router.push(this.localePath('/guild/list'))
          }
        }
      }
    },
    async created() {
      this.$store.commit('SET_STATION', loadConfig)
    },
    beforeMount() {
      // 全域下監聽啓閉
      const globalEvent = {
        'root:showNotyDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          if (this.hasNotyText) this.showNotyDialogStatus = !!val
        },
        'root:showPaymentDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showPaymentDialogStatus = !!val
        },
        'root:mailDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.mailDialogStatus = val
        },
        'root:showRoleDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showRoleDialogStatus = !!val
        },
        'root:showCreateRoleDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showCreateRoleDialogStatus = !!val
        },
        'root:showDeviceWhiteDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showDeviceWhiteDialogStatus = !!val
        },
        'root:showLogoutDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showLogoutDialogStatus = !!val
        },
        'root:showLoginDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showLoginDialogStatus = val
        },
        'root:showGameIframeStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showGameIframeStatus = !!val
        },
        'root:showPhoneNumBindingDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showPhoneNumBindingDialogStatus = !!val
        },
        'root:showNotyNotBindPhoneDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showNotyNotBindPhoneDialogStatus = !!val
        },
        'root:showWelcomeStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showWelcomeStatus = !!val
        },
        'root:showMusicPlayerDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showMusicPlayerDialogStatus = !!val
        },
        'root:showVipPaymentDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showVipPaymentDialogStatus = !!val
        },
        'root:showVipTopUpSuccessDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showVipTopUpSuccessDialogStatus = !!val
        },
        'root:showUploadPhotoMethodDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showUploadPhotoMethodDialogStatus = !!val
        },
        'root:showChooseLocalPhotoDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showChooseLocalPhotoDialogStatus = !!val
        },
        'root:showEditPhotoDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showEditPhotoDialogStatus = !!val
        },
        'root:showPlayerInfoCardDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showPlayerInfoCardDialogStatus = !!val
        },
        'root:confirmDeleteFriendDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.confirmDeleteFriendDialogStatus = val
        },
        'root:confirmDeleteBlockDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.confirmDeleteBlockDialogStatus = val
        },
        'root:confirmAddBlockDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.confirmAddBlockDialogStatus = val
        },
        'root:confirmAddFriendDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.confirmAddFriendDialogStatus = val
        },
        'root:showChatRoomDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showChatRoomDialogStatus = !!val
        },
        'root:showRedeemDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showRedeemDialogStatus = !!val
        },
        'root:showCustomerServiceDialogStatusEvent': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          if (!this.isLogin && !this.maintainSystem[0].maintaining) {
            this.showLoginDialogStatus = { show: true }
            this.setPreLoginAction(
              'customerService',
              this.showCustomerServiceDialogStatusEvent,
              true
            )
          } else {
            this.showCustomerServiceDialogStatus = !!val
          }
        },
        'root:reportDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.reportDialogStatus = val
        },
        'root:showGameCardConfirmDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showGameCardConfirmDialogStatus = val
        },
        'root:guildDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.guildDialogStatus = val
        },
        'root:showQcGameSettingStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showQcGameSettingStatus = val
        },
        'root:showYears18NotyStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.showYears18NotyStatus = val
        },
        'root:serverDisconnectionDialogStatus': async (val) => {
          await this.$store.dispatch('maintain/fetch')
          this.serverDisconnectionDialogStatus = val
        }
      }

      for (const [event, func] of Object.entries(globalEvent)) {
        this.$nuxt.$on(event, func)
      }
      this.$store.dispatch('xinProtocol/initReceived')
      window.addEventListener('beforeunload', this.handleBeforeUnload)
      window.addEventListener('unload', this.handleUnload, { once: true })
      // 禁止縮放監聽
      let lastTouchEndTime = 0
      document.addEventListener(
        'touchend',
        (event) => {
          const now = new Date().getTime()
          if (now - lastTouchEndTime <= 300) {
            // 偵測時間差是否小於 300ms
            event.preventDefault()
          }
          lastTouchEndTime = now
        },
        false
      )

      document.addEventListener(
        'touchstart',
        (event) => {
          if (event.touches && event.touches.length > 1) {
            // 禁止多指觸控
            event.preventDefault()
          }
        },
        { passive: false }
      )
    },
    async mounted() {
      this.$localStorage.set('shownNoty', [])
      this.$store.dispatch('music/fetchMusic')
      this.$store.dispatch('allStar/fetchGameLobby')
      this.$store.dispatch('allStar/fetchGameFish')
      this.$store.dispatch('role/setPlayerProfile')
      //確保畫面已渲染完成
      this.$nextTick(() => {
        requestAnimationFrame(() => {
          this.acceptCookiePolicy = !!(
            this.$localStorage.get('accept_cookie_policy').expired &&
            this.$moment(this.$localStorage.get('accept_cookie_policy').expired).isAfter(
              this.$moment()
            )
          )
          //fb pixel inital
          // GDPR 歐盟法規
          if (this.acceptCookiePolicy) {
            this.$fb.enable()
          }
        })
      })
      if (!this.showGameModeStatus && this.$device.isSamsung) {
        const title = this.$t('reminder')

        await this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: this.$t('samsung_dark_mode_noty') + '</br>' + this.$t('samsung_dont_use_pwa')
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      }

      // 獲取 18+ 提示的 localStorage
      const { enable = 0 } = this.$localStorage.get('years18Noty') || {}
      // 獲取已顯示過的提示清單
      const shownNoty = this.$localStorage.get('shownNoty') || []
      // 是否顯示 18+ 提示
      const showYears18Noty = !shownNoty.includes('years18Noty') && enable === 0
      if (!this.showGameModeStatus && showYears18Noty) {
        this.showYears18NotyStatus = { show: true }
        // 若 localStorage 沒有值，則設置預設值
        if (!this.$localStorage.get('years18Noty')) {
          this.$localStorage.set('years18Noty', { enable: 0 })
        }
      }
      try {
        await ipUtils.initIP(this)
      } catch (error) {
        console.error('IP initialization failed:', error)
      }
    },
    beforeDestroy() {
      window.removeEventListener('beforeunload', this.handleBeforeUnload)
      if (this.$store._modulesNamespaceMap['xinProtocol/initReceived']) {
        this.$store.unregisterModule('xinProtocol/initReceived')
      }
    },
    methods: {
      onScroll() {
        this.toTopBottom = document.documentElement.scrollTop >= window.innerHeight / 2
      },
      logoutAction() {
        if (this.$wsClient) this.$wsClient.disconnect()
        this.$store.dispatch('clear')
        this.$cookies.remove('xinToken', { path: '/' })
      },
      handleBeforeUnload(event) {
        if (this.$wsClient.isConnected) {
          event.preventDefault()
          const confirmationMessage = this.$t('confirmLeaveNoty1')
          event.returnValue = confirmationMessage
          return confirmationMessage
        }
      },
      handleUnload() {
        if (this.$wsClient.isConnected) {
          const reqData = this.$wsPacketFactory.logout()
          this.$wsClient.send(reqData)
        }
        this.logoutAction()
        window.removeEventListener('unload', this.handleUnload)
      },
      showCustomerServiceDialogStatusEvent(val) {
        if (!this.isLogin) {
          this.showLoginDialogStatus = { show: true }
        } else {
          this.showCustomerServiceDialogStatus = val
        }
      },
      resetAllDialogStatus() {
        this.showPaymentDialogStatus = false
        this.mailDialogStatus = { show: false, name: '' }
        this.showRoleDialogStatus = false
        this.showDeviceWhiteDialogStatus = false
        this.showLogoutDialogStatus = false
        this.showLoginDialogStatus = { show: false, onCancelNotify: () => {} }
        this.showGameIframeStatus = false
        this.showYears18NotyStatus = { show: false, onConfirmNotify: () => {} }
        this.showPhoneNumBindingDialogStatus = false
        this.showNotyNotBindPhoneDialogStatus = false
        this.showMusicPlayerDialogStatus = false
        this.showVipPaymentDialogStatus = false
        this.showVipTopUpSuccessDialogStatus = false
        this.showUploadPhotoMethodDialogStatus = false
        this.showChooseLocalPhotoDialogStatus = false
        this.showEditPhotoDialogStatus = false
        this.showPlayerInfoCardDialogStatus = false
        this.confirmDeleteFriendDialogStatus = { show: false, name: '' }
        this.confirmDeleteBlockDialogStatus = { show: false, name: '' }
        this.confirmAddBlockDialogStatus = { show: false, name: '' }
        this.confirmAddFriendDialogStatus = { show: false, name: '' }
        this.showChatRoomDialogStatus = false
        this.showCustomerServiceDialogStatus = false
        this.showRedeemDialogStatus = false
        this.reportDialogStatus = { show: false, name: '' }
        this.guildDialogStatus = { show: false, info: '' }
        this.showGameCardConfirmDialogStatus = {
          show: false,
          hasExp: false,
          hasRobot: false,
          onConfirmNotify: () => {},
          onCancelNotify: () => {}
        }
        this.showCreateRoleDialogStatus = false
        this.serverDisconnectionDialogStatus = {
          show: false,
          text: '',
          onConfirmNotify: () => {}
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  @import '~vuetify/src/styles/settings/_variables.scss';
  $xs: map-get($display-breakpoints, 'xs-only');
  $sm: map-get($display-breakpoints, 'sm-only');

  .v-application {
    &.theme--light {
      background: map-get($colors, grey-5) !important;
    }
  }

  .layout-default {
    @import '@/assets/scss/locale';
  }

  .button-group-layer {
    transition: all 0.5s;
    position: fixed;
    width: 48px;
    height: auto;
    bottom: calc(40px - var(--overflowTop, 0px));
    right: 10px;
    margin-bottom: 16px;
    margin-right: 16px;
    z-index: 4;

    @media #{$sm} {
      bottom: calc(80px - var(--overflowTop, 0px));
    }

    @media #{$xs} {
      bottom: calc(60px - var(--overflowTop, 0px));
      right: 0px;
    }
    @media (orientation: landscape) {
      &.notch-left {
        bottom: calc(60px - var(--overflowTop, 0px));
        right: 0px;
      }
      &.notch-right {
        right: env(safe-area-inset-right) !important;
        bottom: calc(60px - var(--overflowTop, 0px));
      }
    }
  }

  .customer-service-fab-btn {
    transition: all 0.5s;
    position: fixed;
    width: 48px;
    height: auto;
    bottom: 40px;
    right: 10px;
    margin-bottom: 16px;
    margin-right: 16px;
    z-index: 4;

    @media #{$sm} {
      bottom: 80px;
    }

    @media #{$xs} {
      bottom: 60px;
      right: 0px;
    }
    @media (orientation: landscape) {
      &.notch-left {
        bottom: 60px;
        right: 0px;
      }
      &.notch-right {
        right: env(safe-area-inset-right) !important;
        bottom: 60px;
      }
    }
  }
  .customer-service-fab-btn-with-top {
    transition: all 0.5s;
    position: fixed;
    width: 48px;
    height: auto;
    bottom: calc(104px - var(--overflowTop, 0px));
    right: 10px;
    margin-bottom: 16px;
    margin-right: 16px;
    z-index: 4;

    @media #{$sm} {
      bottom: calc(144px - var(--overflowTop, 0px));
    }

    @media #{$xs} {
      bottom: calc(108px - var(--overflowTop, 0px));
      right: 0px;
    }
    @media (orientation: landscape) {
      &.notch-left {
        bottom: calc(108px - var(--overflowTop, 0px));
        right: 0px;
      }
      &.notch-right {
        right: env(safe-area-inset-right) !important;
        bottom: calc(108px - var(--overflowTop, 0px));
      }
    }
  }
</style>
