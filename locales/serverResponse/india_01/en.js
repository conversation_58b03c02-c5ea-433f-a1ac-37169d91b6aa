const serverResponse = {
  lang_15: '{0} WinCoins have been allocated to the character.',
  lang_31:
    'Due to prepaid card purchases, you can redeem points after the bill is credited to the account (App Store & Google Play in-app purchases: 60 days, credit card purchases: 30 days). The WinCoin balance must be over {0} in the safe.',
  lang_49: 'The attachment amount must exceed 10,000 WinCoins.',
  lang_51: 'WinCoins must not exceed {0}',
  lang_54:
    'Transferring {0} WinCoins will requires {1} Honor Point. Please acquire Honor Points through leveling up or other activities.',
  lang_91: '{0} WinCoins have been claimed and added to the Reward Coin Balance.',
  lang_187:
    'Congratulations on becoming an official member! We kindly remind you to exchange points for WinCoins before enjoying our games.',
  lang_190: 'Insufficient WinCoins',
  lang_192:
    'Congratulations on becoming an official member! Reminder: Please exchange points for WinCoins before enjoying our games.',
  lang_206: 'Obtained a total of {0} WinCoins',
  lang_208: 'Top-up:{0} Total: {1} WinCoins received.',
  lang_222: '{0} WinCoins earned as referral rewards!',
  lang_238: 'Incorrect WinCoin amount',
  lang_251:
    'The amount must not exceed 1,000,000 Star Points (equivalent to 140,000,000 WinCoins).',
  lang_252: 'Your balance must not be below 20,000  WinCoins after the transaction.',
  lang_262: "Please deposit your WinCoins via the 'Buy & Top-up' page.",
  lang_299: 'Received WinCoins',
  lang_302:
    'You have redeemed {0} points and earned eligibility for {1} day(s) of extra rewards. Before the Reward Card expiry date {2}\r\n**, you can claim 2,000 WinCoins through daily logins.',
  lang_303: 'Withdraw {0} Star Points Total: {1} WinCoins.',
  lang_307: 'Win up to 1 million WinCoins.',
  lang_325: 'The total amount of WinCoins and Safebox must not exceed 1 billion.',
  lang_343: 'Total WinCoins Earned: {0}',
  lang_346:
    'Club Rank: {0}. Members will receive WinCoins based on their contributed Club Points {1}.',
  lang_349: 'WinCoins',
  lang_394: 'WinCoins: {0} x {1}',
  lang_401: '{0} has sent you {1} (Earned additional {2} WinCoins)',
  lang_409: 'You have earned {0} WinCoins.\r\nPlease visit [Transaction & Redemption] to claim it.'
}

export default {
  ...serverResponse
}
