const basic = {
  daily_limit: 'The transaction limit for today is {coin}.',
  mail_send_noty6:
    'The function is available for characters with Bronze or above and a bound mobile phone number.',
  send_mail_description1:
    'Only {vip_noty} who have already linked a mobile number, and made purchases or used the JOYOUS Starter Pack can use the sending feature.',
  send_mail_description1_noty1: 'Bronze players or higher',
  send_mail_limit_description1_noty:
    'There is a daily transaction limit for Bronze and Silver players.',
  redeem_gift_noty:
    'Redemption complete. You are now an official member and received 10,000 Star Coins. Happy gaming!',
  redeem_gift_noty1:
    'Redemption complete. Congratulations on receiving 10,000 Star Coins. Happy gaming!',
  vip_level_desc: 'Member Rank Info',
  your_rank: 'Your Rank',
  rank: 'Rank',
  send_mail_description8:
    'Mailing fees for official members of the same Club vary by their member rank.',
  top_up_success_noty2_1: 'You are now a formal member and have been upgraded to Bronze.',
  membership_level_alert: 'Bronze or above is available to participate in the General Chat.',
  opponent_membership_level_not_silver_above:
    'The player is below Silver and is unavailable to receive a private chat.',
  literalNoty: 'Prohibited from entering sensitive words.',
  first_login_noty: 'Login to register, please select your login method.'
}

export default {
  ...basic
}
