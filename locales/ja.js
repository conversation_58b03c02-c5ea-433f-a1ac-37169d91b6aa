/* eslint-disable quotes */

const currencys = {
  MYR: 'Ringgit',
  IDR: 'Rupiah'
}

const validations = {
  attributes: {
    nickname: 'Nickname',
    phone_number: 'Phone Number',
    verify_code: 'Verification Code',
    country_code: 'Country Code',
    message: 'Message',
    name: 'Name',
    skype: 'Skype',
    email: '<PERSON><PERSON>',
    year: 'Year',
    month: 'Month',
    day: 'Day',
    begin_at: 'Start Time',
    end_at: 'End Time',
    enter_email: 'Enter Email',
    enter_league_id: 'Enter League ID',
    enter_account: 'Enter Account',
    enter_id: 'Enter ID',
    enter_username: 'Enter nickname',
    enter_id_or_phone_number: 'Enter ID or Phone Number',
    enter_phone_number: 'Enter Phone Number',
    enter_group_id: 'Enter Group ID',
    enter_channel_id: 'Enter Channel ID',
    enter_complete_phone_number: 'Enter Phone Number',
    month_gmt8: 'Month (GMT+8)'
  }
}
const STATION = process.env.STATION
const basic = require(`./basic/ja`).default
const serverResponse = require(`./serverResponse/ja`).default
export default {
  ...basic,
  currencys,
  navigation: require(`./navigation/${STATION}/ja`).default,
  validations,
  seo: require(`./seo/${STATION}/ja`).default,
  ...serverResponse,
  error: require('./error/ja').default,
  locales: require('./locale/ja').default,
  locations: require('./location/ja').default
}
