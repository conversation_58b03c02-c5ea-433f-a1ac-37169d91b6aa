<!-- eslint-disable vue/no-v-html -->
<template>
  <v-dialog
    v-model="showNotyNotRealMemberDialogStatusTmp"
    persistent
    max-width="380"
    content-class="rounded-lg"
  >
    <v-card color="transparent" class="pa-4 pa-sm-6">
      <v-card-title
        class="custom-text-noto text-h6 justify-center font-weight-regular grey-1--text pa-0"
        v-text="$t('reminder')"
      />
      <v-card-text class="default-content--text py-6 px-0" v-html="message" />
      <v-card-actions no-gutters class="pa-0">
        <v-row no-gutters justify="end">
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pr-2">
            <v-btn
              :class="['custom-text-noto default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="closeDialog()"
            >
              {{ $t('latter').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col :cols="breakpoint.xsOnly ? '6' : 'auto'" class="pl-2">
            <v-btn
              depressed
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              @click="showPaymentDialog()"
            >
              {{ $t('upgrade_real').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import utilWhiteList from '@/utils/whiteList.js'
  import { mapGetters } from 'vuex'
  export default {
    name: 'notyNotRealMember',
    mixins: [hiddenScrollHtml],
    props: {
      showNotyNotRealMemberDialogStatus: { type: Boolean, default: false }
    },
    data() {
      return {
        showNotyNotRealMemberDialogStatusTmp: this.showNotyNotRealMemberDialogStatus
      }
    },
    computed: {
      breakpoint() {
        return this.$vuetify.breakpoint
      },
      singleGameHallInfo({ $store }) {
        return $store.getters['gameHall/singleGameHallInfo']
      },
      message() {
        return (
          '<p class="mb-0 text-wrap">' +
          this.$t('not_real_member_noty1') +
          '</p>' +
          '<p class="mb-0 text-wrap">' +
          this.$t('not_real_member_noty3') +
          this.vipLevel +
          '</p>' +
          '<p class="mb-0 text-wrap">' +
          this.$t('not_real_member_noty2') +
          '</p>'
        )
      },
      ...mapGetters('role', ['vipLevelTitle']),
      vipLevel() {
        let level = this.singleGameHallInfo.vipLevel

        const singleGameVipLevel = [...this.vipLevelTitle]
        singleGameVipLevel.shift()
        singleGameVipLevel.splice(1, 0, 'bronzeLV10')

        if (level === -1) level += 1
        return this.$t(singleGameVipLevel[level])
      }
    },
    watch: {
      showNotyNotRealMemberDialogStatus: {
        handler(status) {
          this.showNotyNotRealMemberDialogStatusTmp = status
        }
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showNotyNotRealMemberDialogStatus', false)
      },
      showPaymentDialog() {
        this.closeDialog()
        const url = window.location.origin
        const userName = this.$store.getters['role/userName']
        const openDialog = utilWhiteList.showShoppingMall(userName, url)
        if (openDialog) this.$nuxt.$emit('root:showPaymentDialogStatus', true)
      }
    }
  }
</script>
