<template>
  <v-col
    :cols="
      (!isLoading && (!topPlayersData?.count || maintainSystem[0].maintaining)) ||
      isBreakpoint.pageRankingData.sm
        ? 12
        : isBreakpoint.pageRankingData.lg
        ? 5
        : 6
    "
    class="h-100-percent"
  >
    <v-card color="transparent" flat>
      <v-card-title
        class="winners-title px-0 mb-2 gradient-primary--text font-weight-bold"
        :class="{ 'text-no-wrap': $vuetify.breakpoint.smAndUp }"
        >WINNERS</v-card-title
      >
      <v-card-subtitle
        class="winners-subtitle pa-0 text-h4 white--text ma-0 d-flex align-center button-content--text"
        >{{ $t('leaderboard_dragontiger') }}
        <rank-tips-dialog content-type="leaderboard">
          <template #hintButton>
            <v-btn x-small rounded icon>
              <span class="material-symbols-outlined"> info </span>
            </v-btn>
          </template>
        </rank-tips-dialog>
      </v-card-subtitle>
      <v-card-text
        v-if="topPlayersData?.count && !maintainSystem[0].maintaining"
        class="winners-btn-group pa-0"
      >
        <div class="d-flex my-5" :class="{ 'flex-wrap': isBreakpoint.pageRankingData.sm }">
          <v-btn
            v-for="(tab, index) in rankTabs"
            :key="tab"
            :color="activeRank === index ? 'gradient-button' : 'rgba(26,11,11,0.65)'"
            class="mr-2 py-4 px-4"
            @click="handleActiveRankChange(index)"
            depressed
            ><span
              :class="[
                'grey-2--text font-weight-normal',
                { 'button-content--text': activeRank === index }
              ]"
              >{{ $t(tab) }}</span
            ></v-btn
          >
        </div>
        <div class="date-pick">
          <v-menu v-model="dateMenu" attach=".date-pick">
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                class="w-100 d-flex justify-space-between align-center py-2 px-6"
                color="rgba(26,11,11,0.65)"
                v-bind="attrs"
                v-on="on"
                depressed
              >
                <span
                  class="white--text text-body-1 custom-text-noto font-weight-regular"
                  :class="{ 'text-wrap': $vuetify.breakpoint.xs }"
                  >{{ dateSelected }}</span
                >
                <v-icon right color="white">mdi-menu-down</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="(date, index) in datePickItems"
                :key="index"
                active-class="date-active"
                :input-value="dateSelected === date.date"
                @click="handleDateChange(date)"
              >
                <v-list-item-title
                  class="text-body-1 default-content--text custom-text-noto font-weight-regular"
                  >{{ date?.date }}</v-list-item-title
                >
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
        <v-list flat class="pa-0 transparent">
          <v-list-item
            :class="['profile pa-0 w-100', { 'align-start': $vuetify.breakpoint.xs }]"
            v-for="winner in setWinnerFlags"
            :key="winner?.playerRankData?.sort"
          >
            <div class="profile-avatar">
              <div class="profile-avatar-image">
                <picture>
                  <source :srcset="winner?.webSrc" type="image/webp" />
                  <img :src="winner?.pngSrc" height="100%" width="100%" />
                </picture>
              </div>
            </div>
            <v-list-item-content class="profile-card pa-0">
              <div class="profile-info w-100">
                <div
                  class="profile-header player-ranking-top-three-bg-fill-1 d-flex justify-space-between"
                  :class="$vuetify.breakpoint.xs ? 'align-start flex-column' : 'align-center'"
                >
                  <div class="profile-rank">
                    <span
                      class="profile-username white--text text-subtitle-2 custom-text-noto text-truncate"
                      :class="{ 'text-wrap pt-1 pl-7': $vuetify.breakpoint.xs }"
                      @click="openPlayerInfo($event, winner?.playerRankData?.username)"
                      >{{ winner?.playerRankData?.username }}</span
                    >
                  </div>
                  <div
                    class="profile-score d-flex align-center gray-7 h-100-percent px-2 py-1 text-no-wrap"
                  >
                    <span>{{ isWinsRank ? $t('leaderboard_score') : $t('win_multiplier') }}</span>
                    <span
                      class="text-body-2 gradient-primary--text pl-1 custom-text-noto"
                      :class="{ 'text-wrap': $vuetify.breakpoint.xs }"
                      contenteditable="false"
                      >{{
                        isWinsRank
                          ? winner?.playerRankData?.report?.winAmount
                          : winner?.playerRankData?.report?.odds
                      }}</span
                    >
                  </div>
                </div>

                <div
                  class="profile-content pa-2 d-flex align-center"
                  :class="{
                    'profile-content-active':
                      userPlayerData?.count && winner?.playerRankData?.username === userName
                  }"
                >
                  <v-avatar class="profile-game-icon mr-2" size="40">
                    <v-img :src="winner?.playerRankData?.game_logo_url" />
                  </v-avatar>
                  <div class="w-100">
                    <span class="profile-tag white--text px-2 mb-1 text-no-wrap">{{
                      !!winner?.playerRankData?.platform
                        ? $t('xincity_platform_web')
                        : $t('xincity')
                    }}</span>
                    <div
                      @click="openPlatformGame(winner?.playerRankData)"
                      class="profile-game-name white---text mt-1 text-body-2 custom-text-noto text-truncate"
                    >
                      {{
                        winner?.playerRankData?.hasActive
                          ? winner?.playerRankData?.text
                          : $t(winner?.playerRankData?.text)
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </v-list-item-content>
          </v-list-item></v-list
        >
      </v-card-text>
      <rankEmpty
        v-else-if="!isLoading && (!topPlayersData?.count || maintainSystem[0].maintaining)"
      />
    </v-card>
  </v-col>
</template>

<script>
  import _ from 'lodash'
  import leaderboard from '~/mixins/leaderboard'

  export default {
    name: 'WinnersSection',
    components: {
      rankEmpty: () => import('~/components/leaderboard/rankEmpty.vue'),
      rankTipsDialog: () => import('~/components/leaderboard/rankTipsDialog.vue')
    },
    mixins: [leaderboard],
    props: {
      userPlayerData: {
        type: Object,
        dafault: () => {}
      }
    },
    data() {
      return {
        dateMenu: false,
        rankTabs: [
          'leaderboard_daily_dragon',
          'leaderboard_daily_tiger',
          'leaderboard_weekly_dragon',
          'leaderboard_weekly_tiger'
        ],
        dateSelected: '',
        datePickItems: []
      }
    },
    computed: {
      setWinnerFlags() {
        if (!this.topPlayersData?.count) return []
        const createFlagImgs = (rank, playerRankData) => ({
          webpSrc: require(`~/assets/image/leaderboard/ranking-badge-${rank}.webp`),
          pngSrc: require(`~/assets/image/leaderboard/ranking-badge-${rank}.png`),
          playerRankData
        })

        const { list } = this.topPlayersData
        const winners = list.map((item) => {
          return { ...item, ...this.setPlatformGameDetails(item) }
        })
        return [
          createFlagImgs('01', winners[0]),
          createFlagImgs('02', winners[1]),
          createFlagImgs('03', winners[2])
        ]
      },
      defaultDateConfig() {
        return {
          day: this.lastWeekDayBeforeToday[0],
          week: this.fourWeeksAgoDate[0]
        }
      }
    },
    async created() {
      this.debouncedHandleOpenPlatformGame = _.debounce(this.handleOpenPlatformGame, 300)
      this.debouncedHandleOpenPlayerInfo = _.debounce(this.handleOpenPlayerInfo, 300)
      if (process.client) await this.initialRankData()
    },
    methods: {
      async initialRankData() {
        await this.handleDateChange(this.defaultDateConfig.day)
        this.dateSelected = this.lastWeekDayBeforeToday[0]?.date
        this.datePickItems = this.lastWeekDayBeforeToday
        this.$nextTick(() => {
          this.$emit('section-height-updated')
        })
      },
      async handleDateChange(date) {
        if (!date) return
        // 當選擇日期重複, 而且排行榜有資料時不進行請求
        if (this.topPlayersData?.count && this.dateSelected === date.date) return
        this.dateSelected = date.date
        this.dateMenu = false

        const dateParams = {
          type: this.isDayRank ? 0 : 1,
          beginAt: date.beginAt,
          endAt: date.endAt,
          limit: 199
        }
        await this.getTopPlayersData(dateParams)
        if (!this.topPlayersData?.count) return
        // this.$store.commit('leaderboard/RESET_USER_PLAYER_DATA')
        await Promise.all([this.getWebGameList(), this.fetchGameRtpHandler()])
        if (!this.isLogin) return
        const userPlayerData = await this.getUserPlayer(dateParams)
        this.$emit('update:user-player-data', userPlayerData)
      },
      handleActiveRankChange(val) {
        // 根據選擇的類型更新日期選擇器和數據
        const isDaily = val === 0 || val === 1
        const defaultDate = isDaily ? this.defaultDateConfig.day : this.defaultDateConfig.week
        const dateItems = isDaily ? this.lastWeekDayBeforeToday : this.fourWeeksAgoDate

        this.datePickItems = dateItems
        this.dateSelected = dateItems[0]?.date

        // 切換日週榜時請求數據
        if (val !== this.activeRank) {
          this.dateSelected = ''
          this.$emit('update-active-rank', val)
          this.handleDateChange(defaultDate)
        }
      },
      async resetRankAndUpdateData() {
        this.$store.commit('leaderboard/SET_ACTIVE_RANK', 0)
        await this.handleDateChange(this.defaultDateConfig.day)
      },
      openPlayerInfo(event, username) {
        this.debouncedHandleOpenPlayerInfo(event, username)
      },
      openPlatformGame(playerRankData) {
        this.debouncedHandleOpenPlatformGame(playerRankData)
      },
      handleOpenPlayerInfo(event, username) {
        this.$emit('open-player-info', event, username)
      },
      handleOpenPlatformGame(playerRankData) {
        this.$emit('open-platform-game', playerRankData)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $grey7: map-get($colors, 'grey-7');
  $grey-7-65: map-get($colors, 'grey-7-opacity-65');
  $player-ranking-top-three-bg-fill-2-self: linear-gradient(
    180deg,
    rgba(26, 11, 11, 0.65) 0%,
    rgba(165, 114, 89, 0.65) 100%
  );

  .winners {
    &-title {
      font-size: 60px;
    }
    &-subtitle {
      ::v-deep .ranking-hint {
        padding: 0 8px !important;
        background: transparent;
        align-items: center;
        display: flex;
      }
    }
    letter-spacing: 6px;
    &-btn-group {
      .v-btn {
        border-radius: 28px;
        &:hover {
          span:not(.button-content--text) {
            color: #fff !important;
          }
        }
        span {
          letter-spacing: 0.5px;
        }
      }
      .date-pick {
        margin-bottom: 50px;
        @media screen and (max-width: 780px) {
          margin-bottom: 54px;
        }
        .date-active {
          background: rgba(255, 255, 255, 0.08);
          &::before {
            background: none;
          }
        }
        .v-btn {
          background-color: transparent !important;
          border: 1px solid rgba(255, 255, 255, 0.24) !important;
          overflow: hidden;
          .v-icon {
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    .profile {
      position: relative;
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
        @media screen and (max-width: 780px) {
          margin-bottom: 26px;
        }
      }
      &-avatar {
        width: 50px;
        height: 100px;
        &-image {
          position: absolute;
          width: 68px;
          height: 68px;
          left: 0;
          top: 50%;
          transform: translate(25%, -50%);
          z-index: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &::before {
          content: '';
          position: absolute;
          width: 100px;
          height: 100px;
          border-radius: 96px;
          border: 4px solid #a57259;
          background: radial-gradient(100% 100% at 50% 0%, #1a0b0b 0%, #1a0b0b 50%, #4b3521 100%);
          z-index: 1;
          left: 0;
          top: 50%;
          transform: translate(0, -50%);
        }
      }
      &-card {
        position: relative;
        overflow: unset;
        flex: 1;
        width: calc(100% - 50px);
      }
      &-header {
        padding-left: 66px;
        position: relative;
        .profile-rank {
          width: 64%;
          @media screen and (max-width: 1650px) {
            width: 50%;
          }
          @media screen and (max-width: 900px) {
            width: 40%;
          }
          @media screen and (max-width: 780px) {
            width: 70%;
          }
          .profile-username {
            cursor: pointer;
            display: inline-block;
            width: 80%;
          }
        }
        .profile-score {
          width: 36%;
          @media screen and (max-width: 1650px) {
            width: 50%;
          }
          @media screen and (max-width: 900px) {
            width: 60%;
          }
          @media screen and (max-width: 780px) {
            width: 30%;
          }
        }
      }
      &-score {
        padding-left: 38px !important;
        background-color: $grey7;
        display: flex;
        align-items: center;
        clip-path: polygon(15% 0, 100% 0, 100% 100%, 0% 100%);
        span {
          color: rgba(255, 255, 255, 0.5);
          font-size: 12px;
          letter-spacing: 0.4px;
        }
      }
      &-content {
        padding-left: 66px !important;
        background: $grey-7-65;
        overflow: hidden;
        > div {
          min-width: 130px;
        }
        &-active {
          padding-left: 66px !important;
          background: $player-ranking-top-three-bg-fill-2-self;
        }
      }
      &-game-icon {
        border-radius: 4px 4px 4px 0px;
      }
      &-tag {
        padding-top: 2px;
        padding-bottom: 2px;
        border-radius: 4px 0px;
        background: rgba(255, 255, 255, 0.3);
        font-size: 10px;
        letter-spacing: 0.4px;
        line-height: 1;
      }
      &-game-name {
        font-size: 28px;
        font-weight: 400;
        cursor: pointer;
      }
    }

    @media screen and (max-width: 599px) {
      .profile {
        &-avatar {
          width: 35px;
          min-width: 35px;
          height: 35px;
          &::before {
            width: 70px;
            height: 70px;
            top: 0;
            transform: translate(0%, 0%);
          }
          &-image {
            width: 52px;
            height: 52px;
            left: 9px;
            top: 12%;
            transform: translate(0%, 0%);
          }
        }
        &-header {
          padding-left: 33px;
          gap: 4px;
          .profile-rank {
            width: 100%;
            .profile-username {
              width: 80%;
            }
          }
          .profile-score {
            width: 100%;
          }
        }
        &-score {
          padding-left: 36px !important;
          width: 100%;
          clip-path: polygon(6% 0, 100% 0, 100% 100%, 0% 100%);
        }
        &-content {
          margin-top: -6px;
          margin-left: -31px;
          padding-top: 14px !important;
          padding-left: 84px !important;
          border-radius: 0 0 4px 4px;
          &-active {
            margin-top: -6px;
            margin-left: -31px;
            padding-top: 14px !important;
            padding-left: 84px !important;
            border-radius: 0 0 4px 4px;
            background: $player-ranking-top-three-bg-fill-2-self;
          }
          .profile-game-name {
            width: 90%;
          }
        }
      }
    }
  }
</style>
