<template>
  <v-container class="pa-0">
    <v-card
      :color="$UIConfig.stationPage.backGround"
      class="px-4 px-sm-6 py-6"
      style="width: 100%"
      :elevation="$vuetify.breakpoint.smAndDown ? 0 : 4"
      :class="{ 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }"
    >
      <v-card-title class="px-0 py-0">
        <span class="custom-text-noto text-h5 default-content--text font-weight-bold">{{
          $t('personal_status')
        }}</span>
      </v-card-title>
      <v-card-text class="px-0 py-0">
        <!-- character -->
        <characterInfo :info="info" is-info-page />
        <!-- player's app money and badge -->
        <playerAppAsset v-if="$UIConfig.playerAppAssetStatus" :app-assets="appAssets" />
        <v-divider class="mt-6 mb-6"></v-divider>
        <!-- character-detail -->
        <playerDetail :user-detail="info" is-info-page />
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
  import { mapGetters } from 'vuex'
  import guildMgr from '~/mixins/guildMgr'
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'PersonalInfoContent',
    mixins: [guildMgr, orientation],
    components: {
      characterInfo: () => import('~/components/characterInfo'),
      playerAppAsset: () => import('~/components/player_info/playerAppAsset'),
      playerDetail: () => import('~/components/player_info/playerDetail')
    },
    data() {
      return {
        userDetail: {}
      }
    },
    created() {
      this.$store.dispatch('role/updateUserDetail')
      this.updatePersonalBadge()
      this.$wsClient.send(this.$wsPacketFactory.fatchBalance())
    },
    computed: {
      ...mapGetters('role', [
        'accountType',
        'userName',
        'rank',
        'level',
        'vipLevel',
        'vipLevelTitle',
        'vipLevelImgFileName',
        'balance',
        'honor',
        'activeValue',
        'isLogin',
        'isBind',
        'phoneNumber',
        'referralMonany',
        'gameId',
        'platformId',
        'facebookId',
        'online',
        'silverCoin',
        'copperCoin',
        'pkCoin',
        'ruby'
      ]),
      guildName() {
        return this.$store.getters['guild/guildName']
      },
      info() {
        return {
          userName: this.userName,
          rank: this.rank,
          level: this.level,
          vipLevel: this.vipLevel,
          vipLevelTitle: this.vipLevelTitle,
          vipLevelImgFileName: this.vipLevelImgFileName,
          balance: this.balance,
          honor: this.honor,
          activeValue: this.activeValue,
          isLogin: this.isLogin,
          phoneNumber: this.phoneNumber,
          facebookId: this.facebookId,
          guildName: this.guildName,
          accountType: this.accountType,
          isBind: this.isBind,
          platformId: this.platformId,
          gameId: this.gameId,
          referralMonany: this.referralMonany,
          online: this.online,
          silverCoin: this.silverCoin
        }
      },
      appAssets() {
        return [
          {
            assetName: 'silver_coin',
            isCoin: true,
            imgName: 'coin_silver',
            quality: this.silverCoin
          },
          {
            assetName: 'copper_coin',
            isCoin: true,
            imgName: 'coin_copper',
            quality: this.copperCoin
          },
          { assetName: 'pk_coin', isCoin: true, imgName: 'coin_pk', quality: this.pkCoin.count },
          { assetName: 'ruby', isCoin: true, imgName: 'coin_ruby', quality: this.ruby.count },
          {
            assetName: 'personal_badge',
            isCoin: false,
            imgName: 'badge_bronze',
            quality: this.personalBadge.playerHave,
            total: this.personalBadge.total
          }
        ]
      }
    },
    methods: {
      openBindPhoneDialog() {
        this.$nuxt.$emit('root:showPhoneNumBindingDialogStatus', true)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .v-data-table ::v-deep {
    tbody {
      tr:hover {
        background-color: map-get($colors, 'grey-4');
      }
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
