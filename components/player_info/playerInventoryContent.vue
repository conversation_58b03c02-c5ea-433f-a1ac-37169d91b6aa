<template>
  <v-container class="pa-0">
    <v-card
      :color="$UIConfig.stationPage.backGround"
      :elevation="$vuetify.breakpoint.smAndDown ? 0 : 4"
      :class="['w-100 px-4 px-sm-6', { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }]"
    >
      <v-card-title class="px-0 pt-6">
        <span class="custom-text-noto text-h5 default-content--text font-weight-bold">{{
          $t('backpack_title')
        }}</span>
      </v-card-title>
      <v-card-text class="px-0 pb-6">
        <!-- description -->
        <v-row
          no-gutters
          class="custom-text-noto text-caption grey-3--text pb-4"
          style="font-size: 12px !important"
        >
          <v-col cols="12">
            <i18n path="item_get_and_use_hint" tag="span">
              <template v-slot:download>
                <span class="primary--text" style="cursor: pointer" @click="openRedirectDialog">{{
                  $t('daily_list_desc2_2')
                }}</span>
              </template>
            </i18n>
          </v-col>
        </v-row>
        <!-- 各種物品類別：狀態類 & 遊戲類 & 代幣類 & 其他類 -->
        <v-row no-gutters>
          <v-col cols="12">
            <v-tabs
              v-model="tab"
              class="pb-4"
              align-with-title
              show-arrows
              background-color="transparent"
              color="primary"
              @change="changeTabEvent"
            >
              <v-tab v-for="tab in tabs" :key="tab.id" :href="`#${tab.id}`">
                {{ $t(tab.title) }}
              </v-tab>
            </v-tabs>
            <v-tabs-items v-model="tab" @change="changeTabEvent">
              <v-tab-item v-for="tabItem in tabs" :key="tabItem.id" :value="tabItem.id">
                <!-- 沒取得道具資訊 JSON(從 GCP) or 背包道具封包(從總部)-->
                <div
                  v-if="
                    !itemInfo ||
                    ((tabItem.id === '1' || tabItem.id === '4') && !statusInventory) ||
                    (tabItem.id === '2' && !gameInventory)
                  "
                  class="d-flex flex-column align-center"
                  :class="[$UIConfig.gamePage.background]"
                >
                  <span class="text-subtitle-1 default-content--text custom-text-noto pb-1">{{
                    $t('backpack_loading')
                  }}</span>
                  <span class="custom-text-noto text-caption grey-3--text">{{
                    $t('backpack_error_tip')
                  }}</span>
                </div>
                <!-- 正常取得清單 -->
                <template v-else-if="pageItemList.length > 0">
                  <v-row class="gutters-16" :class="[$UIConfig.gamePage.background]">
                    <v-col
                      v-for="(item, index) in pageItemList"
                      :key="index"
                      cols="4"
                      sm="3"
                      md="2"
                    >
                      <v-card
                        v-if="!item.id.includes('placeholder')"
                        class="item-card cursor-pointer"
                        flat
                        @click="openPlayerInventoryDialog(item)"
                        :ref="tabItem.id === tab ? 'itemCard' : ''"
                      >
                        <div class="item-content pa-2 pb-1">
                          <v-img :src="item.imgSrc" @error="errorImgHandler(item)">
                            <span
                              v-if="item.betAmount"
                              class="custom-text-noto text-body-1 font-weight-bold bet-amount"
                              :class="item.coinType === 1 ? 'bet-amount-gold' : 'bet-amount-silver'"
                              >{{ item.betAmount }}</span
                            >
                          </v-img>
                          <v-card-text
                            class="pa-0 text-center text-caption default-content--text custom-text-noto text-truncate"
                          >
                            {{ formatCountAbbr(item.count) }}
                          </v-card-text>
                        </div>
                      </v-card>
                      <v-card v-else class="item-card" flat>
                        <div class="item-content pa-2 pb-1">
                          <v-img
                            :src="item.imgSrc"
                            class="visibility-hidden"
                            @error="errorImgHandler(item)"
                          ></v-img>
                          <v-card-text
                            class="pa-0 text-center text-caption default-content--text custom-text-noto text-truncate visibility-hidden"
                          >
                            -
                          </v-card-text>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                  <!-- pagination -->
                  <v-pagination
                    v-model="pageNumber"
                    :length="pageObj.pageTotal"
                    total-visible="7"
                    circle
                    :color="$UIConfig.defaultBtnColor"
                    class="pt-3 mt-4"
                    :class="[$UIConfig.gamePage.background]"
                    @input="changePageEvent"
                  />
                </template>
                <!-- 清單內容為空 -->
                <div
                  v-else
                  class="d-flex flex-column align-center"
                  :class="[$UIConfig.gamePage.background]"
                >
                  <span class="text-subtitle-1 default-content--text custom-text-noto pb-1">{{
                    $t('backpack_empty_message')
                  }}</span>
                  <i18n
                    path="go_to_starcity_experience_more"
                    tag="span"
                    class="custom-text-noto text-caption grey-3--text"
                  >
                    <template v-slot:download>
                      <span
                        class="text-decoration-underline cursor-pointer download-link"
                        @click="openRedirectDialog"
                        >{{ $t('go_download') }}</span
                      >
                    </template>
                  </i18n>
                </div>
              </v-tab-item>
            </v-tabs-items>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <playerInventoryDialog
      v-if="showPlayerInventoryDialogStatus"
      :item="item"
      :show-player-inventory-dialog-status.sync="showPlayerInventoryDialogStatus"
    />
  </v-container>
</template>

<script>
  import orientation from '@/mixins/orientation'
  import { mapGetters } from 'vuex'
  import cloneDeep from 'lodash/cloneDeep'
  import debounce from 'lodash/debounce'

  const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
  const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

  export default {
    mixins: [orientation],
    name: 'playerInventoryContent',
    components: {
      playerInventoryDialog: () => import('~/components/player_info/playerInventoryDialog.vue')
    },
    data() {
      // 類別依序為：狀態類 & 遊戲類 & 代幣類 & 其他類
      const tabs = [
        { title: 'backpack_tab_status', id: '1' },
        { title: 'backpack_tab_game', id: '2' },
        { title: 'backpack_tab_coin', id: '3' },
        { title: 'backpack_tab_other', id: '4' }
      ]
      const pageObj = {
        showItemLimit: 12,
        pageTotal: 1,
        startIndex: 0,
        endIndex: 0
      }
      return {
        tab: '1',
        tabs,
        pageNumber: 1,
        pageObj,
        itemInfo: null,
        itemList: [],
        tabItemList: [],
        pageItemList: [],
        defaultImgWebp: require('~/assets/image/inventory_card_default.webp'),
        defaultImgPng: require('~/assets/image/inventory_card_default.png'),
        debounceGetCardWidth: debounce(this.getCardWidth, 200),
        showPlayerInventoryDialogStatus: false,
        item: {}
      }
    },
    computed: {
      // 狀態類 & 其他類：背包道具(statusInventory)、高額鑰匙(premiumKey)
      // 遊戲類：背包道具(gameInventory)
      // 代幣類：pk幣(pkCoin)、紅寶石(ruby)、小鬥幣(battleCoin)
      ...mapGetters('role', [
        'statusInventory',
        'gameInventory',
        'pkCoin',
        'ruby',
        'battleCoin',
        'premiumKey'
      ]),
      // 初始化所有道具清單
      originList() {
        const list = []

        // 檢查是否有收到狀態類、其他類的道具封包
        if (this.statusInventory) {
          list.push(...this.statusInventory)
        }

        // 檢查是否有收到遊戲類的道具封包
        if (this.gameInventory) {
          const gameInventoryMap = new Map()

          this.gameInventory.forEach((item) => {
            const baseKey = `${item.id}-${item.cardType}-${item.coinType}`
            const fullKey = `${baseKey}-${item.betAmount}`
            if (gameInventoryMap.has(fullKey)) {
              gameInventoryMap.get(fullKey).count += 1
            } else {
              gameInventoryMap.set(fullKey, {
                ...item,
                baseKey,
                fullKey,
                count: 1
              })
            }
          })

          list.push(...Array.from(gameInventoryMap.values()))
        }

        // 檢查代幣類、其他類道具是否有效（需同時滿足 id 存在且數量大於 0）
        const isValidItem = (item) => item?.id && item?.count > 0
        if (isValidItem(this.pkCoin)) list.push(this.pkCoin)
        if (isValidItem(this.ruby)) list.push(this.ruby)
        if (isValidItem(this.battleCoin)) list.push(this.battleCoin)
        if (isValidItem(this.premiumKey)) list.push(this.premiumKey)

        return list
      }
    },
    watch: {
      orientation: {
        handler() {
          this.scrollToTop()
        }
      },
      originList: {
        async handler() {
          // 若原始清單改變，重新去 GCP 取得最新道具資訊，並整合成道具清單
          await this.getItemInfo()
          this.doPagination()
          this.getItemList()
          this.getTabItemList()
          this.getPageItemList()
          this.getPageTotal()
          this.$nextTick(() => {
            requestAnimationFrame(() => {
              requestAnimationFrame(() => {
                this.debounceGetCardWidth()
              })
            })
          })
        },
        immediate: true
      }
    },
    async created() {
      await this.getItemInfo()
    },
    mounted() {
      this.$nextTick(() => {
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            this.debounceGetCardWidth()
            window.addEventListener('resize', this.debounceGetCardWidth)
          })
        })
      })
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.debounceGetCardWidth)
      document.documentElement.style.removeProperty('--inventory-card-width')
    },
    methods: {
      openRedirectDialog() {
        this.$nuxt.$emit('root:redirectDialogStatus', { show: true, drawAnalytics: false })
      },
      scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      async getItemInfo() {
        try {
          const resInfo = await this.$axios.get(
            process.env.IMAGE_URL +
              `/inventory_items/item_provider/${loadConfig.client_id}/item_provider.json?` +
              Math.random()
          )
          this.itemInfo = resInfo.data
        } catch (error) {
          this.itemInfo = null
          console.warn('Failed to get item info：', error)
        }
      },
      getCardWidth() {
        if (this.$refs.itemCard && this.$refs.itemCard[0] && this.$refs.itemCard[0].$el) {
          const inventoryCardWidth = this.$refs.itemCard[0].$el.offsetWidth
          document.documentElement.style.setProperty(
            '--inventory-card-width',
            `${inventoryCardWidth}px`
          )
        }
      },
      getItemList() {
        if (!this.itemInfo || this.originList.length === 0) {
          this.itemList = []
          return
        }

        // 用 Map 建立查找表
        const itemInfoMap = new Map(
          this.itemInfo
            .filter((info) => info.isVisibleInBag) // 先過濾出 isVisibleInBag 為 true 的項目
            .map((info) => {
              if (info.type === 2) {
                return [`${info.id}-${info.cardType}-${info.coinType}`, info]
              }
              return [info.id, info]
            })
        )

        // 過濾並合併資訊
        const list = Array.from(itemInfoMap.keys()).flatMap((key) => {
          // 找出所有符合這個 key 的 originItem
          const matchingItems = this.originList
            .filter((originItem) => (originItem.baseKey ?? originItem.id).toString() === key)
            .sort((a, b) => (a.betAmount ?? 0) - (b.betAmount ?? 0)) // 依 betAmount 從小到大排序
          if (matchingItems.length === 0) return []

          const itemInfo = itemInfoMap.get(key)
          return matchingItems.map((originItem) => ({
            ...itemInfo,
            ...(originItem.betAmount !== undefined ? { betAmount: originItem.betAmount } : {}),
            ...(originItem.fullKey !== undefined ? { fullKey: originItem.fullKey } : {}),
            count: originItem.count,
            imgSrc: `${process.env.IMAGE_URL}/inventory_items/item_img/${loadConfig.client_id}/${
              itemInfo.img
            }?t=${Date.now()}`
          }))
        })

        this.itemList = list
      },
      getTabItemList() {
        this.tabItemList = this.itemList.filter((item) => item.type.toString() === this.tab)
      },
      getPageItemList() {
        this.$nuxt.$loading.start()
        let list = []

        if (this.tabItemList.length) {
          for (let index = this.pageObj.startIndex; index <= this.pageObj.endIndex; index++) {
            if (this.tabItemList[index]) list.push(this.tabItemList[index])
          }

          // 計算需要添加多少個空項來達到 12 的倍數
          const remainder = list.length % 12
          const nullItemsToAdd = remainder === 0 ? 0 : 12 - remainder

          // 添加空項
          for (let i = 0; i < nullItemsToAdd; i++) {
            list.push({ type: this.tab, id: `placeholder-${i}`, imgSrc: this.defaultImgWebp })
          }
        }

        this.pageItemList = cloneDeep(list)
        // 若道具詳情彈窗開啟，資料變動時更新 this.item
        if (this.showPlayerInventoryDialogStatus) {
          this.item =
            this.pageItemList.find((pageItem) =>
              this.item.fullKey
                ? pageItem.fullKey === this.item.fullKey
                : pageItem.id === this.item.id
            ) || this.item
        }
        // 添加 loading，避免內容不同產生畫面縮放的錯位感
        this.$nextTick(() => {
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              setTimeout(() => {
                this.$nuxt.$loading.finish()
              }, 200)
            })
          })
        })
      },
      getPageTotal() {
        this.pageObj.pageTotal = Math.ceil(this.tabItemList.length / this.pageObj.showItemLimit)
      },
      // 根據目前頁數，計算道具清單應該取用哪一段資料（取得 startIndex、endIndex）
      doPagination() {
        const { showItemLimit } = this.pageObj
        const startIndex = (this.pageNumber - 1) * showItemLimit
        this.pageObj.startIndex = startIndex
        this.pageObj.endIndex = startIndex + showItemLimit - 1
      },
      /**
       * 將數量格式化為縮寫字串（K, M, B, T），並依規則裁切長度
       * 規則：
       * 1. 數量達4位數時，使用單位K、M、B、T縮寫顯示。
       * 2. 最多顯示6字元（數字+小數點+單位），小數點後不補零，超出長度直接捨去。
       * 3. 超過9,999兆則顯示為9999T+。
       * 4. 小於1000則直接顯示原數字。
       * 5. 移除小數點後不必要的零（例如：4.000M => 4M）。
       *
       * @param {number} count - 要格式化的數量
       * @returns {string|number} - 格式化後的字串或原數字
       */
      formatCountAbbr(count) {
        // 檢查是否為有效數字，若不是則回傳'-'
        if (typeof count !== 'number' || isNaN(count)) {
          return '-'
        }

        // 超過 9,999 兆，直接顯示 9999T+
        if (count > 9999 * 1e12) {
          return '9999T+'
        }

        // 定義單位與對應的數值
        const units = [
          { value: 1e12, symbol: 'T' }, // 兆
          { value: 1e9, symbol: 'B' }, // 十億
          { value: 1e6, symbol: 'M' }, // 百萬
          { value: 1e3, symbol: 'K' } // 千
        ]

        // 找出最適合的單位（從大到小）
        const unit = units.find((u) => count >= u.value)
        // 若小於1000，直接回傳原數字
        if (!unit) return count

        // 取得縮寫單位下的數值
        let num = count / unit.value
        let str = num.toString()

        // 若數字加單位超過6字元，需裁切小數點後長度
        if (str.length + unit.symbol.length > 6) {
          // 拆分整數與小數部分
          const [intPart, decPart] = str.split('.')
          if (intPart.length >= 4) {
            // 整數部分已達4位，直接取前4位（只會發生在T單位）
            str = intPart.slice(0, 4)
          } else if (decPart) {
            // 計算可保留的小數位數，確保總長度不超過6
            const decLen = 6 - unit.symbol.length - 1 - intPart.length
            str = intPart + '.' + decPart.slice(0, decLen)
          } else {
            // 沒有小數部分，直接用整數
            str = intPart
          }
        }

        // 移除小數點後不必要的零
        if (str.includes('.')) {
          str = str.replace(/\.?0+$/, '')
        }

        // 回傳格式化後的字串（數字+單位）
        return str + unit.symbol
      },
      changeTabEvent() {
        this.pageNumber = 1
        this.doPagination()
        this.getTabItemList()
        this.getPageItemList()
        this.getPageTotal()
        this.$nextTick(() => {
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              this.debounceGetCardWidth()
            })
          })
        })
      },
      changePageEvent() {
        this.doPagination()
        this.getPageItemList()
        // 在 Safari 瀏覽器中，window.scrollTo()方法的behavior參數不支援'smooth'值，只支援'auto'和'instant'兩個值。
        this.scrollToTop()
      },
      errorImgHandler(errorItem) {
        // 找到對應 ID 的索引位置：
        // tabItemIndex 來取得原始圖片連結（作為比較基準）
        // pageItemIndex 用來改變渲染到畫面的資料
        const tabItemIndex = this.tabItemList.findIndex((item) =>
          errorItem.fullKey ? item.fullKey === errorItem.fullKey : item.id === errorItem.id
        )
        const pageItemIndex = this.pageItemList.findIndex((item) =>
          errorItem.fullKey ? item.fullKey === errorItem.fullKey : item.id === errorItem.id
        )
        const pageItem = this.pageItemList[pageItemIndex]
        const isPlaceholder = errorItem.id.includes('placeholder')

        // 情況 1：第一次載入失敗（原始圖片）
        if (!isPlaceholder && errorItem.imgSrc === this.tabItemList[tabItemIndex].imgSrc) {
          // 嘗試將圖片格式從 .webp 替換為 .png
          pageItem.imgSrc = errorItem.imgSrc.replace('.webp', '.png')
          return
        }

        // 情況 2：若目前不是預設圖片，則使用預設 webp 圖片
        if (
          !isPlaceholder &&
          errorItem.imgSrc !== this.defaultImgWebp &&
          errorItem.imgSrc !== this.defaultImgPng
        ) {
          pageItem.imgSrc = this.defaultImgWebp
        }
        // 情況 3：如果預設 webp 圖片加載失敗，則嘗試預設 png 圖片
        else if (errorItem.imgSrc === this.defaultImgWebp) {
          pageItem.imgSrc = this.defaultImgPng
        }
      },
      openPlayerInventoryDialog(item) {
        this.item = item
        this.showPlayerInventoryDialogStatus = true
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-color: map-get($colors, primary);
  .v-tab {
    color: rgba(255, 255, 255, 0.6) !important;
    &--active {
      color: $primary-color !important;
    }
  }

  @media (orientation: landscape) {
    .notch-left {
      padding-left: calc(16px + env(safe-area-inset-left)) !important;
    }
    .notch-right {
      padding-right: calc(16px + env(safe-area-inset-right)) !important;
    }
    //sm
    @media (min-width: 600px) {
      .notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      .notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
  }
  .v-tabs-items {
    background-color: transparent !important;
  }
  .gutters-16 {
    margin: -8px;
    > div {
      padding: 8px;
    }
  }
  .download-link {
    transition: color 0.2s ease-in;
    &:hover {
      color: $primary-color;
    }
  }
  .item-card {
    background: transparent;
    position: relative;
    padding: 5px;
    &::after {
      content: '';
      position: absolute;
      inset: 0; /* 等同於 top: 0; right: 0; bottom: 0; left: 0; */
      border-radius: inherit;
      padding: 1px; /* 邊框寬度 */
      background: linear-gradient(to right bottom, #855036, #a57259, #f5d5b5, #a57259, #855036);
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      pointer-events: none;
    }
    .item-content {
      background: linear-gradient(
        to right top,
        rgba(#855036, 0.3),
        rgba(#d9924b, 0.3),
        rgba(#855036, 0.3)
      );
      .visibility-hidden {
        visibility: hidden;
      }
      .bet-amount {
        position: absolute;
        bottom: 0;
        left: 8%;
        width: 100%;
        aspect-ratio: 2.7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: calc(var(--inventory-card-width) * 0.135) !important;
        &.bet-amount-gold {
          color: #830000;
        }
        &.bet-amount-silver {
          color: #00326c;
        }
      }
    }
  }
</style>
