<template>
  <v-container fluid :class="['pa-0', { 'notch-left': hasLeftNotch }]">
    <!-- Top Banner  -->
    <v-row
      no-gutters
      justify="center"
      id="bannerRow"
      :class="['pa-4 pa-md-6 px-lg-0 px-xl-0', { 'notch-right': hasRightNotch }]"
    >
      <v-col cols="12" lg="9" class="mw-75-v">
        <banner :banner-adapt="bannerAdapt" />
      </v-col>
    </v-row>
    <!-- marquee -->
    <v-row no-gutters justify="center" class="mb-6 px-4 px-md-6 pa-lg-0">
      <v-col cols="12" lg="9" class="mw-75-v" :class="{ 'notch-right': hasRightNotch }">
        <marquee />
      </v-col>
    </v-row>
    <!-- Games Content -->
    <v-row class="text-center mb-md-10 mb-6" justify="center" no-gutters>
      <v-col cols="12" lg="9" xl="9" class="px-lg-0 px-md-6 px-sm-4 px-4 mw-75-v">
        <!-- inCompleteGame Content -->
        <inCompleteGame :prop-game-list="inCompleteGameList" />
        <!-- recentGames Content -->
        <recentGames :prop-game-list="recentGameList" />
        <!-- liveGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <liveGameList
            :prop-game-list="liveGameList"
            :prop-game-sort-type="liveGameSet.sortType"
            :game-category-id="liveGameSet.category"
            :block-title-capital="true"
          />
        </v-lazy>
        <!-- leaderboard Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <daily-rankings />
        </v-lazy>
        <!-- hotGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <popularGameList
            :prop-game-list="hotGameList"
            :prop-game-sort-type="popularGameSet.hotGame.sortType"
            :game-category-id="popularGameSet.hotGame.category"
            block-title="hot_games"
            :block-title-capital="true"
          />
        </v-lazy>
        <!-- fishingGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <popularGameList
            :prop-game-list="fishingGameList"
            :prop-game-sort-type="popularGameSet.fishingGame.sortType"
            :game-category-id="popularGameSet.fishingGame.category"
            block-title="fishing_game"
            :block-title-capital="true"
          />
        </v-lazy>
        <!-- featuredGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <featuredGameList :prop-game-list="featuredGameList" :block-title-capital="true" />
        </v-lazy>
        <!-- chessAndCard games Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <popularGameList
            :prop-game-list="chessAndCardGameList"
            :prop-game-sort-type="popularGameSet.chessAndCardGame.sortType"
            :game-category-id="popularGameSet.chessAndCardGame.category"
            block-title="chess_and_card_game"
            :block-title-capital="false"
          />
        </v-lazy>
        <!-- newestGames Content -->
        <v-lazy
          :options="{
            threshold: 0.3
          }"
          transition="fade-transition"
        >
          <newestGameList
            :prop-game-list="newestGameList"
            :prop-game-sort-type="newestGameSet.sortType"
            :block-title-capital="true"
          />
        </v-lazy>
      </v-col>
    </v-row>
    <!-- PWA -->
    <v-lazy
      :options="{
        threshold: 0.3
      }"
      transition="fade-transition"
      :class="{ 'notch-right': hasRightNotch }"
    >
      <v-row class="text-center justify-center mb-10" no-gutters>
        <v-col cols="12" lg="9" class="pt-4 pt-sm-6 pb-0 pb-sm-2 px-lg-0 px-md-6 px-4 mw-75-v">
          <pwa-install-block />
        </v-col>
      </v-row>
    </v-lazy>
  </v-container>
</template>

<script>
  const STATION = process.env.STATION
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'Index',
    mixins: [require(`~/mixins_station/${STATION}/homeGameList`).default, orientation],
    components: {
      banner: () => import('~/components/banner'),
      recentGames: () => import(`~/components/game/recentGames`),
      liveGameList: () => import(`~/components/game/liveGameList`),
      featuredGameList: () => import(`~/components/game/featuredGameList`),
      popularGameList: () => import(`~/components/game/popularGameList`),
      pwaInstallBlock: () => import('~/components/pwa/pwaInstallBlock'),
      marquee: () => import('~/components/marquee'),
      newestGameList: () => import('~/components/game/newestGameList'),
      dailyRankings: () => import('~/components/leaderboard/dailyRankings'),
      inCompleteGame: () => import('~/components/game/inCompleteGame')
    },
    data() {
      return {
        banner: []
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      bannerSource({ banner }) {
        const source = {}
        if (banner.length) {
          banner.forEach((item) => {
            source[item.source] = {
              event: () => {
                switch (item.displayMethod) {
                  case 'new_window':
                    if (item.link !== null) {
                      this.$lineOpenWindow.open(item.link, '_blank')
                    }
                    break
                  case 'redirect':
                    if (item.link !== null) {
                      //若是網址內沒有http則直接跳轉內部網頁
                      if (!/http/.test(item.link)) {
                        self.$router.push({ path: item.link })
                      } else if (item.link.includes(location.origin)) {
                        //若是網址內有http且網址內有location.origin則直接跳轉內部網頁
                        self.$router.push({
                          path: this.localePath(item.link.split(location.origin)[1])
                        })
                      } else {
                        window.location.href = item.link
                      }
                    }
                    break
                }
              }
            }
            source[item.source][this.$i18n.locale] = {
              lg: '',
              xs: ''
            }

            item.thumbs.forEach((img) => {
              source[item.source][img.lang][img.sizeCode] = img.thumbUrl
            })
          })
        }
        return source
      },
      bannerAdapt({ bannerSource, $i18n: { locale }, $vuetify: { breakpoint } }) {
        const src = {}
        const point = breakpoint.mdAndUp ? 'lg' : 'xs'
        for (const key in bannerSource) {
          if (!src[key]) {
            src[key] = {}
          }
          src[key].src = bannerSource[key][locale][point]
          src[key].event = bannerSource[key].event
        }
        return src
      }
    },
    async created() {
      await this.getBanners()
      await this.$store.dispatch('gameProvider/fetch')
      await this.$store.dispatch('literalKeywords/fetch')
      if (this.$route.params.inviteCode !== undefined) {
        this.$store.commit('event/SET_INVITE_CODE', this.$route.params.inviteCode)
        this.$store.commit('social/SET_PROMOTE', this.$route.params.inviteCode)
      }
      if (process.client) {
        await this.init()
      }
    },
    mounted() {
      // line webview 好貼心，直接加參數就好
      if (this.$ua['_ua'].includes('Line')) {
        // ANDROID 吃 document IOS 吃 window
        let dom = window
        if (this.$device.isAndroid) {
          dom = document
        }

        const openExternalBrowser = document.location.search
          ? '&openExternalBrowser=1'
          : '?openExternalBrowser=1'

        dom.location.replace(window.location.href + openExternalBrowser)
      }
    },
    methods: {
      async getBanners() {
        if (this.maintainSystem[0].maintaining) {
          return
        }
        const bannerData = await this.$clientApi.banner.lists({ lang: this.$i18n.locale })
        if (!bannerData.errorCode) {
          this.banner = bannerData.data.list
        } else {
          this.$notify.backendError(bannerData.errorCode)
        }
      }
    },
    watch: {
      '$route.query.promote': {
        immediate: true,
        async handler(promote) {
          if (promote) {
            // 公會長推薦碼，登入時送給總部SERVER
            this.$store.commit('social/SET_PROMOTE', promote)
          }
        }
      },
      'maintainSystem.0.maintaining': {
        handler(val) {
          // 平台維護檢查
          if (val === false) {
            this.getBanners()
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .border-line {
    &::after {
      width: 99.7%;
      height: 99.4%;
    }
  }

  @media screen and (max-width: 600px) {
    .border-line {
      &::after {
        width: 99.5%;
        height: 99.7%;
      }
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
    #bannerRow {
      &.notch-right {
        padding-right: calc(16px + env(safe-area-inset-right)) !important;
      }
    }
    /* md */
    @media (min-width: 960px) and (max-width: 1263px) {
      #bannerRow {
        &.notch-right {
          padding-right: calc(24px + env(safe-area-inset-right)) !important;
        }
      }
    }
    /* lg */
    @media (min-width: 1264px) {
      #bannerRow {
        &.notch-right {
          padding-right: calc(0px + env(safe-area-inset-right)) !important;
        }
      }
    }
  }
</style>
