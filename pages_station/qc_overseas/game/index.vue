<template>
  <v-container
    fluid
    :class="['px-0', { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }]"
  >
    <!-- Games Content Start -->
    <v-lazy>
      <v-row class="text-center justify-center" no-gutters>
        <v-col v-if="isLogin" cols="12" md="10" lg="8" class="py-6 px-xl-0 px-lg-0 px-md-0 px-sm-8">
          <qcGameSetting></qcGameSetting>
        </v-col>
        <v-col v-else cols="12" class="my-2 max-width-800px">
          <v-row class="text-center justify-space-between align-center">
            <v-col cols="6">
              <v-select
                v-model="station"
                :items="stationNames"
                label="game related - station "
                shaped
                filled
                class="mb-0"
              ></v-select
            ></v-col>
            <v-col cols="6">
              <v-btn
                class="button-content--text"
                color="primary-variant-1"
                elevation="0"
                :loading="loading"
                :disabled="loading"
                @click="setStation()"
              >
                STATION update
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" lg="9" class="mb-sm-8 mb-6 px-lg-0 px-sm-3 px-4 mw-75-v">
          <gameIndex id="gameIndex" />
        </v-col>
      </v-row>
    </v-lazy>
  </v-container>
</template>

<script>
  const STATION = process.env.STATION
  const NUXT_ENV = process.env.NUXT_ENV === 'development' ? 'staging' : process.env.NUXT_ENV
  const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'Index',
    mixins: [orientation],
    components: {
      gameIndex: () => import(`~/components_station/qc_overseas/game/index.vue`),
      qcGameSetting: require(`~/components_station/qc_overseas/game/qcGameSetting.vue`).default
    },
    data() {
      return {
        stationNames: [],
        station: ''
      }
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },

      gameProviderList() {
        const stationName = this.$route.params.qcStation
        const providerList = this.$clientApi.game.gameCategoryList({ stationName })
        return providerList
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      loading({ $store }) {
        return $store.getters[`${STATION}/station/loading`]
      }
    },
    created() {
      this.init()
    },
    methods: {
      init() {
        this.stationNames = loadConfig.game.stationConstant.map((item) => {
          return item.stationName
        })
        this.station = this.$route.params.qcStation
      },
      setStation() {
        this.$store.commit(`${STATION}/station/SET_LOADING`, true)
        this.$router.push({
          path: `/${this.station}/game`,
          query: { ...this.$route.query }
        })
        setTimeout(() => {
          this.$store.commit(`${STATION}/station/SET_LOADING`, false)
        }, 5000)
      }
    },
    watch: {
      station(val) {
        if (val) {
          this.$store.dispatch(`${STATION}/station/setStation`, val)
          this.station = val
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .border-line {
    &::after {
      width: 99.7%;
      height: 99.4%;
    }
  }

  @media screen and (max-width: 600px) {
    .border-line {
      &::after {
        width: 99.5%;
        height: 99.7%;
      }
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
  .max-width-800px {
    max-width: 800px;
  }
</style>
