<template>
  <div class="game-layout w-100 d-flex flex-column">
    <div
      class="d-flex h-100-percent w-100"
      :class="{
        'flex-column': !showDrawer,
        'flex-column-reverse': !showDrawer && !isPCOrTablet,
        'flex-row': showDrawer
      }"
    >
      <v-navigation-drawer
        v-model="showDrawer"
        mini-variant
        :mini-variant-width="isIosAndHorizontal ? 56 + leftInset : 56"
        color="iframe-bar"
        ref="drawerRef"
        fixed
        :permanent="isPermanent"
      >
        <div :class="['d-flex flex-wrap justify-center', { 'notch-left': hasLeftNotch }]">
          <v-btn
            text
            class="pa-0 mx-1 mt-1"
            min-width="44px"
            min-height="44px"
            @click="closeAction"
          >
            <v-icon color="default-content"> mdi-chevron-left </v-icon>
          </v-btn>
        </div>
      </v-navigation-drawer>
      <v-toolbar
        class="pa-0 mx-0"
        v-show="appBarStatus"
        dark
        dense
        color="iframe-bar"
        ref="headerRef"
        :height="
          $device.isMobile ? ($device.isAndroid ? '56px' : $device.isIos ? '70px' : '50px') : '50px'
        "
      >
        <v-row v-if="!breakpoint.xsOnly" class="px-3 mx-0" no-gutters align="center">
          <v-col cols="4">
            <v-row no-gutters class="d-flex flex-nowrap">
              <v-btn text class="pa-2 mr-1" min-width="64px" min-height="36px" @click="closeAction">
                <v-icon color="default-content"> mdi-chevron-left </v-icon>
                <span v-if="!breakpoint.xsOnly" class="ml-2">
                  {{ $t('back_lobby') }}
                </span>
              </v-btn>
            </v-row>
          </v-col>
          <v-col cols="8" class="d-flex justify-end align-center">
            <video
              v-if="env !== 'production' && $device.isWindows"
              ref="videoElement"
              autoplay
              playsInline
              muted
              class="video-element"
            ></video>
            <v-btn
              v-if="env !== 'production' && $device.isWindows"
              text
              class="pa-0 mr-1"
              min-width="44px"
              min-height="44px"
              @click="captureAndDownload"
            >
              <v-icon color="default-content"> mdi-camera </v-icon>
            </v-btn>
            <v-btn
              v-if="breakpoint.lgAndUp && !$device.isTablet"
              text
              class="pa-0 ma-0 mr-1"
              min-width="44px"
              min-height="44px"
              @click="toggleFullscreen"
            >
              <v-icon color="default-content">mdi-fullscreen</v-icon>
            </v-btn>
          </v-col>
        </v-row>
        <v-row v-else class="px-0 mx-0" no-gutters align="center">
          <v-col class="pa-0 ma-0 d-flex flex-nowrap align-center">
            <v-btn text class="pa-0" min-width="44px" min-height="44px" @click="closeAction">
              <v-icon color="default-content"> mdi-chevron-left </v-icon>
            </v-btn>
            <v-img
              :src="logoImg"
              contain
              :height="$UIConfig.gameIframe.logoHeight"
              max-width="125"
              @error="imgError()"
            />
          </v-col>
        </v-row>
      </v-toolbar>
      <div
        :class="[
          'iframe-box',
          {
            'iframe-height-horizon': showDrawer,
            'iframe-height-vertical': !showDrawer && !$device.isMobile,
            'iframe-height-vertical-android': !showDrawer && $device.isMobile && $device.isAndroid,
            'iframe-height-vertical-ios': !showDrawer && $device.isMobile && $device.isIos,
            'notch-right': hasRightNotch
          }
        ]"
        :style="{
          'margin-left': showDrawer ? (isIosAndHorizontal ? 56 + leftInset + 'px' : '56px') : '0'
        }"
      >
        <iframe
          v-if="isNonSandboxGame"
          :src="localGameLink"
          :scrolling="singleGameHallInfo.hasScrollbar ? 'yes' : 'no'"
          id="gameIframe"
          style="border: 0"
        >
        </iframe>
        <iframe
          v-else
          :src="localGameLink"
          :scrolling="singleGameHallInfo.hasScrollbar ? 'yes' : 'no'"
          id="gameIframe"
          sandbox="allow-scripts allow-same-origin allow-orientation-lock"
          style="border: 0"
        >
        </iframe>
        <div class="demo-hint-box" :style="{ opacity: mode === 'demo' && hintBoxStatus ? 1 : 0 }">
          <div class="demo-hint-content" :class="{ 'notch-right': hasRightNotch }">
            {{ $t('play_game_playing_1') }}
            <br v-if="breakpoint.xs" />
            {{ $t('play_game_playing_2') }}
          </div>
        </div>
      </div>
    </div>
    <confirmLeaveGame
      v-if="showConfirmBackGameDialog"
      :show-confirm-back-game-dialog.sync="showConfirmBackGameDialog"
      @backToParent="backToParent"
    ></confirmLeaveGame>
    <confirmDialog v-model="showRedirectDemoModeDialog" dialog-width="400">
      <p class="mb-0 text-wrap">{{ $t('detect_not_login') }}</p>
      <p class="mb-0 text-wrap">{{ $t('free_trial_mode_has_been_opened_for_you') }}</p>
      <br />
      <p class="mb-0 text-wrap">{{ $t('free_trial_mode_noty') }}</p>
      <template v-slot:actions>
        <v-row no-gutters justify="end">
          <v-col cols="6" sm="auto" class="pr-2">
            <v-btn
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              depressed
              @click="login({ isLiveGame: false })"
            >
              {{ $t('login').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col cols="6" sm="auto" class="pl-2">
            <v-btn
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              depressed
              @click="redirectDemoMode"
            >
              {{ $t('freePlay').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </template>
    </confirmDialog>
    <confirmDialog v-model="showRedirectHomeDialog" dialog-width="400">
      <p class="mb-0 text-wrap">{{ $t('detect_not_login') }}</p>
      <p class="mb-0 text-wrap">{{ $t('login_required_notice') }}</p>
      <br />
      <p class="mb-0 text-wrap">{{ $t('game_no_trial_notice') }}</p>
      <template v-slot:actions>
        <v-row no-gutters justify="end">
          <v-col cols="6" sm="auto" class="pr-2">
            <v-btn
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              depressed
              @click="login({ isLiveGame: true })"
            >
              {{ $t('login').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col cols="6" sm="auto" class="pl-2">
            <v-btn
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              depressed
              @click="redirectHome"
            >
              {{ $t('gohome').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </template>
    </confirmDialog>
    <confirmDialog v-if="gameDemoModeDialog" v-model="gameDemoModeDialog" dialog-width="325">
      <p class="mb-0 text-wrap default-content--text">{{ $t('play_game_confirm') }}</p>
      <template v-slot:actions>
        <v-row no-gutters justify="end">
          <v-col cols="6" sm="auto" class="pr-2">
            <v-btn
              :class="['default-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              text
              @click="closeAction"
            >
              {{ $t('cancel').toUpperCase() }}
            </v-btn></v-col
          >
          <v-col cols="6" sm="auto" class="pl-2">
            <v-btn
              :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
              :color="$UIConfig.defaultBtnColor"
              depressed
              @click="closeGameDemoModeDialog"
            >
              {{ $t('sure').toUpperCase() }}
            </v-btn></v-col
          >
        </v-row>
      </template>
    </confirmDialog>
    <confirmDialog
      v-if="showDisconnectedDialog"
      v-model="showDisconnectedDialog"
      dialog-width="380"
    >
      <p class="mb-0 text-wrap default-content--text">
        {{ $t('server_891_error_login_redirect') }}
      </p>
      <template v-slot:actions>
        <v-spacer />
        <v-btn
          class="ma-0 px-4 button-content--text"
          :color="$UIConfig.defaultBtnColor"
          depressed
          @click="backToParent"
        >
          {{ $t('back_lobby').toUpperCase() }}
        </v-btn>
      </template>
    </confirmDialog>
    <bothRobotExpNoty
      v-if="showNotyBothRobotExpNotyDialogStatus.show"
      :show-noty-both-robot-exp-noty-dialog-status.sync="showNotyBothRobotExpNotyDialogStatus"
    />
    <noExpGainNoty
      v-else-if="showNotyNoExpGainNotyDialogStatus.show"
      :show-noty-no-exp-gain-noty-dialog-status.sync="showNotyNoExpGainNotyDialogStatus"
    />
    <hasRobotNoty
      v-else-if="showNotyHasRobotNotyDialogStatus.show"
      :show-noty-has-robot-noty-dialog-status.sync="showNotyHasRobotNotyDialogStatus"
    />
  </div>
</template>

<script>
  import analytics from '@/mixins/analytics'
  import orientation from '~/mixins/orientation'
  import images from '~/mixins/images'
  import screenshot from '~/mixins/screenshot'
  import pageHeadUtils from '@/utils/pageHead'
  // import navigationUtils from '@/utils/navigation'
  import debounce from 'lodash/debounce'
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml'

  const STATION = process.env.STATION
  const playGame = require(`~/mixins_station/${STATION}/playGame`).default

  export default {
    name: 'PlayGame',
    mixins: [analytics, orientation, images, screenshot, hiddenScrollHtml, playGame],
    components: {
      confirmLeaveGame: () => import('~/components/game/confirmLeaveGameDialog'),
      confirmDialog: () => import('~/components/confirmDialog'),
      bothRobotExpNoty: () => import('~/components/notifications/bothRobotExpNoty.vue'),
      noExpGainNoty: () => import('~/components/notifications/noExpGainNoty.vue'),
      hasRobotNoty: () => import('~/components/notifications/hasRobotNoty.vue')
    },
    props: {
      mode: {
        type: String,
        required: true
      },
      gameId: {
        type: String,
        required: true
      }
    },
    data() {
      return {
        showDrawer: false,
        appBarStatus: false,
        env: process.env.NUXT_ENV,
        showFullScreenStatus: false,
        showConfirmBackGameDialog: false,
        showRedirectDemoModeDialog: false,
        showRedirectHomeDialog: false,
        showNotification: false,
        deviceWidth: 1920,
        deviceHeight: 0,
        logoImg: `/${STATION}/logo.webp`,
        debounceRenderResize: debounce(this.renderResize, 100),
        loginWatch: null,
        //本頁網址
        pathUrl: '',
        hintBoxStatus: false,
        gameDemoModeDialog: false
      }
    },
    head() {
      return this.headConfig
    },
    computed: {
      headConfig() {
        return pageHeadUtils.getMetaInfo(this, { gameName: this.singleGameHallInfo.name })
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      isPlaying({ $store }) {
        return $store.getters['music/isPlaying']
      },
      singleGameHallInfo({ $store }) {
        return $store.getters['gameHall/singleGameHallInfo']
      },
      isPCOrTablet() {
        return (
          this.$device.isDesktopOrTablet &&
          (this.$device.isMacOS || this.$device.isSamsung || this.$device.isWindows)
        )
      },
      isIosAndHorizontal() {
        return this.$device.isIos && (this.orientation === 90 || this.orientation === 270)
      },
      isNonSandboxGame() {
        return Boolean(this.singleGameHallInfo.canRedirect)
      },
      customRouterPath({ $store }) {
        return $store.getters['customRouterPath']
      },
      isPermanent() {
        return (
          this.showDrawer &&
          this.deviceWidth > this.deviceHeight &&
          this.$device.isMobile &&
          (this.$device.isIos || this.$device.isAndroid)
        )
      },
      balanceTimer({ $store }) {
        return $store.getters['balanceTimer']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    async created() {
      if (this.$device.isIos || this.$device.isMacOS) {
        this.logoImg = `/${STATION}/logo.png`
      }
      this.pathUrl = this.$route.path
    },
    async mounted() {
      if (this.maintainSystem[0].maintaining) {
        return
      }
      // 必須在mounted執行不然會跑版
      this.$store.commit('menu/SET_SHOWGAMEMODESTATUS', true)

      if (this.isPlaying) {
        this.$store.commit('music/SET_IS_PLAYING', false)
      }

      // 從URL 進來才會是該參數為空
      if (this.gameLink === '') {
        await this.startGameHandle()
      } else {
        const isDemo = this.$route.params.mode === 'demo'
        if (isDemo) {
          this.gameDemoModeDialog = true
        }
      }

      // 使用 style.overflowY 直接控制頁面滾動
      // 避免使用 classList.add('overflow-y-hidden')，因為 v-dialog 組件關閉時會自動移除該 class
      // 此舉可防止在遊戲進行中關閉客服或聊天視窗時，意外移除 overflow-y-hidden 導致頁面出現滾動條
      document.documentElement.style.overflowY = 'hidden'
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          this.renderResize()
          // 因有廠商反應，會根據 iframe 父層初始高度，添加高度到他們的遊戲畫面；故等高度取得後，再設定 localGameLink，開啟廠商遊戲 iframe
          this.localGameLink = this.gameLink
        })
      })

      window.addEventListener('resize', this.debounceRenderResize)
      //倒數30秒
      let seconds = 3
      const countdown = setInterval(() => {
        seconds--
        if (seconds === 0) {
          this.showNotification = true
          clearInterval(countdown)
        }
      }, 1000)

      this.updateSafeArea()
    },
    beforeDestroy() {
      this.closeGameMode()
      window.removeEventListener('resize', this.debounceRenderResize)
      if (this.loginWatch) {
        this.loginWatch()
      }
      //若是登入，且正式遊玩，則設定餘額更新計時器，若計時器運作中，則重新計算計時器
      if (this.isLogin && this.pathUrl.includes('play')) {
        if (this.balanceTimer) {
          this.$store.dispatch('clearBalanceTimer')
        }
        this.$store.dispatch('setBalanceTimer')
      }
    },
    beforeRouteLeave(to, from, next) {
      // 在離開路由前，避免iframe再次執行遊戲連結，手動移除 localGameLink、遊戲iframe
      this.localGameLink = ''
      this.cleanupGameIframe()
      next()
    },
    methods: {
      toggleFullscreen() {
        if (!this.$screenfull.isEnabled) {
          return
        }
        this.showFullScreenStatus = !this.showFullScreenStatus
        if (this.showFullScreenStatus) {
          this.$screenfull.request()
        } else {
          this.$screenfull.exit()
        }
      },
      renderResize() {
        this.deviceWidth = Number(document.documentElement.clientWidth)
        this.deviceHeight = Number(document.documentElement.clientHeight)
        this.setSafeAreaVariables()

        // 計算基本條件
        const isHorizontal = this.deviceWidth > this.deviceHeight
        const isMobile = this.$device.isMobile
        const isTablet = this.$device.isTablet
        const isDesktop = this.$device.isDesktopOrTablet && !isTablet

        // 特殊平板條件
        const isSpecialTablet =
          this.$device.isDesktopOrTablet &&
          (this.$device.isMacOS || this.$device.isSamsung) &&
          this.$vuetify.breakpoint.mdAndDown

        // 設置 UI 狀態
        this.setUIState({
          isHorizontal,
          isMobile,
          isTablet,
          isDesktop,
          isSpecialTablet
        })
      },
      setUIState({ isHorizontal, isMobile, isTablet, isDesktop, isSpecialTablet }) {
        // 手機直向：顯示上方欄，隱藏側邊欄
        if (!isHorizontal && isMobile) {
          this.appBarStatus = true
          this.showDrawer = false
          return
        }

        // 手機橫向：隱藏上方欄，顯示側邊欄
        if (isHorizontal && isMobile) {
          this.appBarStatus = false
          this.showDrawer = true
          return
        }

        // 一般平板橫向 或 特殊平板橫向：顯示上方欄，隱藏側邊欄
        if (isHorizontal && (isTablet || isSpecialTablet)) {
          this.appBarStatus = true
          this.showDrawer = false
          return
        }

        // PC 裝置：顯示上方欄，隱藏側邊欄
        if (isDesktop) {
          this.appBarStatus = true
          this.showDrawer = false
          return
        }

        // 預設值
        this.appBarStatus = true
        this.showDrawer = false
      },
      async backToParent() {
        if (!this.isPlaying) {
          this.$store.commit('music/SET_IS_PLAYING', true)
        }

        if (this.isLogin) {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())
        }
        try {
          // 因為以下警告訊息，$router.back() 會導致無法正常發揮效果，故先暫存路徑機制
          // An iframe which has both allow - scripts and allow - same - origin for its sandbox attribute can escape its sandboxing.
          console.log('backToParent', this.customRouterPath)
          await this.$router.push(this.localePath(this.customRouterPath))
          if (this.customRouterPath !== '/') {
            this.$store.commit('SET_CUSTOM_ROUTER_PATH', '/')
          }
        } catch (error) {
          console.error('Navigation error:', error)
        }
      },
      setSafeAreaVariables() {
        if ('supports' in CSS && CSS.supports('top: env(safe-area-inset-top)')) {
          document.documentElement.style.setProperty('--sat', 'env(safe-area-inset-top)')
          document.documentElement.style.setProperty('--sab', 'env(safe-area-inset-bottom)')
          document.documentElement.style.setProperty('--sal', 'env(safe-area-inset-left)')
          document.documentElement.style.setProperty('--sar', 'env(safe-area-inset-right)')
        }
      },
      // 按下返回鍵觸發
      async closeAction() {
        if (this.showNotification && this.mode === 'play') {
          this.showConfirmBackGameDialog = true
        } else {
          await this.backToParent()
        }
      },
      goChat() {
        this.$nuxt.$emit('root:showChatRoomDialogStatus', true)
      },
      async goCustomerService() {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        this.$nuxt.$emit('root:showCustomerServiceDialogStatusEvent', true)
      },
      async imgError() {
        this.logoImg = `/${STATION}/logo.png`
      },
      async captureAndDownload() {
        const size = await this.startCapture()
        if (size.width === 0 && size.height === 0) {
          return
        }
        const canvas = this.captureFrame({ width: size.width, height: size.height })
        this.downloadAsPNG(canvas)
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      async login({ isLiveGame }) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        // 先取消舊的監聽器
        if (this.loginWatch) {
          this.loginWatch()
        }
        const showDialog = (val) => {
          isLiveGame ? (this.showRedirectHomeDialog = val) : (this.showRedirectDemoModeDialog = val)
        }
        showDialog(false)
        this.$nuxt.$emit('root:showLoginDialogStatus', {
          show: true,
          onCancelNotify: () => showDialog(true)
        })
        this.loginWatch = this.$watch('isLogin', async (newVal) => {
          if (newVal) {
            await this.startGameHandle()
            this.loginWatch() // 取消監聽
          }
        })
      },
      redirectDemoMode() {
        const startGamePath = `/demo/${this.singleGameHallInfo.id}`
        this.$router.replace(this.localePath(startGamePath))
      },
      redirectHome() {
        this.$router.push(this.localePath('/'))
      },
      updateBalance() {
        if (this.isLogin) {
          this.$wsClient.send(this.$wsPacketFactory.fatchBalance())
        }
      },
      closeGameDemoModeDialog() {
        this.gameDemoModeDialog = false
        this.hintBoxStatus = true
      },
      cleanupGameIframe() {
        const gameIframe = document.getElementById('gameIframe')
        if (gameIframe) {
          // 先暫停 iframe 內容
          gameIframe.src = 'about:blank'
          // 移除所有可能的事件監聽器
          gameIframe.onload = null
          gameIframe.onerror = null
          gameIframe.remove()
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  @import '~vuetify/src/styles/settings/_variables.scss';
  $black-opacity-50: map-get($colors, black-with-opacity-50);
  $coin-width: 44px;

  .game-layout {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 0;
    height: 100vh !important; /* 基本支援 */
    height: 100dvh !important;
    /* 為iOS添加特殊處理 */
    @supports (-webkit-touch-callout: none) {
      @media (orientation: landscape) {
        height: 100% !important;
        @supports (height: 100dvh) {
          height: 100dvh !important;
        }
      }
      @media (orientation: portrait) {
        height: 100% !important;
        @supports (height: 100svh) {
          height: 100svh !important;
        }
      }
    }

    .iframe-height-horizon {
      width: calc(100% - 56px);
      height: 100%;
    }
    .iframe-height-vertical {
      height: calc(100% - 50px);
    }
    .iframe-height-vertical-android {
      height: calc(100% - 56px);
    }
    .iframe-height-vertical-ios {
      height: calc(100% - 70px);
    }
    .v-toolbar ::v-deep {
      .v-toolbar__content {
        padding: 4px 4px !important;
      }
    }
    .video-element {
      display: none;
    }
    .tooltip-text-wrap {
      text-wrap: wrap;
      white-space: pre-line;
      word-break: break-word;
    }
    .tooltip-content-top {
      // 因 flex-column-reverse 翻轉位置，tooltip 依然會認定 iframe bar 頂部就是螢幕頂端，需加上 bottom 重新定位內容跳出的位置
      top: unset !important;
      bottom: 100% !important;
    }
    .w-max {
      width: max-content;
    }
  }
  html,
  body {
    width: 100vw;
  }
  header {
    z-index: 99;
  }
  iframe {
    width: 100%;
    height: 100%;
  }

  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
    .iframe-box {
      &.notch-right {
        background-color: #000;
      }
    }
  }
  .iframe-box {
    position: relative;
    z-index: 1;
  }

  @keyframes popUp {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.02);
    }
  }
  .demo-hint {
    &-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 12px 24px;
      background-color: $black-opacity-50;
      color: #fff;
      font-size: 48px;
      font-weight: 700;
      width: 100%;
      text-align: center;
      pointer-events: none;
      transition-duration: 0.5s;
      @media screen and(max-width:1270px) {
        font-size: 36px;
      }
      @media screen and(max-width:600px) {
        font-size: 24px;
      }
    }

    &-content {
      opacity: 0.4;
    }
  }
</style>
