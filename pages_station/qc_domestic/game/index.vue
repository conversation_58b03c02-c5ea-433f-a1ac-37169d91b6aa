<template>
  <v-container
    fluid
    :class="['px-0', { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }]"
  >
    <!-- Games Content Start -->
    <v-lazy>
      <v-row class="text-center justify-center" no-gutters>
        <v-col v-if="isLogin" cols="12" md="10" lg="8" class="py-6 px-xl-0 px-lg-0 px-md-0 px-sm-8">
          <qcGameSetting></qcGameSetting>
        </v-col>
        <v-col cols="12" lg="9" class="mb-sm-8 mb-6 px-lg-0 px-sm-3 px-4 mw-75-v">
          <gameIndex id="gameIndex" />
        </v-col>
      </v-row>
    </v-lazy>
  </v-container>
</template>

<script>
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'Index',
    mixins: [orientation],
    components: {
      gameIndex: () => import(`~/components_station/qc_domestic/game/index.vue`),
      qcGameSetting: require(`~/components_station/qc_domestic/game/qcGameSetting.vue`).default
    },
    data() {
      return {}
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },

      gameProviderList() {
        const stationName = this.$route.params.qcStation
        const providerList = this.$clientApi.game.gameCategoryList({ stationName })
        return providerList
      },
      station({ $store }) {
        return $store.getters['station']
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      }
    }
  }
</script>

<style lang="scss" scoped>
  .border-line {
    &::after {
      width: 99.7%;
      height: 99.4%;
    }
  }

  @media screen and (max-width: 600px) {
    .border-line {
      &::after {
        width: 99.5%;
        height: 99.7%;
      }
    }
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>
