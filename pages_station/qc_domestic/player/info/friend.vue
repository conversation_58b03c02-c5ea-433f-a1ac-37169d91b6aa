<template>
  <v-container class="pt-0 pt-md-16 px-0 pb-0 pb-md-15">
    <v-row
      id="friend-page"
      no-gutters
      justify="center"
      :class="$vuetify.breakpoint.smAndDown ? '' : 'pt-6'"
    >
      <v-col xl="8" lg="10" cols="12">
        <v-row no-gutters justify="start" class="pl-0 pl-md-1">
          <v-card
            color="card-fill"
            :class="['px-4 px-sm-6', { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }]"
            style="width: 100%"
            :elevation="$vuetify.breakpoint.smAndDown ? 0 : 4"
          >
            <v-card-title class="px-0">
              <span class="custom-text-noto text-h5 default-content--text font-weight-bold">{{
                $t('friend')
              }}</span>
            </v-card-title>
            <v-card-text class="px-0 pb-6 pb-md-6">
              <v-row no-gutters class="custom-text-noto text-body-1 default-content--text pb-5">
                <v-col cols="12">
                  <span class="custom-text-noto text-body-1">{{ $t('friend_page_msg1') }}</span>
                </v-col>
              </v-row>
              <!-- 搜尋玩家 -->
              <v-row no-gutters class="custom-text-noto text-body-1 default-content--text pb-3">
                <v-col cols="12" sm="6" md="4">
                  <!-- 使用v-validate 當資料量一大時會卡頓，故使用原始rules。不綁v-model也是這個原因 -->
                  <v-text-field
                    ref="inputName"
                    :label="$t('search_player')"
                    rounded
                    background-color="rgba(255, 255, 255, 0)"
                    color="rgba(255, 255, 255)"
                    outlined
                    clearable
                    maxlength="12"
                    dense
                    :rules="rules"
                    :hint="$t('put_full_nickname')"
                    persistent-hint
                    @click:clear="clearSearchEvent"
                    @keydown.enter="searchFriend"
                    @input="checkInputText($event)"
                  >
                    <template v-slot:append>
                      <span class="material-symbols-outlined cursor-pointer" @click="searchFriend">
                        search
                      </span>
                    </template>
                  </v-text-field>
                </v-col>
              </v-row>
              <v-divider class="mt-6 mb-6 mb-1" />
              <v-row no-gutters>
                <v-tabs
                  background-color="transparent"
                  color="primary"
                  class="mb-6"
                  v-model="selectType"
                >
                  <v-tab class="a-link" href="#friend_list">
                    {{ $t('friend_list_text') }}
                  </v-tab>
                  <v-tab class="a-link" href="#block_list">
                    {{ $t('block_list_text') }}
                  </v-tab>
                </v-tabs>
                <v-tabs-items v-model="selectType" class="w-100">
                  <!-- 好友列表 -->
                  <v-tab-item key="friend_list" value="friend_list">
                    <template>
                      <v-data-table
                        class="transparent"
                        :headers="$vuetify.breakpoint.smAndUp ? friendHeaders : friendHeadersMobile"
                        :items="displayFriendList"
                        :item-class="bodyClasses"
                        :page="currentPageFriend"
                        :items-per-page="itemsPageFriend"
                        :hide-default-footer="$vuetify.breakpoint.xsOnly"
                        @update:page="handlePageChangeFriend"
                        @pagination="scollToTop"
                        :footer-props="{
                          'items-per-page-text': $t('items_per_page'),
                          'items-per-page-all-text': $t('all'),
                          'items-per-page-options': [10, 20, 30, -1]
                        }"
                      >
                        <!--mobile body-->
                        <template
                          v-if="$vuetify.breakpoint.xsOnly && displayFriendList.length > 0"
                          v-slot:body="{ items, headers }"
                        >
                          <tbody class="grey-6" v-show="$vuetify.breakpoint.xsOnly" height="auto">
                            <div v-for="(item, index) in items" :key="index">
                              <tr class="v-data-table__mobile-table-row">
                                <v-menu
                                  :close-on-content-click="false"
                                  offset-y
                                  absolute
                                  z-index="3"
                                  content-class="easyPlayer-custom-border"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <div
                                      @click="setSelectPlayerInfoInFriendPage(item)"
                                      class="px-4"
                                      v-bind="attrs"
                                      v-on="on"
                                    >
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[0].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <v-badge
                                            bottom
                                            :color="item.online === true ? 'green' : 'grey-2'"
                                            dot
                                            bordered
                                            offset-x="25"
                                            offset-y="22"
                                          >
                                            <v-list-item-avatar>
                                              <v-img
                                                :src="item.thumbUrl"
                                                contain
                                                @error="errorImgHandler(item)"
                                              >
                                                <template v-slot:placeholder>
                                                  <v-row
                                                    class="fill-height ma-0"
                                                    align="center"
                                                    justify="center"
                                                  >
                                                    <v-img :src="defaultImg" contain />
                                                  </v-row>
                                                </template>
                                              </v-img>
                                            </v-list-item-avatar>
                                          </v-badge>
                                        </div>
                                      </td>
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[1].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <span
                                            style="width: 100%"
                                            class="primary--text text-subtitle-2"
                                            >{{ item.username }}</span
                                          >
                                        </div>
                                      </td>
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[2].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <div class="vip-width">
                                            <vipLevelIcon
                                              width="38"
                                              height="38"
                                              :vip-level="item.levelVip"
                                            >
                                            </vipLevelIcon>
                                          </div>
                                        </div>
                                      </td>
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[3].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <span class="text-body-2" v-if="item.online">
                                            LV {{ item.level }}
                                          </span>
                                          <span class="text-body-2" v-else>LV - </span>
                                        </div>
                                      </td>
                                    </div>
                                  </template>
                                  <playerInfoCard
                                    report
                                    :player-info.sync="selectPlayerData"
                                    class="elevation-4"
                                    :style="
                                      $vuetify.breakpoint.smAndUp ? 'width: 340px' : 'width: 300px'
                                    "
                                    tile
                                    only-coin
                                    badge-type="relation"
                                    :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                                  />
                                </v-menu>
                              </tr>
                              <v-divider class="default-content-2"></v-divider>
                            </div>
                          </tbody>
                        </template>
                        <!--mobile body-->
                        <!-- 頭像 -->
                        <template v-slot:item.avatar="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <v-badge
                                  bottom
                                  :color="item.online === true ? 'green' : 'grey-2'"
                                  dot
                                  bordered
                                  offset-x="25"
                                  offset-y="22"
                                  style="margin-right: -14px"
                                >
                                  <v-list-item-avatar>
                                    <v-img
                                      :src="item.thumbUrl"
                                      contain
                                      @error="errorImgHandler(item)"
                                    >
                                      <template v-slot:placeholder>
                                        <v-row
                                          class="fill-height ma-0"
                                          align="center"
                                          justify="center"
                                        >
                                          <v-img :src="defaultImg" contain />
                                        </v-row>
                                      </template>
                                    </v-img>
                                  </v-list-item-avatar>
                                </v-badge>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 暱稱 -->
                        <template v-slot:item.username="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span style="width: 100%" class="primary--text text-subtitle-2">{{
                                  item.username
                                }}</span>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 階級 -->
                        <template v-slot:item.vip="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <div class="vip-width">
                                  <vipLevelIcon width="38" height="38" :vip-level="item.levelVip">
                                  </vipLevelIcon>
                                </div>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 等級 -->
                        <template v-slot:item.level="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span class="text-body-2" v-if="item.online"
                                  >LV {{ item.level }}</span
                                >
                                <span class="text-body-2" v-else>LV - </span>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 操作 -->
                        <template
                          v-slot:item.operation="{ item }"
                          v-if="$vuetify.breakpoint.smAndUp"
                        >
                          <!-- 開啟玩家資訊卡 -->
                          <span>
                            <v-btn icon @click.stop="showInfoCardDialog(item)" class="first-icon">
                              <span class="material-symbols-outlined icon-style">
                                account_circle
                              </span>
                            </v-btn>
                          </span>
                          <!-- 刪除好友 -->
                          <span>
                            <v-btn
                              icon
                              @click.stop="showConfirmDeleteFriendDialogInFriendPage(item)"
                            >
                              <v-icon> mdi-account-remove </v-icon>
                            </v-btn>
                          </span>
                          <!-- 加黑名單 -->
                          <span>
                            <v-btn icon @click.stop="addBlockInFriendPage(item)">
                              <v-icon class="icon-style"> mdi-account-cancel </v-icon>
                            </v-btn>
                          </span>
                          <!-- 私訊 -->
                          <span>
                            <v-btn icon @click="createMessageInPage(item)">
                              <span class="material-symbols-outlined icon-style"> sms </span>
                            </v-btn>
                          </span>
                          <!-- 檢舉 -->
                          <span>
                            <v-btn icon @click="showReportDialog(item.username)">
                              <v-icon class="icon-style"> mdi-account-alert </v-icon>
                            </v-btn>
                          </span>
                        </template>
                        <!-- footer page-text -->
                        <template v-slot:footer.page-text="{ pageStart, pageStop, itemsLength }">
                          <span v-if="itemsLength !== 0">
                            {{ pageStart }}-{{ pageStop }}{{ $t('total_page') }} {{ itemsLength }}
                            {{ $t('quantity') }}
                          </span>
                          <span v-else>-</span>
                        </template>
                        <template
                          v-if="$vuetify.breakpoint.xsOnly"
                          v-slot:footer="{ props: { pagination } }"
                        >
                          <div class="v-data-footer">
                            <v-row no-gutters>
                              <v-col>
                                <div class="v-data-footer__select d-flex justify-start ml-3">
                                  <span> {{ $t('items_per_page') }}</span>

                                  <v-select
                                    class="py-0 mt-3 mb-3"
                                    v-model="selectFriend"
                                    hide-details
                                    height="32"
                                    @input="onSelectFriend"
                                    :items="pagePaginationitem([10, 20, 30, -1])"
                                  ></v-select>

                                  <span class="v-data-footer__pagination">
                                    {{
                                      pagination.pageStart +
                                      1 +
                                      '-' +
                                      pagination.pageStop +
                                      ' ' +
                                      $t('total_page') +
                                      ' ' +
                                      pagination.itemsLength +
                                      $t('quantity')
                                    }}
                                  </span>
                                </div>
                              </v-col>
                            </v-row>

                            <v-row no-gutters>
                              <v-col>
                                <v-btn
                                  class="v-data-footer__icons-before"
                                  icon
                                  :disabled="pagination.pageStart === 0"
                                  @click="
                                    currentPageFriend =
                                      pagination.page - 1 === 0 ? 1 : pagination.page - 1
                                  "
                                  ><v-icon dark> mdi-chevron-left </v-icon></v-btn
                                >

                                <v-btn
                                  class="v-data-footer__icons-after"
                                  icon
                                  :disabled="pagination.pageStop === pagination.itemsLength"
                                  @click="
                                    currentPageFriend =
                                      pagination.page + 1 === pagination.pageCount
                                        ? pagination.pageCount
                                        : pagination.page + 1
                                  "
                                  ><v-icon dark> mdi-chevron-right </v-icon></v-btn
                                >
                              </v-col>
                            </v-row>
                          </div>
                        </template>
                        <template v-slot:no-data>
                          <span>
                            {{ $t('no_data') }}
                          </span>
                        </template>
                      </v-data-table>
                    </template>
                  </v-tab-item>
                  <!-- 黑名單 -->
                  <v-tab-item key="block_list" value="block_list">
                    <template>
                      <v-data-table
                        class="transparent"
                        :headers="$vuetify.breakpoint.smAndUp ? friendHeaders : friendHeadersMobile"
                        :items="displayBlockList"
                        :item-class="bodyClasses"
                        :page="currentPageBlock"
                        :items-per-page="itemsPageBlock"
                        :hide-default-footer="$vuetify.breakpoint.xsOnly"
                        @update:page="handlePageChangeBlock"
                        @pagination="scollToTop"
                        :footer-props="{
                          'items-per-page-text': $t('items_per_page'),
                          'items-per-page-all-text': $t('all'),
                          'items-per-page-options': [10, 20, 30, -1]
                        }"
                      >
                        <!--mobile body-->
                        <template
                          v-if="$vuetify.breakpoint.xsOnly && displayBlockList.length > 0"
                          v-slot:body="{ items, headers }"
                        >
                          <tbody class="grey-6" v-show="$vuetify.breakpoint.xsOnly" height="auto">
                            <div v-for="(item, index) in items" :key="index">
                              <tr class="v-data-table__mobile-table-row">
                                <v-menu
                                  :close-on-content-click="false"
                                  offset-y
                                  absolute
                                  z-index="3"
                                  content-class="easyPlayer-custom-border"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <div
                                      @click="setSelectPlayerInfoInFriendPage(item)"
                                      class="px-4"
                                      v-bind="attrs"
                                      v-on="on"
                                    >
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[0].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <v-badge
                                            bottom
                                            :color="item.online === true ? 'green' : 'grey-2'"
                                            dot
                                            bordered
                                            offset-x="25"
                                            offset-y="22"
                                            style="margin-right: -14px"
                                          >
                                            <v-list-item-avatar>
                                              <v-img
                                                :src="item.thumbUrl"
                                                contain
                                                @error="errorImgHandler(item)"
                                              >
                                                <template v-slot:placeholder>
                                                  <v-row
                                                    class="fill-height ma-0"
                                                    align="center"
                                                    justify="center"
                                                  >
                                                    <v-img :src="defaultImg" contain />
                                                  </v-row>
                                                </template>
                                              </v-img>
                                            </v-list-item-avatar>
                                          </v-badge>
                                        </div>
                                      </td>
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[1].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <span
                                            style="width: 100%"
                                            class="primary--text text-subtitle-2"
                                            >{{ item.username }}</span
                                          >
                                        </div>
                                      </td>
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[2].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <div class="vip-width">
                                            <vipLevelIcon
                                              width="38"
                                              height="38"
                                              :vip-level="item.levelVip"
                                            >
                                            </vipLevelIcon>
                                          </div>
                                        </div>
                                      </td>
                                      <td class="v-data-table__mobile-row">
                                        <div class="v-data-table__mobile-row__header d-flex">
                                          <span class="content--text px-0 nowrap--text">
                                            {{ headers[3].text }}
                                          </span>
                                        </div>
                                        <div class="v-data-table__mobile-row__cell">
                                          <span class="text-body-2" v-if="item.online">
                                            LV {{ item.level }}
                                          </span>
                                          <span class="text-body-2" v-else>LV - </span>
                                        </div>
                                      </td>
                                    </div>
                                  </template>
                                  <playerInfoCard
                                    report
                                    :player-info.sync="selectPlayerData"
                                    class="elevation-4"
                                    :style="
                                      $vuetify.breakpoint.smAndUp ? 'width: 340px' : 'width: 300px'
                                    "
                                    tile
                                    only-coin
                                    badge-type="relation"
                                    :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                                  />
                                </v-menu>
                              </tr>
                              <v-divider class="default-content-2"></v-divider>
                            </div>
                          </tbody>
                        </template>
                        <!--mobile body-->
                        <!-- 頭像 -->
                        <template v-slot:item.avatar="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <v-badge
                                  bottom
                                  :color="item.online === true ? 'green' : 'grey-2'"
                                  dot
                                  bordered
                                  offset-x="25"
                                  offset-y="22"
                                  style="margin-right: -14px"
                                >
                                  <v-list-item-avatar>
                                    <v-img
                                      :src="item.thumbUrl"
                                      contain
                                      @error="errorImgHandler(item)"
                                    >
                                      <template v-slot:placeholder>
                                        <v-row
                                          class="fill-height ma-0"
                                          align="center"
                                          justify="center"
                                        >
                                          <v-img :src="defaultImg" contain />
                                        </v-row>
                                      </template>
                                    </v-img>
                                  </v-list-item-avatar>
                                </v-badge>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 暱稱 -->
                        <template v-slot:item.username="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span class="primary--text subtitle-2">{{ item.username }}</span>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 階級 -->
                        <template v-slot:item.vip="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <div class="vip-width">
                                  <vipLevelIcon width="38" height="38" :vip-level="item.levelVip">
                                  </vipLevelIcon>
                                </div>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 等級 -->
                        <template v-slot:item.level="{ item }">
                          <v-menu
                            :close-on-content-click="false"
                            offset-y
                            absolute
                            z-index="3"
                            content-class="easyPlayer-custom-border"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <div
                                @click="setSelectPlayerInfoInFriendPage(item)"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span class="text-body-2" v-if="item.online"
                                  >LV {{ item.level }}</span
                                >
                                <span class="text-body-2" v-else>LV - </span>
                              </div>
                            </template>
                            <playerInfoCard
                              report
                              :player-info.sync="selectPlayerData"
                              class="elevation-4"
                              tile
                              only-coin
                              badge-type="relation"
                              :action-bar="$vuetify.breakpoint.smAndUp ? false : true"
                            />
                          </v-menu>
                        </template>
                        <!-- 操作 -->
                        <template v-slot:item.operation="{ item }">
                          <!-- 開啟玩家資訊卡 -->
                          <span>
                            <v-btn icon @click.stop="showInfoCardDialog(item)" class="first-icon">
                              <span class="material-symbols-outlined icon-style">
                                account_circle
                              </span>
                            </v-btn>
                          </span>
                          <!-- 加為好友 -->
                          <span>
                            <v-btn icon @click.stop="addFriendInFriendPage(item)">
                              <span class="material-symbols-rounded icon-style"> person_add </span>
                            </v-btn>
                          </span>
                          <!-- 解除黑名單 -->
                          <span>
                            <v-btn
                              icon
                              @click.stop="showConfirmDeleteBlockDialogInFriendPage(item)"
                            >
                              <v-icon> mdi-account-check </v-icon>
                            </v-btn>
                          </span>
                          <!-- 檢舉  -->
                          <span>
                            <v-btn icon @click="showReportDialog(item.username)">
                              <v-icon class="icon-style"> mdi-account-alert </v-icon>
                            </v-btn>
                          </span>
                        </template>
                        <!-- footer page-text -->
                        <template v-slot:footer.page-text="{ pageStart, pageStop, itemsLength }">
                          <span v-if="itemsLength !== 0">
                            {{ pageStart }}-{{ pageStop }}{{ $t('total_page') }} {{ itemsLength }}
                            {{ $t('quantity') }}
                          </span>
                          <span v-else>-</span>
                        </template>
                        <template
                          v-if="$vuetify.breakpoint.xsOnly"
                          v-slot:footer="{ props: { pagination } }"
                        >
                          <div class="v-data-footer">
                            <v-row no-gutters>
                              <v-col>
                                <div class="v-data-footer__select d-flex justify-start ml-3">
                                  <span> {{ $t('items_per_page') }}</span>

                                  <v-select
                                    class="py-0 mt-3 mb-3"
                                    v-model="selectBlock"
                                    hide-details
                                    height="32"
                                    @input="onSelectBlock"
                                    :items="pagePaginationitem([10, 20, 30, -1])"
                                  ></v-select>

                                  <span class="v-data-footer__pagination">
                                    {{
                                      pagination.pageStart +
                                      1 +
                                      '-' +
                                      pagination.pageStop +
                                      ' ' +
                                      $t('total_page') +
                                      ' ' +
                                      pagination.itemsLength +
                                      $t('quantity')
                                    }}
                                  </span>
                                </div>
                              </v-col>
                            </v-row>

                            <v-row no-gutters>
                              <v-col>
                                <v-btn
                                  class="v-data-footer__icons-before"
                                  icon
                                  :disabled="pagination.pageStart === 0"
                                  @click="
                                    currentPageBlock =
                                      pagination.page - 1 === 0 ? 1 : pagination.page - 1
                                  "
                                  ><v-icon dark> mdi-chevron-left </v-icon></v-btn
                                >

                                <v-btn
                                  class="v-data-footer__icons-after"
                                  icon
                                  :disabled="pagination.pageStop === pagination.itemsLength"
                                  @click="
                                    currentPageBlock =
                                      pagination.page + 1 === pagination.pageCount
                                        ? pagination.pageCount
                                        : pagination.page + 1
                                  "
                                  ><v-icon dark> mdi-chevron-right </v-icon></v-btn
                                >
                              </v-col>
                            </v-row>
                          </div>
                        </template>
                        <template v-slot:no-data>
                          <span>
                            {{ $t('no_data') }}
                          </span>
                        </template>
                      </v-data-table>
                    </template>
                  </v-tab-item>
                </v-tabs-items>
              </v-row>
            </v-card-text>
          </v-card>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import menu from '~/mixins/menu.js'
  import relationship from '~/mixins/relationship.js'
  import chat from '@/mixins/chatroom/chat.js'
  import cloneDeep from 'lodash/cloneDeep'
  import scssLoader from '@/mixins/scssLoader.js'
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'friend',
    mixins: [menu, relationship, chat, scssLoader, orientation],
    components: {
      vipLevelIcon: () => import('~/components/player_info/vipLevelIcon'),
      playerInfoCard: () => import('~/components/player_info/easyPlayerInfo')
    },
    data() {
      return {
        selectType: 'friend_list',
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        friendHeaders: [
          {
            text: this.$t('avatar'),
            sortable: false,
            value: 'avatar',
            width: '60',
            align: 'center'
          },
          { text: this.$t('nickname'), sortable: false, value: 'username', width: '60%' },
          {
            text: this.$t('friend_table_rank'),
            class: 'd-flex justify-center align-center',
            sortable: false,
            value: 'vip',
            width: '60'
          },
          { text: this.$t('level'), sortable: false, value: 'level', width: '40%' },
          {
            text: this.$t('operation'),
            sortable: false,
            value: 'operation',
            width: '225'
          }
        ],
        friendHeadersMobile: [
          {
            text: this.$t('avatar'),
            sortable: false,
            value: 'avatar',
            align: 'center'
          },
          { text: this.$t('nickname'), sortable: false, value: 'username' },
          {
            text: this.$t('friend_table_rank'),
            class: 'd-flex justify-center align-center',
            sortable: false,
            value: 'vip'
          },
          { text: this.$t('level'), sortable: false, value: 'level' }
        ],
        searchFriendName: '',
        selectPlayerData: {},
        rules: [
          (val) =>
            !/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(val) || this.$t('no_special_characters_allowed')
        ],
        btnDisable: false,
        currentPageFriend: 1,
        currentPageBlock: 1,
        itemsPageFriend: 10,
        itemsPageBlock: 10,
        selectFriend: 10,
        selectBlock: 10
      }
    },
    computed: {
      isLogin() {
        return this.$store.getters['role/isLogin']
      },
      displayFriendList() {
        let friendListTmp = JSON.parse(JSON.stringify(this.friendList)).sort(function (a, b) {
          // 先比較 online
          if (Number(a.online) > Number(b.online)) {
            return -1
          } else if (Number(a.online) < Number(b.online)) {
            return 1
          } else {
            // 如果 online 相同，比較 username
            if (/^[0-9a-zA-Z]/.test(a.username) && !/^[0-9a-zA-Z]/.test(b.username)) {
              return -1
            } else if (!/^[0-9a-zA-Z]/.test(a.username) && /^[0-9a-zA-Z]/.test(b.username)) {
              return 1
            } else {
              // 如果都是英文或都是中文，依照字母順序或中文排序
              if (/^[a-zA-Z]/.test(a.username)) {
                return a.username.localeCompare(b.username, 'en')
              } else {
                return a.username.localeCompare(b.username, 'zh-Hant')
              }
            }
          }
        })
        return friendListTmp
      },
      displayBlockList() {
        let blockListTmp = JSON.parse(JSON.stringify(this.blockList)).sort(function (a, b) {
          // 先比較 online
          if (Number(a.online) > Number(b.online)) {
            return -1
          } else if (Number(a.online) < Number(b.online)) {
            return 1
          } else {
            // 如果 online 相同，比較 username
            if (/^[0-9a-zA-Z]/.test(a.username) && !/^[0-9a-zA-Z]/.test(b.username)) {
              return -1
            } else if (!/^[0-9a-zA-Z]/.test(a.username) && /^[0-9a-zA-Z]/.test(b.username)) {
              return 1
            } else {
              // 如果都是英文或都是中文，依照字母順序或中文排序
              if (/^[a-zA-Z]/.test(a.username)) {
                return a.username.localeCompare(b.username, 'en')
              } else {
                return a.username.localeCompare(b.username, 'zh-Hant')
              }
            }
          }
        })
        return blockListTmp
      },
      userName() {
        return this.$store.getters['role/userName']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      }
    },
    watch: {
      isLogin: {
        handler(val) {
          if (!val) this.$router.push({ path: this.localePath('/') })
        }
      }
    },
    mounted() {
      if (!this.isLogin) {
        this.$nextTick(() => {
          this.$notify.info(this.$t('plz_login'))
          this.$router.push({ path: this.localePath('/') })
        })
      }
    },
    methods: {
      async searchFriend() {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        if (this.btnDisable || !this.$refs.inputName.lazyValue) return
        if (this.checkTextCount(this.$refs.inputName.lazyValue) > 12) {
          this.$notify.warning(this.$t('nickname_exceeds_character_limit'))
        } else {
          //是否此人在好友名單內
          for (let i of this.friendList) {
            if (i.username.toLowerCase() === this.$refs.inputName.lazyValue.toLowerCase()) {
              this.$refs.inputName.lazyValue = i.username
            }
          }
          //是否此人在黑名單內
          for (let i of this.blockList) {
            if (i.username.toLowerCase() === this.$refs.inputName.lazyValue.toLowerCase()) {
              this.$refs.inputName.lazyValue = i.username
            }
          }
          //是否此人是自己
          if (this.$refs.inputName.lazyValue.toLowerCase() === this.userName.toLowerCase()) {
            this.$refs.inputName.lazyValue = this.userName
          }
          this.setSelectPlayerInfo(await this.getPlayerData(this.$refs.inputName.lazyValue))
          this.$refs.inputName.lazyValue = ''
          this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', true)
        }
      },
      showConfirmDeleteFriendDialogInFriendPage(item) {
        this.setSelectPlayerInfo(item)
        this.showConfirmDeleteFriendDialog(item.username)
      },
      showConfirmDeleteBlockDialogInFriendPage(item) {
        this.setSelectPlayerInfo(item)
        this.showConfirmDeleteBlockDialog(item.username)
      },
      async setSelectPlayerInfoInFriendPage(item) {
        // 先獲取空數據，以免顯示上一個用戶資訊
        this.selectPlayerData = {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        }
        this.selectPlayerData = await this.getPlayerData(item.username)
      },
      bodyClasses() {
        return 'body-classes'
      },
      errorImgHandler(item) {
        item.thumbUrl = this.defaultImg
      },
      //開啟玩家資訊卡
      async showInfoCardDialog(item) {
        this.$nuxt.$loading.start()
        let role = await this.getPlayerData(item.username)
        this.$nuxt.$loading.finish()
        this.setSelectPlayerInfo(role)
        this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', true)
      },
      // 加黑名單
      async addBlockInFriendPage(item) {
        this.setSelectPlayerInfo(item)
        this.checkAddBlock(item.username)
      },
      // 加好友
      async addFriendInFriendPage(item) {
        this.setSelectPlayerInfo(item)
        await this.checkAddFriend(item.username)
      },
      clearSearchEvent() {
        this.$refs.inputName.lazyValue = ''
      },
      //字串長度計算 中文+1 英文+2
      checkTextCount(str) {
        let count = 0
        for (const char of str) {
          if (/[a-zA-Z0-9]/.test(char)) {
            count += 1
          } else if (/[\u4e00-\u9fa5]/.test(char)) {
            count += 2
          }
        }
        return count
      },
      checkInputText(val) {
        if (/[^\u4e00-\u9fa5a-zA-Z0-9]/.test(val)) {
          this.btnDisable = true
        } else {
          this.btnDisable = false
        }
      },
      scollToTop() {
        //在 Safari 瀏覽器中，window.scrollTo()方法的behavior參數不支援'smooth'值，只支援'auto'和'instant'兩個值。
        this.$nextTick(() => {
          window.scrollTo({ top: 0, behavior: 'auto' })
        })
      },
      pagePaginationitem(itemArray) {
        let newItemArray = cloneDeep(itemArray)
        newItemArray[newItemArray.findIndex((x) => x == -1)] = this.$t('all')
        return newItemArray
      },
      onSelectFriend() {
        if (this.selectFriend === this.$t('all')) {
          this.itemsPageFriend = -1
        } else this.itemsPageFriend = this.selectFriend
      },
      onSelectBlock() {
        if (this.selectBlock === this.$t('all')) {
          this.itemsPageBlock = -1
        } else this.itemsPageBlock = this.selectBlock
      },
      handlePageChangeFriend(page) {
        this.currentPageFriend = page
      },
      handlePageChangeBlock(page) {
        this.currentPageBlock = page
      },
      createMessageInPage(player) {
        if (player.online) {
          this.createMessage(player.username)
        } else {
          this.$notify.warning(this.$t('player_offline', { player: player.username }))
        }
      }
    }
  }
</script>
<!-- 若加scoped會有一些css吃不到，故最外層使用ID包覆避免汙染 -->
<style lang="scss">
  @import '~vuetify/src/styles/settings/_variables.scss';
  $xs: map-get($display-breakpoints, 'xs-only');
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $primary: map-get($colors, 'primary');
  $grey-4: map-get($colors, 'grey-4');
  $grey-6: map-get($colors, 'grey-6');
  $dialog-fill: map-get($colors, 'dialog-fill');
  #friend-page {
    .input-bottom-text {
      margin-top: -30px !important;
    }
    .table-text-nowrap {
      text-wrap: nowrap !important;
    }
    .v-tabs-items {
      background-color: transparent !important;
    }
    .a-link {
      color: rgba(255, 255, 255, 0.6) !important;
    }
    .v-tab--active {
      color: $primary !important;
    }
    .v-data-table-header {
      background-color: $grey-4 !important;
      color: $primary !important;
      th {
        color: $primary !important;
      }
    }
    .v-small-dialog__activator__content {
      width: 100%;
    }
    .v-small-dialog__content {
      padding: 0px 0px !important;
    }
    .body-classes {
      background-color: $grey-6 !important;
    }
    .v-data-table__mobile-row__header {
      width: 50px !important;
    }
    .v-data-table__mobile-row__cell {
      display: flex;
      width: -webkit-fill-available;
      justify-content: flex-end;
      div[role='button'] {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        .vip-width {
          width: 38px;
        }
      }
    }
    .first-icon {
      margin-left: -5px;
    }
    .theme--dark.v-badge .v-badge__badge::after {
      border-color: $dialog-fill !important;
    }
    .v-data-table__empty-wrapper td {
      @media #{$xs} {
        display: flex;
        justify-content: center;
      }
    }
  }
</style>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-left {
      padding-left: calc(16px + env(safe-area-inset-left)) !important;
    }
    .notch-right {
      padding-right: calc(16px + env(safe-area-inset-right)) !important;
    }
    //sm
    @media (min-width: 600px) {
      .notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      .notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
  }
</style>
