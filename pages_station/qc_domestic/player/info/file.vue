<template>
  <v-container class="pb-0 pb-md-15 px-0 pt-0">
    <v-row no-gutters justify="center">
      <v-col xl="8" lg="10" cols="12">
        <v-row no-gutters justify="start" class="pl-0 pl-md-1"> </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import menu from '~/mixins/menu.js'

  export default {
    name: 'playerFilePage',
    mixins: [menu],
    data() {
      return {}
    },
    computed: {
      isLogin() {
        return this.$store.getters['role/isLogin']
      }
    },
    watch: {
      isLogin: {
        handler(val) {
          if (!val) this.$router.push({ path: this.localePath('/') })
        }
      }
    },
    mounted() {
      if (!this.isLogin) {
        this.$nextTick(() => {
          this.$notify.info(this.$t('plz_login'))
          this.$router.push({ path: this.localePath('/') })
        })
      }
    },
    methods: {}
  }
</script>

<style></style>
