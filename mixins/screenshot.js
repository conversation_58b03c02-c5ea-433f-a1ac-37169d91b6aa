export default {
  methods: {
    async getDisplayMedia(options) {
      if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
        return await navigator.mediaDevices.getDisplayMedia(options)
      }
      if (navigator.getDisplayMedia) {
        return await navigator.getDisplayMedia(options)
      }
      if (navigator.webkitGetDisplayMedia) {
        return await navigator.webkitGetDisplayMedia(options)
      }
      if (navigator.mozGetDisplayMedia) {
        return await navigator.mozGetDisplayMedia(options)
      }
      throw new Error('getDisplayMedia is not defined')
    },
    stopCapture() {
      if (!this.$refs.videoElement.srcObject) {
        return
      }
      let tracks = this.$refs.videoElement.srcObject.getTracks()
      tracks.forEach((track) => track.stop())
      this.$refs.videoElement.srcObject = null
    },
    async startCapture() {
      try {
        const mode =
          this.singleGameHallInfo.xinkey === undefined ? this.$t('freePlay') : this.$t('play')
        const title = '截圖資訊'
        const res = await fetch('https://icanhazip.com')
        const ip = await res.text()
        const showString =
          '截圖時間點: ' +
          this.$moment().format() +
          '<br>模式: ' +
          mode +
          '<br>玩家名稱: ' +
          this.userName +
          '<br>遊戲平台編號: ' +
          this.singleGameHallInfo.id +
          '<br>遊戲名稱: ' +
          this.singleGameHallInfo.name +
          '<br>xinkey: ' +
          this.$store.getters['gameHall/xinkey'] +
          '<br>玩家IP: ' +
          ip +
          '<br>userAgent: ' +
          this.$device.userAgent
        this.$store.commit('easyDialog/SET_COPYSTATUS', true)
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: showString
        })

        let width = screen.width * (window.devicePixelRatio || 1)
        let height = screen.height * (window.devicePixelRatio || 1)
        const displayMediaOptions = {
          // 参数的详情可以在mdn网站查找
          audio: false,
          video: {
            width,
            height,
            frameRate: 1
          },
          preferCurrentTab: true
        }

        if (this.$device.isMobile) {
          displayMediaOptions.video.mediaSource = 'screen'
        }

        await this.getDisplayMedia(displayMediaOptions)
          .then(async (stream) => {
            this.$refs.videoElement.srcObject = stream
            await this.$refs.videoElement.play()
            await this.$refs.videoElement.pause()
          })
          .catch((error) => {
            width = 0
            height = 0
            console.log(error)
            this.$store.commit('easyDialog/SET_COPYSTATUS', false)
          })
        return { width, height }
      } catch (error) {
        console.error('Error accessing screen capture:', error)
      }
    },
    captureFrame({ width, height }) {
      const canvas = document.createElement('canvas')
      canvas.width = width
      canvas.height = height

      canvas.getContext('2d').drawImage(this.$refs.videoElement, 0, 0, canvas.width, canvas.height)
      return canvas
    },
    downloadAsPNG(canvas) {
      let now = this.$moment().format()
      const dataURL = canvas.toDataURL('image/png')
      const link = document.createElement('a')
      link.href = dataURL
      link.download =
        this.singleGameHallInfo.id + '_' + this.singleGameHallInfo.name + '_' + now + '.png'
      link.click()
      this.stopCapture()
    }
  }
}
