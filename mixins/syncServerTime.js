import jwt from '~/utils/jwt.js'

export default {
  async beforeCreate() {
    if (process.client) {
      // 存入偏差值以及更新時間
      const syncServerTime = this.$localStorage.get('syncServerTime')
      const clientTime = Math.floor(Date.now() / 1000)
      const updateTimeOffset = clientTime - syncServerTime.updateTime

      // 四小時刷新一遍
      if (!syncServerTime || updateTimeOffset >= 14400) {
        await jwt
          .fetchTime()
          .then((response) => {
            const data = {}
            data.timeOffset = clientTime - response.data
            data.updateTime = response.data
            this.$localStorage.set('syncServerTime', data)
          })
          .catch((error) => {
            console.log('response', error)
          })
      }
    }
  }
}
