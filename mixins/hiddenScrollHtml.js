export default {
  data() {
    return {
      bodyElement: null,
      htmlElement: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      requestAnimationFrame(() => {
        this.bodyElement = document.body
        this.htmlElement = document.documentElement
        this.disableScroll()
      })
    })
  },
  beforeD<PERSON>roy() {
    this.enableScroll()
  },
  methods: {
    disableScroll() {
      if (!this.bodyElement || !this.htmlElement) return

      this.bodyElement.classList.add('noscroll')
      this.bodyElement.scroll = 'no'
      this.htmlElement.classList.add('overflow-y-hidden')
    },
    enableScroll() {
      if (!this.bodyElement || !this.htmlElement) return

      this.bodyElement.classList.remove('noscroll')
      this.bodyElement.scroll = 'yes'
      this.htmlElement.classList.remove('overflow-y-hidden')
    }
  }
}
