export default {
  methods: {
    saveMusicPreferenceToLocalStorage() {
      const preference = {
        volume: this.volume,
        isPlayByOrder: this.isPlayByOrder,
        currentTrack: this.currentTrack
      }
      localStorage.setItem('musicPreference', JSON.stringify(preference))
    },
    getMusicPreferenceFromLocalStroage() {
      const preference = localStorage.getItem('musicPreference')
      if (preference) {
        const { volume, isPlayByOrder, currentTrack } = JSON.parse(preference)
        this.$store.commit('music/SET_VOLUME', volume)
        this.$store.commit('music/SET_IS_PLAY_BY_ORDER', isPlayByOrder)
        //如果設定是依序播放，在下次進入頁面時則預設為第一首歌
        if (isPlayByOrder) this.$store.commit('music/SET_CURRENT_TRACK', 0)
        else this.$store.commit('music/SET_CURRENT_TRACK', currentTrack)
      }
    }
  }
}
